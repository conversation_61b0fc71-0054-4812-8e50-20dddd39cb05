#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 获取当前分支名
CURRENT_BRANCH=$(git symbolic-ref --short HEAD)

echo -e "${BLUE}当前分支: ${CURRENT_BRANCH}${NC}"

# 检查是否有未提交的更改
HAS_LOCAL_CHANGES=false
if [[ -n $(git status -s) ]]; then
    echo -e "${YELLOW}检测到未提交的更改...${NC}"
    echo -e "${BLUE}执行 git stash 保存本地修改...${NC}"

    # 执行 stash 操作保存本地修改
    git stash save "自动保存的本地修改 $(date +"%Y-%m-%d %H:%M:%S")"

    if [ $? -ne 0 ]; then
        echo -e "${RED}Stash 操作失败，请检查错误信息${NC}"
        exit 1
    else
        echo -e "${GREEN}本地修改已暂存${NC}"
        HAS_LOCAL_CHANGES=true
    fi
fi

# 检查远程分支是否存在
git fetch origin $CURRENT_BRANCH
if [ $? -ne 0 ]; then
    echo -e "${YELLOW}远程分支 $CURRENT_BRANCH 不存在${NC}"
    # 注释掉推送代码的逻辑
    # # 如果远程分支不存在，直接推送
    # git push -u origin $CURRENT_BRANCH
    # if [ $? -eq 0 ]; then
    #     echo -e "${GREEN}成功推送到新的远程分支: $CURRENT_BRANCH${NC}"
    # else
    #     echo -e "${RED}推送失败，请检查错误信息${NC}"
    #     exit 1
    # fi
    echo -e "${YELLOW}请手动创建远程分支${NC}"
    exit 0
fi

# 检查远程分支是否有新提交
echo -e "${BLUE}检查远程分支是否有新提交...${NC}"
git fetch origin $CURRENT_BRANCH

LOCAL=$(git rev-parse @)
REMOTE=$(git rev-parse @{u})
BASE=$(git merge-base @ @{u})

if [ $LOCAL = $REMOTE ]; then
    echo -e "${GREEN}本地分支与远程分支一致，无需rebase${NC}"
elif [ $LOCAL = $BASE ]; then
    echo -e "${YELLOW}远程分支有新提交，需要先拉取${NC}"

    # 执行git pull --rebase
    echo -e "${BLUE}正在执行 git pull --rebase...${NC}"
    git pull --rebase origin $CURRENT_BRANCH

    if [ $? -ne 0 ]; then
        echo -e "${RED}Rebase失败，可能存在冲突，请手动解决${NC}"
        exit 1
    else
        echo -e "${GREEN}Rebase成功${NC}"
    fi
else
    echo -e "${BLUE}本地有新提交，准备推送...${NC}"

    # 执行git pull --rebase以确保与远程同步
    echo -e "${BLUE}先执行rebase确保与远程同步...${NC}"
    git pull --rebase origin $CURRENT_BRANCH

    if [ $? -ne 0 ]; then
        echo -e "${RED}Rebase失败，可能存在冲突，请手动解决${NC}"
        exit 1
    else
        echo -e "${GREEN}Rebase成功${NC}"
    fi
fi

# 注释掉推送到远程分支的逻辑
# echo -e "${BLUE}正在推送到远程分支...${NC}"
# git push origin $CURRENT_BRANCH
#
# if [ $? -eq 0 ]; then
#     echo -e "${GREEN}成功推送到远程分支: $CURRENT_BRANCH${NC}"
# else
#     echo -e "${RED}推送失败，请检查错误信息${NC}"
#     exit 1
# fi

# 如果之前有本地修改，执行 stash pop 恢复本地修改
if [ "$HAS_LOCAL_CHANGES" = true ]; then
    echo -e "${BLUE}执行 git stash pop 恢复本地修改...${NC}"
    git stash pop

    if [ $? -ne 0 ]; then
        echo -e "${RED}恢复本地修改时发生冲突，请手动解决冲突${NC}"
        echo -e "${YELLOW}提示: 使用 git stash list 查看暂存的修改${NC}"
        echo -e "${YELLOW}提示: 使用 git stash apply 手动应用暂存的修改${NC}"
        exit 1
    else
        echo -e "${GREEN}本地修改已恢复${NC}"
    fi
fi

echo -e "${GREEN}Rebase操作已完成! 请手动推送代码${NC}"
echo -e "${YELLOW}提示: 使用 git push origin $CURRENT_BRANCH 命令推送代码${NC}"
