# Sorting-weigh 项目技术规范文档 (更新版)

## 1. 项目概述

Sorting-weigh 是一个基于 Vue 3、Vite、TypeScript、Electron 的桌面应用程序，用于商品分拣和称重管理。该系统采用前后端分离架构，前端使用 Vue 3 + TypeScript 开发，后端使用 Electron 提供桌面应用能力，并通过 IPC 通信与硬件设备（如串口设备、摄像头等）进行交互。

## 2. 技术栈

### 2.1 前端技术栈

- **框架**: Vue 3
- **语言**: TypeScript / JavaScript
- **状态管理**: Pinia (升级自 Vuex)
- **UI 组件库**: Element Plus
- **路由**: Vue Router
- **HTTP 客户端**: Axios
- **国际化**: Vue I18n
- **CSS 预处理器**: SCSS
- **构建工具**: Vite

### 2.2 桌面应用技术栈

- **框架**: Electron
- **打包工具**: Electron Builder
- **自动更新**: Electron Updater
- **日志**: Electron Log
- **硬件通信**: SerialPort

### 2.3 其他技术

- **加密**: Crypto-JS
- **AI 识别**: 自定义 AI 服务
- **键盘输入**: simple-keyboard

## 3. 项目结构规范

### 3.1 目录结构

```
sorting-weigh/
├── .electron-vite/        # Electron Vite 配置
├── .cursor/               # Cursor 编辑器配置
│   └── rules/             # 项目规范文档
├── src/                   # 源代码
│   ├── i18n/              # 国际化配置
│   ├── main/              # Electron 主进程
│   │   ├── config/        # 主进程配置
│   │   ├── recognize/     # AI 识别相关
│   │   ├── server/        # 服务相关
│   │   └── services/      # 主进程服务
│   └── renderer/          # 渲染进程 (Vue 应用)
│       ├── api/           # API 请求
│       ├── assets/        # 静态资源
│       ├── components/    # 公共组件
│       ├── i18n/          # 国际化
│       ├── public/        # 公共资源
│       ├── router/        # 路由配置
│       ├── store/         # Pinia 状态管理
│       ├── styles/        # 全局样式
│       ├── utils/         # 工具函数
│       └── views/         # 页面视图
│           ├── common/    # 公共页面
│           ├── sorting/   # 分拣相关页面
│           └── store/     # 库存相关页面
├── package.json           # 项目依赖
└── tsconfig.json          # TypeScript 配置
```

### 3.2 模块结构

每个业务模块应遵循以下结构：

```
views/
└── module/                # 模块名
    ├── index.vue          # 模块入口页面
    ├── components/        # 模块私有组件
    │   ├── List.vue       # 列表组件
    │   └── Detail.vue     # 详情组件
    └── const/             # 模块常量
        └── index.js       # 常量定义
```

## 4. 命名规范

### 4.1 文件命名规范

- **Vue 组件文件**: 使用 PascalCase (首字母大写) 或 kebab-case (短横线分隔)
  - 例: `TitleBar.vue` 或 `title-bar.vue`
- **JavaScript/TypeScript 文件**: 使用 camelCase (小驼峰)
  - 例: `utils.js`, `apiRequest.ts`
- **常量文件**: 使用 kebab-case (短横线分隔)
  - 例: `api-constants.js`
- **样式文件**: 使用 kebab-case (短横线分隔)
  - 例: `main-style.scss`

### 4.2 组件命名规范

- **组件名**: 使用 PascalCase (首字母大写)
  - 例: `ProductCard`, `OrderList`
- **基础组件**: 使用特定前缀，如 `Base`, `App`, `V` 等
  - 例: `BaseButton`, `AppHeader`
- **单例组件**: 使用 `The` 前缀
  - 例: `TheHeader`, `TheSidebar`
- **紧密耦合的组件**: 使用父组件名作为前缀
  - 例: `ProductCardTitle`, `ProductCardImage`

### 4.3 变量命名规范

- **变量**: 使用 camelCase (小驼峰)
  - 例: `userName`, `productList`
- **常量**: 使用 UPPER_SNAKE_CASE (大写下划线)
  - 例: `API_URL`, `MAX_COUNT`
- **私有变量**: 使用下划线前缀
  - 例: `_privateVar`
- **布尔值**: 使用 `is`, `has`, `can` 等前缀
  - 例: `isActive`, `hasPermission`
- **接口**: 使用 PascalCase，以 `I` 开头
  - 例: `IProduct`, `IUserInfo`
- **类型**: 使用 PascalCase，以 `T` 开头
  - 例: `TProductStatus`, `TApiResponse`

### 4.4 函数命名规范

- **函数**: 使用 camelCase (小驼峰)，动词开头
  - 例: `getData()`, `updateUser()`
- **事件处理函数**: 使用 `handle` 前缀
  - 例: `handleClick()`, `handleSubmit()`
- **Getter 函数**: 使用 `get` 前缀
  - 例: `getUserInfo()`, `getProductDetails()`
- **Setter 函数**: 使用 `set` 前缀
  - 例: `setUserInfo()`, `setProductDetails()`

### 4.5 CSS 类命名规范

- **CSS 类**: 使用 kebab-case (短横线分隔)
  - 例: `header-container`, `product-card`
- **BEM 命名法**:
  - Block: `block`
  - Element: `block__element`
  - Modifier: `block--modifier` 或 `block__element--modifier`

## 5. 编码规范

### 5.1 Vue 组件规范

#### 5.1.1 组件结构

```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup lang="ts">
// 导入语句
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';

// Props 定义
const props = defineProps({
  propName: {
    type: String,
    required: true,
    default: ''
  }
});

// Emits 定义
const emits = defineEmits(['update', 'delete']);

// 响应式数据
const count = ref(0);

// 计算属性
const doubleCount = computed(() => count.value * 2);

// 方法
function handleClick() {
  count.value++;
  emits('update', count.value);
}
</script>

<style lang="scss" scoped>
/* 样式内容 */
.component-class {
  display: flex;
}
</style>
```

#### 5.1.2 Props 规范

- 始终使用 camelCase 定义 Props
- 提供默认值和类型
- 使用对象形式定义 Props，而不是数组形式
- 必要时添加 validator 函数

```js
const props = defineProps({
  status: {
    type: String,
    required: true,
    validator: (value) => ['active', 'inactive', 'pending'].includes(value)
  },
  maxCount: {
    type: Number,
    default: 10
  }
});
```

#### 5.1.3 事件规范

- 使用 kebab-case 命名事件
- 提供事件处理函数的命名前缀为 `handle`

```js
const emits = defineEmits(['item-click', 'item-delete']);

function handleItemClick(item) {
  emits('item-click', item);
}
```

### 5.2 TypeScript 规范

#### 5.2.1 类型定义

- 使用接口 (interface) 定义对象结构
- 使用类型别名 (type) 定义联合类型或交叉类型
- 导出类型定义，便于复用
- 避免使用 `any` 类型，优先使用 `unknown`
- 使用 TypeScript 的严格模式

```ts
// 接口定义
export interface IProduct {
  id: string;
  name: string;
  price: number;
  stock: number;
  unit?: string;
}

// 类型别名
export type TProductStatus = 'in-stock' | 'out-of-stock' | 'discontinued';

// 泛型使用
export interface IApiResponse<T> {
  code: number;
  data: T;
  message: string;
}
```

#### 5.2.2 函数类型

- 明确定义函数参数和返回值类型
- 使用可选参数而不是默认参数
- 使用函数重载表达复杂的参数关系

```ts
function calculateTotal(products: IProduct[], discount?: number): number {
  // 实现
  return 0;
}
```

### 5.3 样式规范

#### 5.3.1 SCSS 使用规范

- 使用嵌套语法组织相关样式
- 使用变量定义颜色、字体等
- 避免过深的嵌套 (不超过 3 层)
- 使用 mixins 和 functions 复用样式逻辑

```scss
.product-card {
  display: flex;
  padding: 12px;
  
  .product-image {
    width: 80px;
    height: 80px;
    border-radius: 4px;
  }
  
  .product-info {
    margin-left: 10px;
    
    .product-name {
      font-weight: 500;
      line-height: 24px;
    }
  }
}
```

#### 5.3.2 样式作用域

- 优先使用 `scoped` 样式
- 全局样式放在 `styles` 目录下
- 使用 CSS 变量定义主题颜色
- 避免使用内联样式

```vue
<style lang="scss" scoped>
/* 组件样式 */
</style>
```

### 5.4 API 请求规范

- 使用统一的 API 请求函数
- 处理错误和加载状态
- 使用拦截器统一处理认证和错误
- 按模块组织 API 请求函数

```ts
import request from '@/utils/request';

export function getProductList(params) {
  return request({
    url: '/products',
    method: 'get',
    params
  });
}
```

## 6. 业务模块开发规范

### 6.1 模块划分

项目主要分为以下业务模块：

1. **公共模块 (common)**
   - 登录认证
   - 首页仪表盘
   - 系统设置

2. **分拣模块 (sorting)**
   - 订单管理
   - 商品分拣
   - 缺货管理
   - 分拣线管理
   - 客户管理

3. **库存模块 (store)**
   - 商品管理
   - 供应商管理
   - 入库管理
   - 商品识别

### 6.2 模块开发流程

1. **需求分析**
   - 明确功能需求和业务逻辑
   - 确定 UI/UX 设计

2. **组件设计**
   - 划分页面组件
   - 设计组件接口和数据流

3. **开发实现**
   - 实现组件功能
   - 实现业务逻辑
   - 实现 API 交互

4. **测试验证**
   - 单元测试
   - 功能测试
   - 性能测试

5. **代码审查**
   - 代码质量检查
   - 代码规范检查

6. **发布部署**
   - 版本管理
   - 打包发布

### 6.3 数据流管理

- 使用 Pinia 管理全局状态
- 使用 Props/Emits 管理父子组件通信
- 使用 Provide/Inject 管理深层组件通信

```js
// Pinia store 示例
import { defineStore } from 'pinia';

export const useProductStore = defineStore('product', {
  state: () => ({
    products: [],
    loading: false,
    error: null
  }),
  getters: {
    getProductById: (state) => (id) => state.products.find(p => p.id === id)
  },
  actions: {
    async fetchProducts() {
      this.loading = true;
      try {
        const response = await api.getProducts();
        this.products = response.data;
      } catch (error) {
        this.error = error;
      } finally {
        this.loading = false;
      }
    }
  }
});
```

## 7. UI 设计规范

### 7.1 颜色规范

- **主色**: `#038A78` (绿色)
- **辅助色**:
  - 成功: `#059E84` (绿色)
  - 警告: `#E6A23C` (黄色)
  - 危险: `#DB4646` (红色)
  - 信息: `#2C85D7` (蓝色)
- **中性色**:
  - 主要文本: `#1C2026`
  - 常规文本: `#505762`
  - 次要文本: `#909399`
  - 占位文本: `#C0C4CC`
  - 边框: `#E6EAF0`
  - 分割线: `#EBEEF5`
  - 背景: `#F5F7FA`

### 7.2 字体规范

- **主要字体**: "PingFang SC", "Microsoft YaHei", sans-serif
- **字号**:
  - 主标题: 20px
  - 次标题: 18px
  - 正文: 16px
  - 辅助文字: 14px
  - 小字: 12px
- **行高**:
  - 紧凑: 1.2
  - 常规: 1.5
  - 宽松: 1.8

### 7.3 间距规范

- **基础间距**: 4px
- **内边距**:
  - 小: 8px
  - 中: 12px
  - 大: 16px
- **外边距**:
  - 小: 8px
  - 中: 16px
  - 大: 24px
- **组件间距**: 16px

### 7.4 圆角规范

- **小圆角**: 4px
- **中圆角**: 8px
- **大圆角**: 12px
- **特殊圆角**: 20px (用于库存信息等特殊元素)

### 7.5 阴影规范

- **浅阴影**: `0 2px 4px rgba(0, 0, 0, 0.1)`
- **中阴影**: `0 4px 8px rgba(0, 0, 0, 0.1)`
- **深阴影**: `0 8px 16px rgba(0, 0, 0, 0.1)`

### 7.6 组件样式规范

#### 7.6.1 按钮

- **高度**: 40px
- **内边距**: 16px
- **圆角**: 8px
- **字体大小**: 16px
- **边框**: 1.5px

#### 7.6.2 输入框

- **高度**: 40px
- **内边距**: 12px
- **圆角**: 8px
- **字体大小**: 16px

#### 7.6.3 卡片

- **内边距**: 12px
- **圆角**: 12px
- **边框**: 1px solid #E6EAF0
- **背景色**: #FFFFFF

#### 7.6.4 列表项

- **高度**: 根据内容自适应
- **内边距**: 12px
- **边框**: 1px solid #E6EAF0

### 7.7 响应式设计

- 使用 flex 布局
- 使用相对单位 (%, em, rem)
- 断点设置:
  - 小屏: < 768px
  - 中屏: 768px - 1200px
  - 大屏: > 1200px

## 8. 性能优化规范

### 8.1 代码层面优化

- 使用 `computed` 缓存计算结果
- 使用 `v-memo` 缓存模板
- 使用 `v-once` 渲染静态内容
- 使用 `shallowRef` 和 `shallowReactive` 减少深层响应式
- 使用 `defineAsyncComponent` 异步加载组件

### 8.2 资源优化

- 图片使用 WebP 格式
- 使用 SVG 图标
- 懒加载图片和组件
- 压缩静态资源

### 8.3 渲染优化

- 使用 `key` 优化列表渲染
- 避免不必要的组件渲染
- 使用 `v-show` 替代频繁切换的 `v-if`
- 使用虚拟滚动处理大列表

### 8.4 网络优化

- 使用缓存策略
- 合并请求
- 使用防抖和节流

## 9. 错误处理规范

### 9.1 前端错误处理

- 使用 try/catch 捕获异步错误
- 使用全局错误处理器捕获未处理的错误
- 提供友好的错误提示

```js
// 全局错误处理
export function errorHandler(app) {
  app.config.errorHandler = (err, vm, info) => {
    console.error('Vue Error:', err);
    // 上报错误或显示错误提示
  };
}
```

### 9.2 API 错误处理

- 统一处理 HTTP 错误
- 区分业务错误和网络错误
- 提供重试机制

```js
// API 错误处理
serves.interceptors.response.use(
  (res) => {
    if (res.data.code !== 0) {
      // 处理业务错误
      ElMessage.error(res.data.message);
      return Promise.reject(res.data);
    }
    return res.data;
  },
  (err) => {
    // 处理网络错误
    if (err.message.includes("timeout")) {
      ElMessage.error("网络超时");
    } else if (err.message.includes("Network Error")) {
      ElMessage.error("网络连接错误");
    } else {
      ElMessage.error(err.message);
    }
    return Promise.reject(err);
  }
);
```

## 10. 国际化规范

### 10.1 翻译文件组织

```
i18n/
├── index.ts              # 国际化配置
└── languages/            # 语言文件
    ├── en.ts             # 英文
    └── zh-CN.ts          # 中文
```

### 10.2 翻译 Key 命名规范

- 使用模块名作为前缀
- 使用点号分隔层级
- 使用小写字母和下划线

```js
// 示例
{
  "common": {
    "confirm": "确认",
    "cancel": "取消"
  },
  "product": {
    "list": {
      "title": "商品列表",
      "empty": "暂无商品"
    }
  }
}
```

### 10.3 使用方式

```js
// 在 JS 中使用
import { i18n } from '@/i18n';
const message = i18n.global.t('product.list.title');

// 在模板中使用
<template>
  <div>{{ $t('product.list.title') }}</div>
</template>
```

## 11. 版本控制规范

### 11.1 Git 分支管理

- **master**: 主分支，用于生产环境
- **develop**: 开发分支，用于开发环境
- **feature/xxx**: 功能分支，用于开发新功能
- **bugfix/xxx**: 修复分支，用于修复 bug
- **release/xxx**: 发布分支，用于准备发布

### 11.2 提交信息规范

使用 Angular 提交规范：

```
<type>(<scope>): <subject>

<body>

<footer>
```

- **type**: 提交类型
  - feat: 新功能
  - fix: 修复 bug
  - docs: 文档更新
  - style: 代码风格更改
  - refactor: 代码重构
  - perf: 性能优化
  - test: 测试相关
  - chore: 构建过程或辅助工具的变动
- **scope**: 影响范围
- **subject**: 简短描述
- **body**: 详细描述
- **footer**: 关闭 issue 等

### 11.3 版本号规范

使用语义化版本号：`主版本号.次版本号.修订号`

- **主版本号**: 不兼容的 API 修改
- **次版本号**: 向下兼容的功能性新增
- **修订号**: 向下兼容的问题修正

## 12. 文档规范

### 12.1 注释规范

- 使用 JSDoc 风格的注释
- 为复杂函数添加参数和返回值说明
- 为组件添加用途和使用示例

```js
/**
 * 计算两个数的和
 * @param {number} a - 第一个数
 * @param {number} b - 第二个数
 * @returns {number} 两个数的和
 */
function add(a, b) {
  return a + b;
}
```

### 12.2 README 文档

- 项目简介
- 安装说明
- 使用说明
- 开发指南
- 贡献指南

### 12.3 API 文档

- 接口说明
- 参数说明
- 返回值说明
- 错误码说明
- 示例代码

## 13. 测试规范

### 13.1 单元测试

- 使用 Jest 或 Vitest 进行单元测试
- 测试文件与源文件同名，后缀为 `.spec.ts` 或 `.test.ts`
- 测试覆盖率要求：70% 以上

### 13.2 组件测试

- 使用 Vue Test Utils 进行组件测试
- 测试组件渲染、事件和生命周期

### 13.3 端到端测试

- 使用 Cypress 或 Playwright 进行端到端测试
- 测试关键业务流程

## 14. 安全规范

### 14.1 数据安全

- 敏感数据加密存储
- 使用 HTTPS 进行通信
- 避免在前端存储敏感信息

### 14.2 认证与授权

- 使用 Token 进行认证
- 实现权限控制
- 防止未授权访问

### 14.3 防御 XSS 和 CSRF

- 输入验证和转义
- 使用 CSP (Content Security Policy)
- 使用 CSRF Token

## 15. 部署规范

### 15.1 环境配置

- 开发环境 (Development)
- 测试环境 (Testing/SIT)
- 预发布环境 (Staging)
- 生产环境 (Production)

### 15.2 构建流程

- 清理旧文件
- 编译源代码
- 打包资源
- 生成版本信息
- 打包应用

### 15.3 更新机制

- 使用 Electron Updater 实现自动更新
- 提供更新日志
- 支持强制更新和可选更新

## 16. 硬件交互规范

### 16.1 串口通信

- 使用 SerialPort 库进行串口通信
- 实现连接、断开、读取、写入等功能
- 处理通信错误和超时

### 16.2 摄像头交互

- 使用 Electron 的 desktopCapturer API 获取摄像头
- 实现拍照、录像等功能
- 处理权限和错误

### 16.3 打印机交互

- 使用系统打印 API
- 支持不同打印机型号
- 提供打印预览功能

## 17. 总结

本规范文档旨在统一项目的开发标准，提高代码质量和开发效率。所有团队成员应遵循本规范进行开发，确保项目的可维护性和可扩展性。

规范不是一成不变的，随着项目的发展和技术的进步，本规范也将不断更新和完善。欢迎团队成员提出建议和意见，共同改进本规范。
