export default {
  dev: {
    // Paths    https://fos-rest.qa.saas.sh-wefresh.com.cn    http://*************:8080
    assetsSubDirectory: 'static',
    assetsPublicPath: '/',
    proxyTable: {
      '/': {
        target: 'https://freshx-gateway-saas-scp-dc-qa.canpan.net',
        changeOrigin: true,
        secure: false
      }
    },
    host: 'localhost',
    port: 8899,
    autoOpenBrowser: false,
    errorOverlay: true,
    notifyOnErrors: true,
    poll: false, 
    showEslintErrorsInOverlay: false,
    devtool: 'cheap-module-eval-source-map',
    cacheBusting: true,
    cssSourceMap: true
  },
  DllFolder: "",
};
