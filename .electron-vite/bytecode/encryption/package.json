{"name": "encryption", "version": "1.0.0", "description": "encryption", "main": "index.js", "license": "MIT", "keywords": ["napi-rs", "NAPI", "N-API", "Rust", "node-addon", "node-addon-api"], "files": ["index.d.ts", "index.js"], "napi": {"name": "index", "triples": {"defaults": true, "additional": ["x86_64-unknown-linux-musl", "aarch64-unknown-linux-gnu", "i686-pc-windows-msvc", "armv7-unknown-linux-gnueabihf", "aarch64-apple-darwin", "aarch64-linux-android", "x86_64-unknown-freebsd", "aarch64-unknown-linux-musl", "aarch64-pc-windows-msvc", "armv7-linux-androideabi"]}}, "engines": {"node": ">= 10"}, "scripts": {"artifacts": "napi artifacts", "build": "napi build --release", "install-napi": "yarn install"}, "devDependencies": {"@napi-rs/cli": "^2.11.4"}}