import { join } from "path";
import { defineConfig } from "vite";
import vuePlugin from "@vitejs/plugin-vue";
import vueJsx from "@vitejs/plugin-vue-jsx";
import { getConfig } from "./utils";
import viteIkarosTools from "./plugin/vite-ikaros-tools";
import { svgBuilder } from "./plugin/svgBuilder";

function resolve(dir: string) {
  return join(__dirname, "..", dir);
}
const config = getConfig();

const root = resolve("src/renderer");

export default defineConfig({
  mode: config && config.NODE_ENV,
  root,
  define: {
    __CONFIG__: config,
    __VUE_I18N_FULL_INSTALL__: true,
    __VUE_I18N_LEGACY_API__: true, 
    __INTLIFY_PROD_DEVTOOLS__: false
  },
  resolve: {
    alias: {
      "@renderer": root,
    },
  },
  base: "./",
  build: {
    outDir:
      config && config.target
        ? resolve("dist/web")
        : resolve("dist/electron/renderer"),
    emptyOutDir: true,
    target: "esnext",
    cssCodeSplit: false,
  },
  server: {
    // host: "0.0.0.0",
    // port: 8848,
    // proxy: {
    //   "/api": {
    //     target: "https://freshx-gateway-saas-scp-dc-qa.canpan.net",
    //     changeOrigin: true,
    //     secure: false,
    //   },
    // },
  },
  plugins: [vueJsx(), vuePlugin(), viteIkarosTools(), svgBuilder(resolve('src/renderer/assets/icons/svg/'))],
  optimizeDeps: {},
});
