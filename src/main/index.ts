'use strict'

import { app, session, ipcMain } from 'electron'
import InitWindow from './services/windowManager'
import DisableButton from './config/DisableButton'
import { initTray } from './services/trayManager'
import { updateAutoLaunch } from './auto-launch'
import { serialPortProcess } from './serial-port-process'
import { startSFHelper, ipcMainHandleFuc } from './recognize/index.js';
import { registerMacaddFun } from './getMacaddFun/index';


function onAppReady() {
  updateAutoLaunch()

  // 注册渲染层串口处理事件
  serialPortProcess();
  // 注册渲染层识别处理事件
  ipcMainHandleFuc();
  // 启动识别服务
  startSFHelper();

  // 注册获取mac地址事件
  registerMacaddFun();

  new InitWindow().initWindow()
  initTray()
  DisableButton.Disablef12()
  if (process.env.NODE_ENV === 'development') {
    const { VUEJS3_DEVTOOLS } = require("electron-devtools-vendor");
    session.defaultSession.loadExtension(VUEJS3_DEVTOOLS, {
      allowFileAccess: true,
    });
    console.log('已安装: vue-devtools', process.env)
  }

  // 优化渲染性能
  app.commandLine.appendSwitch('enable-gpu-rasterization');
  app.commandLine.appendSwitch('enable-zero-copy');
  app.commandLine.appendSwitch('disable-gpu-vsync');
  
  // 如果是Windows平台，开启硬件加速
  if (process.platform === 'win32') {
    app.commandLine.appendSwitch('enable-accelerated-compositing');
  }
}

app.whenReady().then(onAppReady)

// 由于9.x版本问题，需要加入该配置关闭跨域问题
app.commandLine.appendSwitch('disable-features', 'OutOfBlinkCors')

app.on('window-all-closed', () => {
  // 所有平台均为所有窗口关闭就退出软件
  app.quit()
})
app.on('browser-window-created', () => {
  console.log('window-created')
})

if (process.defaultApp) {
  if (process.argv.length >= 2) {
    app.removeAsDefaultProtocolClient('electron-vue-template')
    console.log('由于框架特殊性开发环境下无法使用')
  }
} else {
  app.setAsDefaultProtocolClient('electron-vue-template')
}


