const fs = require("fs");
const os = require("os");
const net = require("net");
const path = require("path");
const iconv = require("iconv-lite");
const { exec, spawn } = require("child_process");
class SFAIHelper {
  //收到socket信息中的前缀
  static PREFIX = "FEEE_";
  //收到socket信息中的后缀
  static SUFFIX = "_EEEF";
  static LOCALHOST = "127.0.0.1";
  static PORT = 5566;
  static AUTOPORT = 5533;
  //所有类型命令
  static ServerCmd = {
    INITAI: 100, //初始化AI
    WEIGHTCHANGED: 101, //重量变化
    SHOWACITVEWND: 102, //显示激活窗口
    SHOWOPTIONWND: 103, //显示选项窗口
    RECOGNIZE: 200, //识别
    CONFIRM: 201, //反馈（学习）
    ACTIVE: 202, //激活
    ISACTIVE: 203, //检测激活状态
    GETONEPIC: 204, //获取当前捕捉图片（服务接管摄像头时有效）
    SETAREA: 205, //设置标定范围（矩形）
    GETAREA: 206, //获取标定范围（矩形）
    REMOVEFEATURE: 207, //清除学习特征（单个商品或全部）
    DEACTIVATE: 208, //解绑设备
    VERSION: 209, //查询版本号
    INITCAMERA: 210, //初始化摄像头
    EXPORTFTDB: 211, //导出学习文件
    IMPORTFTDB: 212, //导入学习文件
    PRODSAMPLE: 213, //获取商品示例图片
    SETAREA2: 214, //设置标定范围（4点）
    GETAREA2: 215, //获取标定范围（4点）
    LEARN: 216, //主动学习
    CLEAN: 217, //特征清理
    CAMERALIST: 218, //获取摄像头列表
    QUIT: 219, //退出服务
    UPDATEPRODS: 220, //同步商品信息
    ALLPRODUCTS: 221, //获取所有学习过的商品
    SETPARAM: 222, //修改算法参数
    DEVICESTATUS: 223, //设备状态
    PRODSAMPLELINKS: 224, //批量获取商品图片链接
    GETAIUSEDCARMEAINFO: 225, //获取AI程序所使用的摄像头信息
    ADDAUTORUN: 226, //添加开机自启
    LANPULLDATA: 227, //局域网数据同步
  };
  constructor(appAuth, exePath) {
    //appAuth
    this.m_appAuth = appAuth;
    //exe全路径
    this.m_exePath = exePath;
    //socket对象
    this.m_socket = null;
    //socket接收信息回调函数
    this.receiveMessageListener = null;
    //socket发送信息回调函数
    this.sendMessageListener = null;
    this.m_RequestID = null;
    //监听AI称重重量变化socket对象
    this.m_AutoTriggrtSocket == null;
  }

  //业务接口:  初始化AI服务
  async initAI() {
    const accessFileRes = await this.accessFile(this.m_exePath);
    if (accessFileRes) {
      const serverIsRuningRes = await this.serverIsRuning();
      if (serverIsRuningRes) {
        //如果有进程
        this.tryConnectingSendRequest();
      } else {
        this.haveFileExeFileTryConnecting();
      }
    }
  }
  /**
   * 业务接口:  调用识别
   */
  recongnize(baseUrl) {
    this.request({
      cmd: SFAIHelper.ServerCmd.RECOGNIZE,
      img: baseUrl,
      topK: 5,
    });
  }
  /**
   * 业务接口:   点击商品时候(即确认)， 进行反馈学习
   * @param {*} code 商品PLU
   * @param {*} name 商品名
   */
  confirm(code, name) {
    //如果有requestId才去执行
    if (this.m_RequestID && this.m_RequestID != "") {
      this.request({
        cmd: SFAIHelper.ServerCmd.CONFIRM,
        requestId: this.m_RequestID,
        data: { name, code },
      });
      //清楚requestId
      this.m_RequestID = "";
    }
  }
  /**
   * 业务接口:  显示设置
   */
  showSetting() {
    this.request({
      cmd: SFAIHelper.ServerCmd.SHOWOPTIONWND,
      ShowActivateInfo: true,
      ShowCameraInfo: true,
      ShowTriggerInfo: false,
      ShowFuncInfo: false,
    });
  }
  /**
   * 业务接口: 导出学习数据
   * @param {*} sPath
   */
  exportLearnData(sPath) {
    this.request({ cmd: SFAIHelper.ServerCmd.EXPORTFTDB, outputDir: sPath });
  }
  /**
   * 业务接口: 导入学习数据
   * @param {*} sPath
   */
  importLearnData(sPath) {
    this.request({ cmd: SFAIHelper.ServerCmd.IMPORTFTDB, file: sPath });
  }
  /**
   * 业务接口: 清除学习数据
   */
  cleanLearnData(code) {
    this.request({ cmd: SFAIHelper.ServerCmd.CLEAN, code });
  }
  /**
   * 业务接口: 获取所有的商品图片
   * @param {*} oProductNames 商品名字数组
   */
  getAllProductImage(oProductNames) {
    if (Array.isArray(oProductNames) && oProductNames.length > 0) {
      const name = oProductNames.join(";");
      this.request({ cmd: SFAIHelper.ServerCmd.PRODSAMPLELINKS, name });
    }
  }
  /**
   * 业务接口: 获取AI程序所使用的摄像头信息
   */
  getUsedCamearInfo() {
    this.request({ cmd: SFAIHelper.ServerCmd.GETAIUSEDCARMEAINFO });
  }
  /**
   * 局域网同步学习数据
   */
  lanSyncLearnData(host) {
    if (host) this.request({ cmd: SFAIHelper.ServerCmd.LANPULLDATA, host });
  }
  /**
   * 业务接口: 匹配基础库,只做一次即可
   * @param {*} products 商品对象数组[{code:"1001",name: "苹果"}]
   */
  matchBasicModel(products) {
    this.request({
      cmd: SFAIHelper.ServerCmd.UPDATEPRODS,
      reset: false,
      data: products,
    });
  }
  /**
   * 图片学习
   * @param {*} code
   * @param {*} name
   * @param {*} imageFile
   */
  async learnPicture(code, name, imageFile) {
    const accessFileRes = await this.accessFile(imageFile);
    if (code == "" || imageFile == "" || accessFileRes)
      this.request({ cmd: SFAIHelper.ServerCmd.LEARN, code, name, imageFile });
  }
  /**
   * 业务接口: 通知AI重量变化（一直调用）
   * @param {*} weight 重量
   */
  notifyAIWeightChange(weight) {
    if (this.m_AutoTriggrtSocket == null) {
      return new Promise((resolve, reject) => {
        // tcp客户端
        this.m_AutoTriggrtSocket = net.createConnection(SFAIHelper.PORT);
        this.m_AutoTriggrtSocket.on("connect", () => {
          console.log("clientAutoTriggrtSocketConnect");
          this.request({ cmd: SFAIHelper.ServerCmd.WEIGHTCHANGED, weight });
          resolve(true);
        });
        this.m_AutoTriggrtSocket.on("data", async (data) => {
          console.log("clientAutoTriggrtSocketData---->" + data);
          const msg = await this.parseReceiveMessageListener(data);
          this.receiveMessageListener && this.receiveMessageListener(msg);
        });
        this.m_AutoTriggrtSocket.on("close", () => {
          console.log("clientAutoTriggrtSocketClose");
          resolve(false);
        });
        this.m_AutoTriggrtSocket.on("error", (err) => {
          console.log("clientAutoTriggrtSocketError---->" + err);
          resolve(false);
        });
      });
    }
  }
  /**
   * 主动发送信息给AI插件
   * @param {*} msg
   */
  sendMessage(msg) {
    this.request(msg);
  }
  /**
   * 关闭soket
   */
  destroy() {
    if (this.m_socket) {
      this.m_socket.destroy();
      this.m_socket = null;
    }
  }
  /**
   * 程序休眠
   * @param {休眠时间} ms
   * @returns
   */
  sleep(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
  /**
   * 发送信息
   */
  request(cmdObj) {
    if (!this.m_socket) {
      return false;
    }
    const cmd = cmdObj.cmd;
    let cmdObjStringify = "";
    if (typeof cmdObj === "object") {
      cmdObjStringify = JSON.stringify(cmdObj);
    } else {
      cmdObjStringify = cmdObj;
    }
    if (cmd == SFAIHelper.ServerCmd.WEIGHTCHANGED) {
      //重量发送 不需要接收回包
      const msg = SFAIHelper.PREFIX + cmdObjStringify + SFAIHelper.SUFFIX;
      this.m_socket.write(msg);
      console.log("socket=========>", cmdObjStringify);
      this.sendMessageListener && this.sendMessageListener(cmdObjStringify);
      return true;
    }
    const msg = SFAIHelper.PREFIX + cmdObjStringify + SFAIHelper.SUFFIX;
    console.log(msg, "msgmsgmsg");
    this.m_socket.write(msg);
    console.log("socket=========>", cmdObjStringify);
    this.sendMessageListener && this.sendMessageListener(cmdObjStringify);
    return true;
  }
  //粘包信息全局缓存变量
  socketReceiveStickyWrapMsg = "";
  /**
   * 解析socket收到以FEEE_开头以_EEEF结尾之间的数据
   * @param {*} msg
   */
  async parseReceiveMessage(msg) {
    const regex = /FEEE_([\s\S]*?)_EEEF/;
    const match = msg.match(regex);
    const value = match ? match[1] : null;
    let rStr = value;
    if (value) {
      const vobj = JSON.parse(value);
      if (vobj.cmd == SFAIHelper.ServerCmd.RECOGNIZE) {
        //如果是识别命令返回的信息就记录一下requestId
        this.m_RequestID = vobj.requestId ? vobj.requestId : "";
        //将本地临时识别到的商品图片
        const imgPath = await this.getRecongnizeImgBase64();
        vobj.imgPath = imgPath;
        rStr = JSON.stringify(vobj);
      }
    }
    return rStr;
  }
  /**
   * 解析socket收到的最原始数据
   * @param {*} msg
   * @returns
   */
  async parseReceiveMessageListener(msg) {
    if (msg) {
      // 将 Uint8Array 转换为 ArrayBuffer
      const arrayBuffer = msg.buffer;
      // 将 ArrayBuffer 解码为文字
      const decodedText = new TextDecoder().decode(arrayBuffer).trim();
      //判断信息是否以FEEE_开头以_EEEF结尾
      const sb_regex = /^FEEE_[\s\S]*?_EEEF$/;
      if (sb_regex.test(decodedText)) {
        const str = await this.parseReceiveMessage(decodedText);
        return str;
      } else {
        this.socketReceiveStickyWrapMsg += this.socketReceiveStickyWrapMsg;
        if (sb_regex.test(this.socketReceiveStickyWrapMsg)) {
          const str = await this.parseReceiveMessage(
            this.socketReceiveStickyWrapMsg
          );
          return str;
        } else {
          return null;
        }
      }
    } else {
      return msg;
    }
  }
  /**
   * 设置接收信息监听回调函数
   * @param {*} fun
   */
  setReceiveMessageListener(fun) {
    // if (this.m_socket)
    this.receiveMessageListener = fun;
  }
  /**
   * 设置发送信息监听回调函数
   * @param {*} fun
   */
  setSendMessageListener(fun) {
    // if (this.m_socket)
    this.sendMessageListener = fun;
  }
  /**
   * 启动socket服务
   */
  createSocketServer() {
    return new Promise((resolve, reject) => {
      // tcp客户端
      this.m_socket = net.createConnection(SFAIHelper.PORT);
      this.m_socket.on("connect", () => {
        console.log("clientSocketConnect");
        resolve(true);
      });
      this.m_socket.on("data", async (data) => {
        console.log("clientSocketData---->" + data);
        const msg = await this.parseReceiveMessageListener(data);
        this.receiveMessageListener && this.receiveMessageListener(msg);
      });
      this.m_socket.on("close", () => {
        console.log("clientSocketClose");
        resolve(false);
      });
      this.m_socket.on("error", (err) => {
        console.log("clientSocketError---->" + err);
        resolve(false);
      });
    });
  }
  /**
   * 尝试连接socket服务
   */
  async tryConnecting(times) {
    let success = false;
    for (let i = 0; i < times; i++) {
      const createSocketServerPromise = await this.createSocketServer();
      success = createSocketServerPromise;
      if (success) {
        return success;
      }
      await this.sleep(1000);
    }
    return success;
  }
  /**
   * 判断有没有插件，有就尝试连接socket服务发送信息
   */
  async haveFileExeFileTryConnecting() {
    //执行AI插件
    const satrtExEFileRes = await this.satrtExEFile(this.m_exePath);
    await this.sleep(3000);
    if (satrtExEFileRes) {
      //启动一个sokect客户端和AI插件通讯
      const connect = await this.tryConnecting(10);
      if (connect) {
        //withPic:是否返回识别识别时候的图片
        //appAuth:AppAuth 防止激活码授权问题  联系食方给分配
        //enableBaseLib:根据自己的需求开启
        this.request({
          cmd: SFAIHelper.ServerCmd.INITAI,
          withPic: false,
          appAuth: this.m_appAuth,
          enableBaseLib: false,
          cameraId: -1,
        });
      } else {
        const connect = await this.tryConnecting(10);
        if (connect) {
          //withPic:是否返回识别识别时候的图片
          //appAuth:AppAuth 防止激活码授权问题  联系食方给分配
          //enableBaseLib:根据自己的需求开启
          this.request({
            cmd: SFAIHelper.ServerCmd.INITAI,
            withPic: false,
            appAuth: this.m_appAuth,
            enableBaseLib: false,
            cameraId: -1,
          });
        }
      }
    }
  }
  /**
   * 尝试连接socket服务成功就发送信息
   */
  async tryConnectingSendRequest() {
    //启动一个sokect客户端和AI插件通讯
    const connect = await this.tryConnecting(10);
    if (connect) {
      //withPic:是否返回识别识别时候的图片
      //appAuth:AppAuth 防止激活码授权问题  联系食方给分配
      //enableBaseLib:根据自己的需求开启
      this.request({
        cmd: SFAIHelper.ServerCmd.INITAI,
        withPic: false,
        appAuth: this.m_appAuth,
        enableBaseLib: false,
        cameraId: -1,
      });
    } else {
      const connect = await this.tryConnecting(10);
      if (connect) {
        //withPic:是否返回识别识别时候的图片
        //appAuth:AppAuth 防止激活码授权问题  联系食方给分配
        //enableBaseLib:根据自己的需求开启
        this.request({
          cmd: SFAIHelper.ServerCmd.INITAI,
          withPic: false,
          appAuth: this.m_appAuth,
          enableBaseLib: false,
          cameraId: -1,
        });
      } else {
        this.destroy();
        const killProcessRes = await this.killProcess();
        if (killProcessRes) {
          this.haveFileExeFileTryConnecting();
        }
      }
    }
  }
  /**
   * 判断进程是否正在运行
   * @param {*} name 进程名字
   * @returns
   */
  serverIsRuning() {
    return new Promise((resolve, reject) => {
      //'tasklist /FI "IMAGENAME eq SFScalePlugin.exe"';
      const cmd = spawn("tasklist", ["/FI", "IMAGENAME eq SFScalePlugin.exe"]);
      let result = "";
      cmd.stdout.on("data", (data) => {
        const str = iconv.decode(Buffer.from(data, "binary"), "GBK");
        result += str;
      });
      cmd.stderr.on("data", (data) => {
        console.error(`serverIsRuning stderr: ${data}`);
        resolve(false);
      });
      cmd.on("close", (code) => {
        if (code === 0) {
          console.log(`serverIsRuning child process exited with code ${code}`);
          console.log(result);
          resolve(result.includes("SFScalePlugin.exe"));
        } else {
          console.error(`命令执行失败，退出码：${code}`);
          resolve(false);
        }
      });
    });
  }
  /**
   * 杀死进程
   */
  killProcess() {
    return new Promise((resolve, reject) => {
      // 定义要执行的命令
      //'taskkill /F /IM SFScalePlugin.exe'; // Windows系统上使用taskkill命令结束指定名称的进程
      const cmd = spawn("taskkill", ["/F", "/IM", "SFScalePlugin.exe"]);
      let result = "";
      cmd.stdout.on("data", (data) => {
        const str = iconv.decode(Buffer.from(data, "binary"), "GBK");
        result += str;
      });
      cmd.stderr.on("data", (data) => {
        console.error(`killProcess stderr: ${data}`);
        resolve(false);
      });
      cmd.on("close", (code) => {
        if (code === 0) {
          console.log(`killProcess child process exited with code ${code}`);
          console.log(result);
          resolve(result.includes("SFScalePlugin.exe"));
        } else {
          console.error(`命令执行失败，退出码：${code}`);
          resolve(false);
        }
      });
    });
  }
  /**
   * 判断文件是否存在
   * @returns
   */
  accessFile(filePath) {
    return new Promise((resolve, reject) => {
      fs.access(filePath, (err) => {
        if (!err) {
          console.log(`${filePath} 文件存在！`);
          resolve(true);
        } else {
          console.log(`${filePath} 文件不存在或无法访问！`);
          resolve(false);
        }
      });
    });
  }
  /**
   * 执行一个exe文件
   */
  satrtExEFile(exePath) {
    return new Promise((resolve, reject) => {
      // 启动子进程
      const child = exec(exePath);
      // 监听子进程的输出
      child.stdout.on("data", (data) => {
        console.log(`子进程输出：${data}`);
        resolve(true);
      });
      // 监听子进程的错误输出
      child.stderr.on("data", (data) => {
        console.error(`子进程错误输出：${data}`);
        resolve(false);
      });
      // 监听子进程退出事件
      child.on("close", (code) => {
        console.log(`子进程退出，退出码 ${code}`);
      });
    });
  }
  /**
   * 获取系统临时目录返回识别图片base64地址
   * @returns
   */
  getRecongnizeImgBase64() {
    return new Promise((resolve, reject) => {
      // 获取图片文件路径C:\Users\<USER>\AppData\Local\Temp\sf_rec_tmp.jpg
      const imagePath = path.join(os.tmpdir(), "sf_rec_tmp.jpg");
      // 读取图片文件
      fs.readFile(imagePath, (err, data) => {
        if (err) {
          resolve("");
        } else {
          // 将图片数据转换成base64编码
          const base64Data = Buffer.from(data).toString("base64");
          resolve(`data:image/png;base64,${base64Data}`);
        }
      });
    });
  }
}
// module.exports = SFAIHelper;

export default SFAIHelper;
