import SFAIHelper from "./SFAIHelper";
const http = require("http");
class SFAIHttpHelper extends SFAIHelper {
  static HTTPLOCALHOST = "127.0.0.1";
  static HTTPLOCALPORT = "5567";
  constructor(appAuth, exePath) {
    super(appAuth, exePath);
    //HTTP请求实体
    this.m_client = null;
  }
  //业务接口:  初始化AI服务
  async initAI() {
    const accessFileRes = await this.accessFile(this.m_exePath);
    if (accessFileRes) {
      const serverIsRuningRes = await this.serverIsRuning();
      if (serverIsRuningRes) {
        //如果有进程
        this.tryConnectingSendRequest();
      } else {
        this.haveFileExeFileTryConnecting();
      }
    }
  }
  /**
   * 关闭http
   */
  destroy() {
    if (this.m_client) {
      this.m_client.destroy();
      this.m_client = null;
    }
  }
  /**
   * 请求数据
   * @param {*} cmd 发送的JSON对象
   */
  async request(cmd) {
    const serviceUrl = "/cmd/";
    const parameterData = cmd;
    const parameterDataStr =
      typeof parameterData == "string"
        ? parameterData
        : JSON.stringify(parameterData);
    this.sendMessageListener && this.sendMessageListener(parameterDataStr);
    try {
      const sResponse = await this.post(parameterData, serviceUrl);
      const vobj = JSON.parse(sResponse);
      this.receiveMessageListener && this.receiveMessageListener(sResponse);
      if (vobj.cmd == SFAIHelper.ServerCmd.RECOGNIZE) {
        //如果是识别命令返回的信息就记录一下requestId
        this.m_RequestID = vobj.requestId ? vobj.requestId : "";
      }
      return true;
    } catch (error) {
      return false;
    }
  }
  /**
   * 发送一个post请求
   * @param {*} parameterData
   * @param {*} serviceUrl
   * @param {*} ContentType
   * @param {*} Accept
   */
  post(parameterData, serviceUrl, ContentType = "application/json") {

    const encoder = new TextEncoder();
    const jsonStr = JSON.stringify(parameterData);
    const uint8Array = encoder.encode(jsonStr);

    return new Promise((resolve, reject) => {
      const postData = JSON.stringify(parameterData);
      const options = {
        protocol: "http:",
        host: SFAIHttpHelper.HTTPLOCALHOST,
        port: SFAIHttpHelper.HTTPLOCALPORT,
        path: serviceUrl,
        method: "POST",
        headers: {
          "Content-Type": ContentType,
          "Content-Length":uint8Array.length,
        },
        body: uint8Array
      };
      // 创建请求
      const app = http.request(options, (res) => {
        this.m_client = res;
        let data = "";
        // 当接收到一部分响应数据时，将其追加到data变量中
        this.m_client.on("data", (chunk) => {
          data += chunk;
        });
        // 当响应结束时，打印完整的数据
        this.m_client.on("end", () => {
          resolve(data);
        });
      });
      // 错误处理
      app.on("error", (e) => {
        reject(e);
      });
      // 写入数据到请求主体
      app.write(postData);
      // 结束请求
      app.end();
    });
  }
  /**
   * 判断有没有插件，有就尝试Http发送信息
   */
  async haveFileExeFileTryConnecting() {
    //执行AI插件
    const satrtExEFileRes = await this.satrtExEFile(this.m_exePath);
    await this.sleep(3000);
    if (satrtExEFileRes) {
      for (let i = 0; i < 10; i++) {
        const isSuccess = this.request({
          cmd: SFAIHelper.ServerCmd.INITAI,
          withPic: false,
          cameraId: -1,
          appAuth: this.m_appAuth,
          enableBaseLib: false,
        });
        if (isSuccess) break;
        await this.sleep(1000);
      }
    }
  }
  /**
   * 尝试发送HTTP信息不成功就杀死进程再次重试发送
   */
  async tryConnectingSendRequest() {
    let isSuccess = false;
    for (let i = 0; i < 10; i++) {
      isSuccess = this.request({
        cmd: SFAIHelper.ServerCmd.INITAI,
        withPic: false,
        cameraId: -1,
        appAuth: this.m_appAuth,
        enableBaseLib: false,
      });
      if (isSuccess) break;
      await this.sleep(1000);
    }
    if (!isSuccess) {
      this.destroy();
      const killProcessRes = await this.killProcess();
      if (killProcessRes) {
        this.haveFileExeFileTryConnecting();
      }
    }
  }
}

export default SFAIHttpHelper;
