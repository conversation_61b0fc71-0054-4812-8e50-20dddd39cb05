const path = require("path");
const { BrowserWindow, ipcMain, dialog } = require("electron");
import SFAIHttpHelper from "./sf/SFAIHttpHelper.js";

const appAuth =
  "dEhVZGFERTlDUUsxUXJpbHdTUGRDZzBIUklTQWc2RUo6R2M4NDZ5ZkdZeXVNYm13azFaUHBsbG5qbjVHZVlyNzc= ";

const prodBinPath =
  process.env.NODE_ENV === "production"
    ? path.join(process.resourcesPath, "bin/SFScalePlugin.exe")
    : path.join(__dirname, "../../../bin/SFScalePlugin.exe");

let helper = null;

export const startSFHelper = () => {
  const sfAIHttpHelper = new SFAIHttpHelper(appAuth, prodBinPath);
  helper = sfAIHttpHelper;

  //监听接收信息
  sfAIHttpHelper.setReceiveMessageListener((msg) => {
    const mainWindow = BrowserWindow.getFocusedWindow();
    if (mainWindow) {
      const webContents = mainWindow.webContents;
      webContents.send("electron-receive-message", msg);
    }
  });

  //监听发送信息
  sfAIHttpHelper.setSendMessageListener((msg) => {
    const mainWindow = BrowserWindow.getFocusedWindow();
    if (mainWindow) {
      const webContents = mainWindow.webContents;
      webContents.send("electron-send-message", msg);
    }
  });
  return { sfAIHttpHelper };
};

export const ipcMainHandleFuc = () => {
  //主进程中监听页面按钮触发的事件调用对应的业务接口
  const ipcMainHandleMap = new Map([
    //初始化启动AI服务
    [
      "start-ai-service",
      () => {
        helper.initAI();
      },
    ],
    //调用识别
    [
      "recongnize",
      (baseUrl) => {
        helper.recongnize(baseUrl);
      },
    ],
    //点击商品时候(即确认)， 进行反馈学习
    [
      "confirm",
      (...args) => {
        helper.confirm(args[0], args[1]);
      },
    ],
    //显示设置
    [
      "show-setting",
      (...args) => {
        helper.showSetting();
      },
    ],
    //导出学习数据
    [
      "export-learn-data",
      (...args) => {
        dialog
          .showOpenDialog({
            properties: ["openDirectory"],
            multiSelections: false,
          })
          .then((res) => {
            if (res.filePaths) helper.exportLearnData(res.filePaths[0]);
          })
          .catch((err) => {
            console.error(err);
          });
      },
    ],
    //导入学习数据
    [
      "import-learn-data",
      (...args) => {
        dialog
          .showOpenDialog({
            properties: ["openFile"],
            multiSelections: false,
          })
          .then((res) => {
            if (res.filePaths) helper.importLearnData(res.filePaths[0]);
          })
          .catch((err) => {
            console.error(err);
          });
      },
    ],
    //清除学习数据
    [
      "clean-learn-data",
      (...args) => {
        helper.cleanLearnData();
      },
    ],
    //获取所有的商品图片
    [
      "get-all-product-image",
      (...args) => {
        const oProductNames = ["苹果", "橘子", "香蕉", "菠萝"];
        helper.getAllProductImage(oProductNames);
      },
    ],
    //获取AI程序所使用的摄像头信息
    [
      "get-used-camear-info",
      (...args) => {
        helper.getUsedCamearInfo();
      },
    ],
    //局域网同步学习数据
    [
      "lan-sync-learn-data",
      (...args) => {
        //demo先写死开发按实际场景来
        const host = "*************";
        helper.lanSyncLearnData(host);
      },
    ],
    //匹配基础库
    [
      "match-basic-model",
      (...args) => {
        helper.matchBasicModel();
      },
    ],
    //学习图片
    [
      "learn-picture",
      (params) => {
        //demo先写死开发按实际场景来
        // const pathStr = "./sf/assets/img/100-小猫.jpg";
        // const picturePath = path.join(__dirname, pathStr);
        // const basename = path.basename(pathStr, path.extname(pathStr));
        // const code = basename.split("-")[0]; //PLU
        // const name = basename.split("-")[1]; //商品名
        const { code, name, imageFile } = params;
        helper.learnPicture(code, name, imageFile);
      },
    ],
    //主动向AI插件发信息
    [
      "send-message",
      (...args) => {
        helper.sendMessage(args[0]);
      },
    ],
    //选择SDK路径
    [
      "select-sdk-path",
      (...args) => {
        dialog
          .showOpenDialog({
            properties: ["openFile"],
            filters: [{ name: "Applications", extensions: ["exe"] }],
            multiSelections: false,
          })
          .then((res) => {
            if (res.filePaths && res.filePaths.length > 0) {
              const exeFullPath = res.filePaths[0];
              startSFHelper(exeFullPath);
              const mainWindow = BrowserWindow.getFocusedWindow();
              if (mainWindow) {
                const webContents = mainWindow.webContents;
                webContents.send("selectSdkPath", exeFullPath);
              }
            }
          })
          .catch((err) => {
            console.error(err);
          });
      },
    ],
  ]);
  //循环map监听渲染进程传过来的按钮事件
  ipcMainHandleMap.forEach((item, channel) => {
    ipcMain.handle(channel, (event, ...args) => {
      item(...args);
    });
  });
};
