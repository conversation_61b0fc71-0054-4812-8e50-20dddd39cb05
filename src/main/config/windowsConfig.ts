import { IsUseSysTitle } from "./const";
import { BrowserWindowConstructorOptions } from "electron";

export const mainWindowConfig: BrowserWindowConstructorOptions = {
  useContentSize: true,
  height: 770,
  width: 1366,
  minWidth: 200,
  alwaysOnTop: false,
  fullscreen: false,
  resizable: true,//可否缩放
  show: false,
  frame: IsUseSysTitle,
  movable: false,
  webPreferences: {
    contextIsolation: false,
    nodeIntegration: true,
    webSecurity: false,
    // 如果是开发模式可以使用devTools
    devTools: process.env.NODE_ENV === "development",
    // 在macos中启用橡皮动画
    scrollBounce: process.platform === "darwin",
  },
};

export const otherWindowConfig: BrowserWindowConstructorOptions = {
  height: 770,
  width: 1366,
  useContentSize: true,
  autoHideMenuBar: true,
  minWidth: 842,
  movable: false,
  frame: IsUseSysTitle,
  alwaysOnTop: false,
  fullscreen: false,
  resizable: true,//可否缩放
  show: false,
  webPreferences: {
    contextIsolation: false,
    nodeIntegration: true,
    webSecurity: false,
    // 如果是开发模式可以使用devTools
    devTools: process.env.NODE_ENV === "development",
    // 在macos中启用橡皮动画
    scrollBounce: process.platform === "darwin",
  },
};

export const printWinds: BrowserWindowConstructorOptions = {
  height: 200,
  width: 500,
  useContentSize: true,
  autoHideMenuBar: true,
  minWidth: 500,
  frame: IsUseSysTitle,
  show: false,
  transparent: true, 
  webPreferences: {
    contextIsolation: false,
    nodeIntegration: true,
    webSecurity: false,
    // 如果是开发模式可以使用devTools
    devTools: process.env.NODE_ENV === "development",
    // 在macos中启用橡皮动画
    scrollBounce: process.platform === "darwin",
  },
};
