
import { networkInterfaces } from 'os';
import { ipcMain } from 'electron'

function getMacAddress(): string {
  const nets = networkInterfaces();
  const results: { [key: string]: string[] } = {};

  for (const name of Object.keys(nets)) {
    for (const net of nets[name] || []) {
      // Skip over non-IPv4 and internal (i.e. 127.0.0.1) addresses
      if (net.family === 'IPv4' && !net.internal) {
        if (!results[name]) {
          results[name] = [];
        }
        results[name].push(net.mac);
      }
    }
  }

  // Get the first non-empty MAC address
  for (const name of Object.keys(results)) {
    if (results[name].length > 0) {
      return results[name][0];
    }
  }

  return '';
}


export function registerMacaddFun() {

  // Register IPC handlers
  ipcMain.handle('get-mac-address', () => {
    return getMacAddress();
  });
}