import { ipcMain } from 'electron'
const { SerialPort } = require('serialport');

// 协议解析函数（强化版）
function parseScaleData(packet) {
    // 基础校验
    if (!Buffer.isBuffer(packet) || packet.length !== 11) {
        throw new Error(`无效数据包长度: ${packet?.length || 0}字节`);
    }

    // 1. 校验起始字符
    if (packet[0] !== 0x57) { // 'W'
        throw new Error(`起始字符错误: 0x${packet[0].toString(16)}`);
    }

    // 2. 校验标志位
    const FLAGS = new Set([0x55, 0x53, 0x4F, 0x49, 0x44, 0x43]);
    if (!FLAGS.has(packet[1])) {
        throw new Error(`未知标志位: 0x${packet[1].toString(16)}`);
    }

    // 3. 校验符号位
    if (packet[2] !== 0x2B && packet[2] !== 0x2D) { // '+'/'-'
        throw new Error(`无效符号: 0x${packet[2].toString(16)}`);
    }

    // 4. 校验重量值（6位ASCII数字）
    const weightBytes = packet.subarray(3, 9);
    if (!/^[0-9]{6}$/.test(weightBytes.toString('ascii'))) {
        throw new Error(`重量值非数字: ${weightBytes.toString('ascii')}`);
    }

    // 5. 校验结束符
    if (packet[9] !== 0x0D || packet[10] !== 0x0A) { // CRLF
        throw new Error(`无效结束符: 0x${packet[9].toString(16)} 0x${packet[10].toString(16)}`);
    }

    // 6. 结构转换
    return {
        flag: String.fromCharCode(packet[1]),
        sign: packet[2] === 0x2B ? '+' : '-',
        weight_gram: parseInt(weightBytes.toString('ascii'), 10),
        weight_kg: ((packet[2] === 0x2B ? '+' : '-') === '-' ? -1 : 1) * (parseInt(weightBytes.toString('ascii'), 10)) / 100,
        valid: packet[1] === 0x53 // 仅当标志为'S'时视为有效
    };
}

const protocol = {
    TIANMEI: {
        fun: (rawBuffer, event) => {
            while (rawBuffer.length >= 11) {
                // 1. 查找起始符 'W' (0x57) 的位置
                const wIndex = rawBuffer.indexOf(0x57);

                // 情况1：未找到起始符 -> 清空缓冲区
                if (wIndex === -1) {
                    rawBuffer = Buffer.alloc(0);
                    break;
                }

                // 情况2：起始符不在缓冲区开头 -> 丢弃前面的无效数据
                if (wIndex > 0) {
                    rawBuffer = rawBuffer.subarray(wIndex);
                }

                // 2. 检查剩余数据是否足够 11 字节
                if (rawBuffer.length < 11) break;

                // 3. 提取数据包（从起始符开始的 11 字节）
                const packet = rawBuffer.subarray(0, 11);
                rawBuffer = rawBuffer.subarray(11); // 移除已处理数据
                try {
                    // 基础校验
                    if (!Buffer.isBuffer(packet) || packet.length !== 11) {
                        throw new Error(`无效数据包长度: ${packet?.length || 0}字节`);
                    }

                    // 1. 校验起始字符
                    if (packet[0] !== 0x57) { // 'W'
                        throw new Error(`起始字符错误: 0x${packet[0].toString(16)}`);
                    }

                    // 2. 校验标志位
                    const FLAGS = new Set([0x55, 0x53, 0x4F, 0x49, 0x44, 0x43]);
                    if (!FLAGS.has(packet[1])) {
                        throw new Error(`未知标志位: 0x${packet[1].toString(16)}`);
                    }

                    // 3. 校验符号位
                    if (packet[2] !== 0x2B && packet[2] !== 0x2D) { // '+'/'-'
                        throw new Error(`无效符号: 0x${packet[2].toString(16)}`);
                    }

                    // 4. 校验重量值（6位ASCII数字）
                    const weightBytes = packet.subarray(3, 9);
                    if (!/^[0-9]{6}$/.test(weightBytes.toString('ascii'))) {
                        throw new Error(`重量值非数字: ${weightBytes.toString('ascii')}`);
                    }

                    // 5. 校验结束符
                    if (packet[9] !== 0x0D || packet[10] !== 0x0A) { // CRLF
                        throw new Error(`无效结束符: 0x${packet[9].toString(16)} 0x${packet[10].toString(16)}`);
                    }

                    // 6. 结构转换
                    const parsed = {
                        flag: String.fromCharCode(packet[1]),
                        sign: packet[2] === 0x2B ? '+' : '-',
                        weight_gram: parseInt(weightBytes.toString('ascii'), 10),
                        weight_kg: ((packet[2] === 0x2B ? '+' : '-') === '-' ? -1 : 1) * (parseInt(weightBytes.toString('ascii'), 10)) / 100,
                        valid: packet[1] === 0x53 // 仅当标志为'S'时视为有效
                    };
                    event.sender.send('weight-update', parsed);
                } catch (err) {
                    console.error('解析失败:', err.message);
                }
            }
        }
    },
    TOP: {
        fun: (rawBuffer, event) => {
            console.log(rawBuffer.length, "rawBuffer.length")
            while (rawBuffer.length >= 2) {
                // 1. 查找起始符 0x01 0x02
                const startIndex = rawBuffer.findIndex((v, i) =>
                    i <= rawBuffer.length - 2 &&
                    v === 0x01 &&
                    rawBuffer[i + 1] === 0x02
                );

                if (startIndex === -1) {
                    rawBuffer = Buffer.alloc(0);
                    break;
                }

                // 2. 截取有效数据段
                rawBuffer = rawBuffer.subarray(startIndex);
                // 3. 直接按协议长度提取（无需动态查找结束符）
                const isSmallWeight = rawBuffer.length >= 16;
                const packetLength = isSmallWeight ? 16 : 17;
                if (rawBuffer.length < packetLength) break;
                const packet = rawBuffer.subarray(0, packetLength);
                rawBuffer = rawBuffer.subarray(packetLength);


                if ([16, 17].includes(packet.length)) {
                    const isLargeMode = packet.length === 17;
                    const weightStr = packet.toString('ascii', 4, isLargeMode ? 11 : 10);
                    const unit = packet.toString('ascii', isLargeMode ? 11 : 10, isLargeMode ? 13 : 12);
                    const statusChar = String.fromCharCode(packet[2]);
                    const sign = packet[3] === 0x20 ? '+' : '-';
                    const statusByte = packet[packet.length - 1];
                    const status = {
                        isZero: (statusByte & 0x10) !== 0,
                        isTare: (statusByte & 0x20) !== 0,
                        isOverload: (statusByte & 0x40) !== 0
                    };
                    event.sender.send('weight-update', {
                        weight: parseFloat(weightStr),
                        unit,
                        status: statusChar,
                        sign,
                        statusFlags: status
                    });
                }
            }
        }
    }
}


export function serialPortProcess() {
    let port = null;
    let rawBuffer = Buffer.alloc(0);

    // 监听渲染进程的串口操作请求
    ipcMain.handle('serial-connect', async (event, { path }) => {
        if (port) {
            port.removeAllListeners(); // 清理所有监听器
            port.close(); // 同步关闭
            port = null;
        };

        port = new SerialPort({
            path,
            baudRate: 9600,
            dataBits: 8,
            parity: 'none',
            stopBits: 1,
            autoOpen: false // 手动控制打开流程
        })

        port.on('data', (chunk) => {
            rawBuffer = Buffer.concat([rawBuffer, chunk]);
            while (rawBuffer.length >= 2) {
                // 1. 查找起始符 0x01 0x02
                const startIndex = rawBuffer.findIndex((v, i) =>
                    i <= rawBuffer.length - 2 &&
                    v === 0x01 &&
                    rawBuffer[i + 1] === 0x02
                );

                if (startIndex === -1) {
                    rawBuffer = Buffer.alloc(0);
                    break;
                }

                // 2. 截取有效数据段
                rawBuffer = rawBuffer.subarray(startIndex);
                // 3. 直接按协议长度提取（无需动态查找结束符）
                const isSmallWeight = rawBuffer.length >= 16;
                const packetLength = isSmallWeight ? 16 : 17;
                if (rawBuffer.length < packetLength) break;
                const packet = rawBuffer.subarray(0, packetLength);
                rawBuffer = rawBuffer.subarray(packetLength);

                if ([16, 17].includes(packet.length)) {
                    const isLargeMode = packet.length === 17;
                    const weightStr = packet.toString('ascii', 4, isLargeMode ? 11 : 10);
                    const unit = packet.toString('ascii', isLargeMode ? 11 : 10, isLargeMode ? 13 : 12);
                    const statusChar = String.fromCharCode(packet[2]);
                    const sign = packet[3] === 0x20 ? '+' : '-';
                    const statusByte = packet[packet.length - 1];
                    const status = {
                        isZero: (statusByte & 0x10) !== 0,
                        isTare: (statusByte & 0x20) !== 0,
                        isOverload: (statusByte & 0x40) !== 0
                    };
                    event.sender.send('weight-update', {
                        weight: parseFloat(weightStr),
                        unit,
                        status: statusChar,
                        sign,
                        statusFlags: status
                    });
                }
            }
        });

        // 显式打开串口
        return new Promise((resolve, reject) => {
            port.open(err => {
                if (err) {
                    port = null;
                    event.sender.send('weight-update', { data: `打开失败: ${err.message}`, type: '打开失败' })
                    reject(`打开失败: ${err.message}`);
                } else {
                    event.sender.send('weight-update', { data: `打开成功`, type: '打开成功' })
                    resolve({ status: 'connected' });
                }
            });
        });

    });

    ipcMain.handle('serial-close', (event) => {
        if (port) {
            port.removeAllListeners(); // 清理所有监听器
            port.close(); // 同步关闭
            port = null;
            return { success: true };
        };
        return { success: false };
    });
}