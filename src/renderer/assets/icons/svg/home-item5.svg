<svg width="276" height="280" viewBox="0 0 276 280" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Group 427318915">
<g id="Rectangle 1159" filter="url(#filter0_d_414_2215)">
<rect x="78.3729" y="8.07892" width="182.161" height="182.161" rx="60.113" fill="url(#paint0_linear_414_2215)"/>
</g>
<g id="Rectangle 1160" filter="url(#filter1_bdiii_414_2215)">
<path d="M60.5336 84.6654C60.5336 51.4659 87.4471 24.5524 120.647 24.5524H182.581C215.781 24.5524 242.694 51.4659 242.694 84.6654V146.6C242.694 179.8 215.781 206.713 182.581 206.713H120.647C87.4471 206.713 60.5336 179.8 60.5336 146.6V84.6654Z" fill="#F2D0FF" fill-opacity="0.2" shape-rendering="crispEdges"/>
<path d="M60.989 84.6654C60.989 51.7174 87.6986 25.0078 120.647 25.0078H182.581C215.529 25.0078 242.239 51.7174 242.239 84.6654V146.6C242.239 179.548 215.529 206.258 182.581 206.258H120.647C87.6986 206.258 60.989 179.548 60.989 146.6V84.6654Z" stroke="url(#paint1_linear_414_2215)" stroke-opacity="0.2" stroke-width="0.910803" shape-rendering="crispEdges"/>
</g>
<g id="Group 427318914" filter="url(#filter2_d_414_2215)">
<path id="Vector" d="M167.135 97.0753C166.957 96.0753 165.999 95.0684 165.007 94.8414L164.272 94.6715C162.528 94.145 160.979 92.9793 159.996 91.2773C159.005 89.563 158.779 87.6159 159.208 85.8332L159.425 85.1354C159.723 84.1646 159.336 82.8276 158.565 82.1701C158.565 82.1701 157.865 81.5797 155.896 80.4405C153.927 79.3026 153.072 78.9919 153.072 78.9919C152.116 78.6466 150.768 78.9753 150.077 79.7218L149.559 80.2788C148.233 81.5323 146.446 82.2997 144.482 82.2997C142.509 82.2997 140.716 81.5267 139.389 80.2649L138.889 79.7245C138.201 78.9794 136.851 78.6493 135.896 78.9948C135.896 78.9948 135.041 79.3039 133.067 80.4431C131.101 81.5824 130.406 82.1701 130.406 82.1701C129.631 82.8276 129.243 84.159 129.544 85.1339L129.76 85.8387C130.183 87.62 129.955 89.563 128.971 91.276C127.985 92.985 126.421 94.1535 124.67 94.6742L123.952 94.8386C122.964 95.0643 122.005 96.0725 121.826 97.0711C121.826 97.0711 121.665 97.9694 121.665 100.249C121.665 102.529 121.826 103.424 121.826 103.424C122.006 104.426 122.964 105.432 123.952 105.663L124.654 105.819C126.412 106.343 127.98 107.515 128.971 109.231C129.959 110.944 130.184 112.891 129.757 114.674L129.544 115.37C129.243 116.34 129.631 117.677 130.409 118.334C130.409 118.334 131.104 118.925 133.074 120.064C135.045 121.206 135.897 121.513 135.897 121.513C136.854 121.858 138.2 121.526 138.891 120.785L139.385 120.253C140.712 118.986 142.506 118.215 144.482 118.215C146.457 118.215 148.254 118.992 149.587 120.253H149.588L150.077 120.785C150.768 121.526 152.11 121.858 153.069 121.513C153.069 121.513 153.921 121.201 155.899 120.064C157.865 118.925 158.562 118.334 158.562 118.334C159.336 117.677 159.72 116.345 159.425 115.37L159.208 114.652C158.79 112.875 159.018 110.936 159.996 109.231C160.991 107.513 162.556 106.345 164.311 105.819V105.816L165.013 105.655C166.004 105.429 166.963 104.424 167.141 103.421C167.141 103.421 167.302 102.524 167.302 100.242C167.297 97.9722 167.135 97.0753 167.135 97.0753ZM144.481 107.605C140.432 107.605 137.148 104.312 137.148 100.251C137.148 96.1951 140.432 92.9055 144.481 92.9055C148.53 92.9055 151.81 96.1965 151.81 100.256C151.81 104.318 148.53 107.605 144.481 107.605Z" fill="white"/>
<path id="Vector_2" d="M197.554 81.9139C197.421 81.1677 196.706 80.4163 195.966 80.2469L195.417 80.1201C194.116 79.7272 192.96 78.8574 192.227 77.5873C191.487 76.308 191.318 74.855 191.638 73.5247L191.801 73.004C192.023 72.2796 191.734 71.2819 191.158 70.7912C191.158 70.7912 190.637 70.3506 189.167 69.5005C187.697 68.6514 187.059 68.4195 187.059 68.4195C186.346 68.1618 185.34 68.4071 184.825 68.9642L184.438 69.3798C183.449 70.3152 182.115 70.8879 180.65 70.8879C179.177 70.8879 177.839 70.3111 176.849 69.3695L176.476 68.9662C175.962 68.4102 174.955 68.1638 174.242 68.4217C174.242 68.4217 173.604 68.6523 172.131 69.5025C170.664 70.3526 170.145 70.7912 170.145 70.7912C169.568 71.2818 169.278 72.2754 169.502 73.0029L169.663 73.5288C169.979 74.8581 169.809 76.308 169.075 77.5863C168.339 78.8616 167.172 79.7336 165.866 80.1221L165.329 80.2448C164.592 80.4132 163.876 81.1656 163.743 81.9108C163.743 81.9108 163.623 82.5811 163.623 84.2825C163.623 85.9839 163.743 86.6511 163.743 86.6511C163.877 87.3994 164.592 88.1498 165.329 88.3223L165.853 88.4388C167.165 88.8294 168.335 89.7046 169.075 90.985C169.812 92.2634 169.98 93.7163 169.661 95.0467L169.502 95.5663C169.278 96.2896 169.568 97.2874 170.147 97.7779C170.147 97.7779 170.666 98.2186 172.137 99.0687C173.607 99.921 174.243 100.15 174.243 100.15C174.958 100.407 175.961 100.16 176.477 99.6071L176.846 99.2101C177.836 98.2644 179.175 97.6887 180.65 97.6887C182.124 97.6887 183.464 98.2686 184.459 99.2101H184.46L184.825 99.6071C185.34 100.16 186.342 100.407 187.057 100.15C187.057 100.15 187.693 99.9169 189.169 99.0687C190.637 98.2186 191.156 97.7779 191.156 97.7779C191.734 97.2874 192.021 96.2938 191.801 95.5663L191.638 95.03C191.327 93.7038 191.497 92.2571 192.227 90.985C192.969 89.7025 194.137 88.8315 195.446 88.4388V88.4366L195.97 88.3161C196.71 88.1476 197.425 87.3974 197.558 86.649C197.558 86.649 197.679 85.9797 197.679 84.2773C197.675 82.5832 197.554 81.9139 197.554 81.9139ZM180.649 89.7711C177.627 89.7711 175.177 87.3142 175.177 84.2835C175.177 81.2571 177.627 78.8023 180.649 78.8023C183.67 78.8023 186.118 81.2581 186.118 84.2877C186.118 87.3184 183.67 89.7711 180.649 89.7711Z" fill="white"/>
<path id="Vector_3" d="M215.731 53.6626H104.131C100.618 53.6626 97.931 56.3493 97.931 59.8626V138.396C97.931 141.909 100.618 144.596 104.131 144.596H151.664V152.863H137.198C134.924 152.863 133.064 154.723 133.064 156.996C133.064 159.269 134.924 161.129 137.198 161.129H182.664C184.938 161.129 186.798 159.269 186.798 156.996C186.798 154.723 184.938 152.863 182.664 152.863H168.198V144.596H215.731C219.244 144.596 221.931 141.909 221.931 138.396V59.8626C221.931 56.3493 219.244 53.6626 215.731 53.6626ZM213.664 125.996H106.198V63.9959H213.664V125.996Z" fill="white"/>
</g>
</g>
<defs>
<filter id="filter0_d_414_2215" x="71.0865" y="0.792493" width="204.02" height="204.02" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="3.64321" dy="3.64321"/>
<feGaussianBlur stdDeviation="5.46482"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.00392157 0 0 0 0 0.266667 0 0 0 0 0 0 0 0 0.04 0"/>
<feBlend mode="multiply" in2="BackgroundImageFix" result="effect1_dropShadow_414_2215"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_414_2215" result="shape"/>
</filter>
<filter id="filter1_bdiii_414_2215" x="0.420562" y="6.33637" width="273.241" height="273.241" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="4.55402"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_414_2215"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-14.5729" dy="27.3241"/>
<feGaussianBlur stdDeviation="22.7701"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.970833 0 0 0 0 0.791667 0 0 0 0 1 0 0 0 0.2 0"/>
<feBlend mode="multiply" in2="effect1_backgroundBlur_414_2215" result="effect2_dropShadow_414_2215"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_414_2215" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2.73241" dy="2.73241"/>
<feGaussianBlur stdDeviation="1.3662"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_414_2215"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4.55402" dy="-4.55402"/>
<feGaussianBlur stdDeviation="11.385"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.97375 0 0 0 0 0.8125 0 0 0 0 1 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_414_2215" result="effect4_innerShadow_414_2215"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1.82161" dy="-1.82161"/>
<feGaussianBlur stdDeviation="1.3662"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="effect4_innerShadow_414_2215" result="effect5_innerShadow_414_2215"/>
</filter>
<filter id="filter2_d_414_2215" x="79.9497" y="53.6626" width="159.963" height="143.429" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="17.9813"/>
<feGaussianBlur stdDeviation="8.99065"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.517647 0 0 0 0 0.172549 0 0 0 0 0.760784 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_414_2215"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_414_2215" result="shape"/>
</filter>
<linearGradient id="paint0_linear_414_2215" x1="236.572" y1="190.24" x2="91.2479" y2="-2.02058" gradientUnits="userSpaceOnUse">
<stop stop-color="#842CC2"/>
<stop offset="0.546875" stop-color="#D367FF"/>
<stop offset="1" stop-color="#D56DFB"/>
</linearGradient>
<linearGradient id="paint1_linear_414_2215" x1="211.467" y1="50.5814" x2="81.2435" y2="216.728" gradientUnits="userSpaceOnUse">
<stop stop-color="#842CC2"/>
<stop offset="1" stop-color="#D56DFB" stop-opacity="0.52"/>
</linearGradient>
</defs>
</svg>
