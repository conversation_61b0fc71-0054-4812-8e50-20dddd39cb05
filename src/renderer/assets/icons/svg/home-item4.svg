<svg width="303" height="307" viewBox="0 0 303 307" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Group 427318906">
<g id="Rectangle 1160" filter="url(#filter0_d_168_21552)">
<rect x="86.5336" y="8" width="200" height="200" rx="66" fill="url(#paint0_linear_168_21552)"/>
</g>
<g id="Rectangle 1161" filter="url(#filter1_bdiii_168_21552)">
<path d="M66.5336 92.0868C66.5336 55.636 96.0828 26.0868 132.534 26.0868H200.534C236.984 26.0868 266.534 55.636 266.534 92.0868V160.087C266.534 196.538 236.984 226.087 200.534 226.087H132.534C96.0828 226.087 66.5336 196.538 66.5336 160.087V92.0868Z" fill="#FFEFAF" fill-opacity="0.35" shape-rendering="crispEdges"/>
<path d="M67.0336 92.0868C67.0336 55.9121 96.3589 26.5868 132.534 26.5868H200.534C236.708 26.5868 266.034 55.9121 266.034 92.0868V160.087C266.034 196.261 236.708 225.587 200.534 225.587H132.534C96.3589 225.587 67.0336 196.261 67.0336 160.087V92.0868Z" stroke="url(#paint1_linear_168_21552)" stroke-opacity="0.2" shape-rendering="crispEdges"/>
</g>
<g id="Group 237483" filter="url(#filter2_d_168_21552)">
<path id="Vector 6 (Stroke)" fill-rule="evenodd" clip-rule="evenodd" d="M134.165 101.202C134.165 99.1718 135.811 97.5262 137.841 97.5262H200.696C202.726 97.5262 204.371 99.1718 204.371 101.202C204.371 103.232 202.726 104.877 200.696 104.877H137.841C135.811 104.877 134.165 103.232 134.165 101.202Z" fill="white"/>
<path id="Vector 7 (Stroke)" fill-rule="evenodd" clip-rule="evenodd" d="M134.165 120.537C134.165 118.507 135.811 116.862 137.841 116.862H200.696C202.726 116.862 204.371 118.507 204.371 120.537C204.371 122.567 202.726 124.213 200.696 124.213H137.841C135.811 124.213 134.165 122.567 134.165 120.537Z" fill="white"/>
<path id="Exclude" fill-rule="evenodd" clip-rule="evenodd" d="M209.734 189.845C224.535 189.845 236.534 177.846 236.534 163.045C236.534 148.244 224.535 136.245 209.734 136.245C194.933 136.245 182.934 148.244 182.934 163.045C182.934 177.846 194.933 189.845 209.734 189.845ZM209.734 166.759C207.704 166.759 206.058 165.114 206.058 163.084V147.253C206.058 145.223 207.704 143.578 209.734 143.578C211.764 143.578 213.41 145.223 213.41 147.253V159.369H223.316C225.346 159.369 226.992 161.015 226.992 163.045C226.992 165.075 225.346 166.72 223.316 166.72H210.464C210.408 166.72 210.352 166.719 210.296 166.717C210.113 166.745 209.925 166.759 209.734 166.759Z" fill="white"/>
<g id="Mask group">
<mask id="mask0_168_21552" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="116" y="62" width="107" height="111">
<path id="Union" fill-rule="evenodd" clip-rule="evenodd" d="M142.756 66.4642C142.756 64.1804 144.607 62.3291 146.891 62.3291C149.174 62.3291 151.026 64.1804 151.026 66.4642V66.9641H187.511V66.4642C187.511 64.1804 189.362 62.3291 191.646 62.3291C193.93 62.3291 195.781 64.1804 195.781 66.4642V66.9641H202.978H210.976C217.066 66.9641 222.003 71.9009 222.003 77.9909V88.224V161.407C222.003 167.497 217.066 172.434 210.976 172.434H127.56C121.47 172.434 116.534 167.497 116.534 161.407V88.224V77.9909C116.534 71.901 121.47 66.9641 127.56 66.9641H135.558H142.756V66.4642ZM124.532 88.224H214.005V152.779C214.005 158.869 209.068 163.806 202.978 163.806H135.558C129.469 163.806 124.532 158.869 124.532 152.779V88.224Z" fill="white"/>
</mask>
<g mask="url(#mask0_168_21552)">
<path id="Ellipse 49" d="M216.515 53.9979C86.466 47.4235 114.818 62.0104 110.298 176.65C151.985 180.24 192.369 188.425 186.947 181.871C182.718 176.758 180.177 170.198 180.177 163.045C180.177 146.721 193.41 133.488 209.733 133.488C215.627 133.488 221.119 135.214 225.729 138.187C233.888 143.447 233.361 57.9013 216.515 53.9979Z" fill="white"/>
</g>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_168_21552" x="78.5336" y="0" width="224" height="224" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4" dy="4"/>
<feGaussianBlur stdDeviation="6"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.00392157 0 0 0 0 0.266667 0 0 0 0 0 0 0 0 0.04 0"/>
<feBlend mode="multiply" in2="BackgroundImageFix" result="effect1_dropShadow_168_21552"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_168_21552" result="shape"/>
</filter>
<filter id="filter1_bdiii_168_21552" x="0.533569" y="6.08679" width="300" height="300" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="5"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_168_21552"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-16" dy="30"/>
<feGaussianBlur stdDeviation="25"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.937255 0 0 0 0 0.686275 0 0 0 0.2 0"/>
<feBlend mode="multiply" in2="effect1_backgroundBlur_168_21552" result="effect2_dropShadow_168_21552"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_168_21552" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-3" dy="3"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_168_21552"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="5" dy="-5"/>
<feGaussianBlur stdDeviation="12.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.9915 0 0 0 0 1 0 0 0 0 0.575 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_168_21552" result="effect4_innerShadow_168_21552"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="-2"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="effect4_innerShadow_168_21552" result="effect5_innerShadow_168_21552"/>
</filter>
<filter id="filter2_d_168_21552" x="96.5336" y="62.3291" width="160" height="167.515" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="20"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.708333 0 0 0 0 0.349208 0 0 0 0 0.0177083 0 0 0 0.7 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_168_21552"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_168_21552" result="shape"/>
</filter>
<linearGradient id="paint0_linear_168_21552" x1="103.087" y1="15.9481" x2="254.415" y2="208" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFD11F"/>
<stop offset="0.505208" stop-color="#FFCE22"/>
<stop offset="1" stop-color="#FEA401"/>
</linearGradient>
<linearGradient id="paint1_linear_168_21552" x1="232.248" y1="54.6648" x2="89.2716" y2="237.083" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFD11F"/>
<stop offset="1" stop-color="#FCF676" stop-opacity="0.52"/>
</linearGradient>
</defs>
</svg>
