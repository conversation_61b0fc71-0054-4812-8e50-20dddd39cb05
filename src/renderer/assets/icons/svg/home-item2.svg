<svg width="303" height="307" viewBox="0 0 303 307" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Group 427318904">
<g id="Rectangle 1157" filter="url(#filter0_d_168_21523)">
<rect x="86.8073" y="8" width="200" height="200" rx="66" fill="url(#paint0_linear_168_21523)"/>
</g>
<g id="Rectangle 1159" filter="url(#filter1_bdiii_168_21523)">
<path d="M66.2599 92.0868C66.2599 55.636 95.8091 26.0868 132.26 26.0868H200.26C236.711 26.0868 266.26 55.636 266.26 92.0868V160.087C266.26 196.538 236.711 226.087 200.26 226.087H132.26C95.8091 226.087 66.2599 196.538 66.2599 160.087V92.0868Z" fill="#B8E3FC" fill-opacity="0.5" shape-rendering="crispEdges"/>
<path d="M66.7599 92.0868C66.7599 55.9122 96.0852 26.5868 132.26 26.5868H200.26C236.435 26.5868 265.76 55.9122 265.76 92.0868V160.087C265.76 196.261 236.435 225.587 200.26 225.587H132.26C96.0852 225.587 66.7599 196.261 66.7599 160.087V92.0868Z" stroke="url(#paint1_linear_168_21523)" stroke-opacity="0.2" shape-rendering="crispEdges"/>
</g>
<g id="Group 237478" filter="url(#filter2_d_168_21523)">
<path id="Stroke" fill-rule="evenodd" clip-rule="evenodd" d="M232.246 164C237.491 164 241.743 159.005 241.743 152.843V148.659C241.743 132.543 227.261 119.372 203.758 119.372C180.254 119.372 165.772 132.543 165.772 148.659V152.843C165.772 159.005 170.024 164 175.269 164H232.246Z" fill="white"/>
<path id="Subtract" fill-rule="evenodd" clip-rule="evenodd" d="M203.758 143.576C203.758 151.073 198.585 157.151 192.203 157.151H166.506C166.034 155.826 165.772 154.37 165.772 152.843V148.659C165.772 134.019 177.722 121.81 197.529 119.696C201.585 125.158 203.758 131.601 203.758 138.486V143.576ZM195.45 117.164C175.678 119.939 162.999 132.636 162.999 148.659V152.843C162.999 154.32 163.21 155.773 163.607 157.151H122.879C116.497 157.151 111.324 151.073 111.324 143.576V138.486C111.324 118.877 128.945 102.852 157.541 102.852C174.499 102.852 187.597 108.487 195.45 117.164Z" fill="white"/>
<circle id="Ellipse 47" cx="157.541" cy="75.1083" r="23.1083" fill="white"/>
<circle id="Ellipse 48" cx="203.758" cy="96.5695" r="18.9926" fill="white"/>
</g>
</g>
<defs>
<filter id="filter0_d_168_21523" x="78.8073" y="0" width="224" height="224" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4" dy="4"/>
<feGaussianBlur stdDeviation="6"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.00392157 0 0 0 0 0.266667 0 0 0 0 0 0 0 0 0.04 0"/>
<feBlend mode="multiply" in2="BackgroundImageFix" result="effect1_dropShadow_168_21523"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_168_21523" result="shape"/>
</filter>
<filter id="filter1_bdiii_168_21523" x="0.259857" y="6.08682" width="300" height="300" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="5"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_168_21523"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-16" dy="30"/>
<feGaussianBlur stdDeviation="25"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.783333 0 0 0 0 0.961 0 0 0 0 1 0 0 0 0.2 0"/>
<feBlend mode="multiply" in2="effect1_backgroundBlur_168_21523" result="effect2_dropShadow_168_21523"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_168_21523" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-3" dy="3"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_168_21523"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="5" dy="-5"/>
<feGaussianBlur stdDeviation="12.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.575 0 0 0 0 0.847 0 0 0 0 1 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_168_21523" result="effect4_innerShadow_168_21523"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="-2"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="effect4_innerShadow_168_21523" result="effect5_innerShadow_168_21523"/>
</filter>
<filter id="filter2_d_168_21523" x="91.3245" y="52" width="170.418" height="152" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="20"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.137812 0 0 0 0 0.241096 0 0 0 0 0.7875 0 0 0 0.7 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_168_21523"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_168_21523" result="shape"/>
</filter>
<linearGradient id="paint0_linear_168_21523" x1="134.351" y1="201.342" x2="263.468" y2="14.1808" gradientUnits="userSpaceOnUse">
<stop stop-color="#1C78F2"/>
<stop offset="0.526042" stop-color="#3AA2FD"/>
<stop offset="1" stop-color="#1AC3FB"/>
</linearGradient>
<linearGradient id="paint1_linear_168_21523" x1="231.974" y1="54.6648" x2="88.9979" y2="237.083" gradientUnits="userSpaceOnUse">
<stop stop-color="#3BB6EB"/>
<stop offset="1" stop-color="#76ABFC" stop-opacity="0.52"/>
</linearGradient>
</defs>
</svg>
