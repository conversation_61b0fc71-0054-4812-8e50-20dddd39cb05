<svg width="101" height="100" viewBox="0 0 101 100" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_529_7068)">
<g clip-path="url(#clip0_529_7068)">
<path d="M6.66675 15.4654C6.66675 8.02867 12.6954 2 20.1322 2H81.2013C88.6381 2 94.6667 8.02867 94.6667 15.4654V76.5346C94.6667 83.9713 88.6381 90 81.2013 90H20.1322C12.6954 90 6.66675 83.9713 6.66675 76.5346V15.4654Z" fill="url(#paint0_linear_529_7068)"/>
<g filter="url(#filter1_d_529_7068)">
<path d="M6.66675 14C6.66675 7.37258 12.0393 2 18.6667 2H82.6667C89.2942 2 94.6667 7.37258 94.6667 14V78C94.6667 84.6274 89.2942 90 82.6667 90H18.6667C12.0393 90 6.66675 84.6274 6.66675 78V14Z" fill="url(#paint1_linear_529_7068)"/>
</g>
<g opacity="0.6">
<path fill-rule="evenodd" clip-rule="evenodd" d="M27.8083 55.5511L62.8081 103.029L99.6462 94.674L112.651 78.2207L68.8602 31.9248L28.8985 33.1327L27.8083 55.5511Z" fill="url(#paint2_linear_529_7068)" style="mix-blend-mode:multiply"/>
</g>
<g filter="url(#filter2_d_529_7068)">
<path d="M72.3806 55.988L73.8723 56.1977C74.0946 56.2278 74.2982 56.3384 74.4446 56.5084C74.591 56.6784 74.6699 56.8962 74.6666 57.1205V60.8616C74.6666 61.3256 74.3258 61.7188 73.8697 61.7844L72.378 61.9941C72.2167 62.0182 72.0645 62.0841 71.9367 62.1853C71.8088 62.2865 71.7097 62.4194 71.6492 62.5708C71.3586 63.2658 70.9825 63.9219 70.5297 64.5239C70.4295 64.6535 70.3644 64.8069 70.341 64.969C70.3176 65.1312 70.3365 65.2967 70.3961 65.4494L70.9597 66.8493C71.0433 67.0568 71.0495 67.2874 70.9773 67.4991C70.9052 67.7108 70.7593 67.8896 70.5665 68.0028L67.3419 69.8746C67.1484 69.9881 66.9202 70.027 66.7 69.9841C66.4799 69.9411 66.283 69.8192 66.1464 69.6413L65.2236 68.4563C65.1213 68.327 64.9868 68.2268 64.8336 68.1656C64.6805 68.1044 64.5139 68.0844 64.3506 68.1077C63.6109 68.2032 62.862 68.2032 62.1223 68.1077C61.959 68.0844 61.7925 68.1044 61.6393 68.1656C61.4861 68.2268 61.3516 68.327 61.2493 68.4563L60.3265 69.6413C60.1903 69.8196 59.9936 69.942 59.7734 69.9855C59.5532 70.029 59.3248 69.9905 59.131 69.8773L55.9065 68.0054C55.7132 67.8926 55.567 67.7139 55.4948 67.502C55.4225 67.2901 55.4291 67.0593 55.5132 66.8519L56.0769 65.4494C56.1366 65.2967 56.1557 65.1312 56.1323 64.969C56.1088 64.8068 56.0437 64.6534 55.9432 64.5239C55.4916 63.9221 55.1156 63.267 54.8237 62.5735C54.7627 62.4221 54.6635 62.2892 54.5358 62.1877C54.408 62.0861 54.2561 62.0194 54.0949 61.9941L52.6032 61.7844C52.3816 61.7522 52.179 61.6412 52.0326 61.4717C51.8862 61.3021 51.8058 61.0855 51.8063 60.8616V57.1205C51.8063 56.6539 52.1471 56.2607 52.6059 56.1951L54.0976 55.9854C54.2588 55.9613 54.411 55.8954 54.5389 55.7942C54.6667 55.693 54.7659 55.5601 54.8264 55.4086C55.1174 54.706 55.4975 54.0533 55.9458 53.4555C56.0463 53.3261 56.1115 53.1727 56.1349 53.0105C56.1583 52.8483 56.1392 52.6827 56.0795 52.5301L55.5158 51.1302C55.4323 50.9227 55.426 50.6921 55.4982 50.4804C55.5704 50.2687 55.7162 50.0899 55.9091 49.9767L59.1337 48.1049C59.3271 47.9914 59.5554 47.9524 59.7755 47.9954C59.9956 48.0384 60.1925 48.1603 60.3291 48.3382L61.2493 49.5231C61.3519 49.6529 61.4868 49.7534 61.6405 49.8146C61.7941 49.8758 61.9612 49.8955 62.1249 49.8718C62.8646 49.7763 63.6135 49.7763 64.3533 49.8718C64.5166 49.895 64.6831 49.8751 64.8363 49.8139C64.9894 49.7527 65.1239 49.6525 65.2263 49.5231L66.1491 48.3382C66.2853 48.1598 66.482 48.0374 66.7021 47.994C66.9223 47.9505 67.1507 47.989 67.3445 48.1022L70.5691 49.9767C70.7624 50.0895 70.9085 50.2683 70.9808 50.4801C71.053 50.692 71.0464 50.9228 70.9623 51.1302L70.3987 52.5327C70.3392 52.6854 70.3202 52.8509 70.3436 53.0131C70.3671 53.1752 70.4321 53.3286 70.5324 53.4582C70.9807 54.0559 71.3582 54.7113 71.6518 55.4086C71.7128 55.56 71.812 55.6929 71.9398 55.7944C72.0675 55.896 72.2194 55.9627 72.3806 55.988ZM59.6213 58.9871C59.6213 60.9953 61.2414 62.6259 63.2391 62.6259C65.2367 62.6259 66.8569 60.9953 66.8569 58.9871C66.8569 56.979 65.2367 55.3483 63.2391 55.3483C61.2414 55.3483 59.6213 56.979 59.6213 58.9871Z" fill="url(#paint3_linear_529_7068)"/>
</g>
<g filter="url(#filter3_d_529_7068)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M35.7614 24.9971C35.7614 23.3402 37.1046 21.9971 38.7614 21.9971H56.796C58.4528 21.9971 59.796 23.3402 59.796 24.9971V30.4519H65.8906C67.5475 30.4519 68.8906 31.795 68.8906 33.4519V43.5289C67.1274 42.8839 65.2231 42.5319 63.2365 42.5319C58.82 42.5319 54.8099 44.2716 51.8539 47.103H36.264C34.6071 47.103 33.264 48.4462 33.264 50.103V55.9407H29.6667C28.0099 55.9407 26.6667 54.5975 26.6667 52.9407V33.4519C26.6667 31.7951 28.0099 30.4519 29.6667 30.4519H35.7614V24.9971ZM36.6862 49.2827H49.9449C49.379 50.0562 48.879 50.881 48.4532 51.7488H36.6862C36.0052 51.7488 35.4531 51.1968 35.4531 50.5158C35.4531 49.8348 36.0052 49.2827 36.6862 49.2827ZM36.6862 53.4746H47.7257C47.4428 54.2702 47.2194 55.094 47.0608 55.9407H36.6862C36.0052 55.9407 35.4531 55.3886 35.4531 54.7076C35.4531 54.0266 36.0052 53.4746 36.6862 53.4746Z" fill="url(#paint4_linear_529_7068)"/>
</g>
<circle opacity="0.3" cx="33.6362" cy="36.5483" r="2.17914" fill="#EEA73D"/>
</g>
</g>
<defs>
<filter id="filter0_d_529_7068" x="0.666748" y="0" width="100" height="100" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.258824 0 0 0 0 0.27451 0 0 0 0 0.305882 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_529_7068"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_529_7068" result="shape"/>
</filter>
<filter id="filter1_d_529_7068" x="0.666748" y="0" width="100" height="100" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.259809 0 0 0 0 0.274891 0 0 0 0 0.304167 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_529_7068"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_529_7068" result="shape"/>
</filter>
<filter id="filter2_d_529_7068" x="50.0413" y="47.9766" width="26.3904" height="25.5563" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.76497"/>
<feGaussianBlur stdDeviation="0.882486"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.8375 0 0 0 0 0.508607 0 0 0 0 0.202396 0 0 0 0.52 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_529_7068"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_529_7068" result="shape"/>
</filter>
<filter id="filter3_d_529_7068" x="24.9018" y="21.9971" width="45.7538" height="37.4733" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.76497"/>
<feGaussianBlur stdDeviation="0.882486"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.8375 0 0 0 0 0.508607 0 0 0 0 0.202396 0 0 0 0.52 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_529_7068"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_529_7068" result="shape"/>
</filter>
<linearGradient id="paint0_linear_529_7068" x1="-14.25" y1="27.7785" x2="37.307" y2="105.209" gradientUnits="userSpaceOnUse">
<stop stop-color="#57D3FD"/>
<stop offset="1" stop-color="#378DF0"/>
</linearGradient>
<linearGradient id="paint1_linear_529_7068" x1="50.6667" y1="2" x2="50.6667" y2="90" gradientUnits="userSpaceOnUse">
<stop stop-color="#EABE38"/>
<stop offset="1" stop-color="#F19340"/>
</linearGradient>
<linearGradient id="paint2_linear_529_7068" x1="36.2415" y1="53.47" x2="65.8052" y2="84.0786" gradientUnits="userSpaceOnUse">
<stop stop-color="#915004" stop-opacity="0.436134"/>
<stop offset="1" stop-color="#9D5F16" stop-opacity="0.0503988"/>
</linearGradient>
<linearGradient id="paint3_linear_529_7068" x1="32.721" y1="29.0807" x2="32.721" y2="70.0031" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#FFF1D2"/>
</linearGradient>
<linearGradient id="paint4_linear_529_7068" x1="-8.58425" y1="25.311" x2="-8.58425" y2="54.0985" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#FFF1D2"/>
</linearGradient>
<clipPath id="clip0_529_7068">
<path d="M6.66675 15.4654C6.66675 8.02867 12.6954 2 20.1322 2H81.2013C88.6381 2 94.6667 8.02867 94.6667 15.4654V76.5346C94.6667 83.9713 88.6381 90 81.2013 90H20.1322C12.6954 90 6.66675 83.9713 6.66675 76.5346V15.4654Z" fill="white"/>
</clipPath>
</defs>
</svg>
