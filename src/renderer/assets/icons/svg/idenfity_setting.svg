<svg width="101" height="100" viewBox="0 0 101 100" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_529_6861)">
<g clip-path="url(#clip0_529_6861)">
<rect x="6.33333" y="2" width="88" height="88" rx="12" fill="url(#paint0_linear_529_6861)"/>
<g opacity="0.7">
<path fill-rule="evenodd" clip-rule="evenodd" d="M29.0654 67.7329L77.4605 113.045L126.694 70.9811L71.7256 23.7109L29.0654 67.7329Z" fill="url(#paint1_linear_529_6861)" style="mix-blend-mode:multiply"/>
</g>
<path d="M27.5833 26.5929C27.5833 24.6086 29.1919 23 31.1762 23H69.9905C71.9747 23 73.5833 24.6086 73.5833 26.5929V65.4071C73.5833 67.3914 71.9747 69 69.9905 69H31.1762C29.1919 69 27.5833 67.3914 27.5833 65.4071V26.5929Z" fill="url(#paint2_linear_529_6861)"/>
<path d="M39.5257 27.5707C39.5257 26.8073 38.9069 26.1885 38.1435 26.1885H33.0357C31.7853 26.1885 30.7717 27.2021 30.7717 28.4524V33.5602C30.7717 34.3236 31.3906 34.9424 32.1539 34.9424C32.9173 34.9424 33.5361 34.3236 33.5361 33.5602V28.9529H38.1435C38.9069 28.9529 39.5257 28.3341 39.5257 27.5707ZM39.5257 64.4295C39.5257 63.6661 38.9069 63.0473 38.1435 63.0473H33.5361V58.4399C33.5361 57.6766 32.9173 57.0577 32.1539 57.0577C31.3906 57.0577 30.7717 57.6766 30.7717 58.4399V64.3647C30.7717 65.1639 31.4195 65.8117 32.2187 65.8117H38.1435C38.9069 65.8117 39.5257 65.1928 39.5257 64.4295ZM63.0232 65.8117C62.2598 65.8117 61.641 65.1928 61.641 64.4295C61.641 63.6661 62.2598 63.0473 63.0232 63.0473H67.6305V58.4399C67.6305 57.6766 68.2494 57.0577 69.0127 57.0577C69.7761 57.0577 70.3949 57.6766 70.3949 58.4399V64.3647C70.3949 65.1639 69.7471 65.8117 68.948 65.8117H63.0232ZM63.0232 28.9529C62.2598 28.9529 61.641 28.3341 61.641 27.5707C61.641 26.8073 62.2598 26.1885 63.0232 26.1885H68.131C69.3813 26.1885 70.3949 27.2021 70.3949 28.4524V33.5602C70.3949 34.3236 69.7761 34.9424 69.0127 34.9424C68.2494 34.9424 67.6305 34.3236 67.6305 33.5602V28.9529H63.0232Z" fill="#9CF8DC"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M61.3846 38.6959C61.2819 38.6169 61.1743 38.5453 61.0618 38.4812L52.481 33.5258C52.3684 33.4616 52.2524 33.4049 52.1329 33.3555C52.0135 33.3051 51.8915 33.2632 51.7671 33.2296C51.6418 33.197 51.5149 33.1723 51.3865 33.1556C51.2582 33.1378 51.1293 33.1289 51 33.1289C50.8707 33.1289 50.7418 33.1378 50.6135 33.1556C50.4851 33.1723 50.3582 33.1975 50.2329 33.2311C50.1075 33.2647 49.9855 33.3061 49.8671 33.3555C49.7476 33.4049 49.6316 33.4616 49.519 33.5258L40.9382 38.4812C40.8257 38.5453 40.7181 38.6169 40.6154 38.6959C40.5127 38.7749 40.4159 38.8598 40.3251 38.9506C40.2333 39.0424 40.1479 39.1397 40.0689 39.2424C39.9899 39.3451 39.9183 39.4527 39.8542 39.5652C39.789 39.6778 39.7317 39.7933 39.6824 39.9118C39.633 40.0312 39.591 40.1537 39.5565 40.2791C39.5239 40.4045 39.4992 40.5313 39.4824 40.6597C39.4657 40.788 39.4573 40.9169 39.4573 41.0462V50.9554C39.4573 51.0838 39.4657 51.2121 39.4824 51.3405C39.4992 51.4688 39.5244 51.5957 39.558 51.7211C39.5915 51.8465 39.633 51.9689 39.6824 52.0884C39.7317 52.2078 39.789 52.3234 39.8542 52.4349C39.9183 52.5475 39.9899 52.6551 40.0689 52.7578C40.1479 52.8605 40.2333 52.9577 40.3251 53.0495C40.4159 53.1404 40.5127 53.2253 40.6154 53.3043C40.7181 53.3832 40.8257 53.4548 40.9382 53.519L49.519 58.4744C49.6316 58.5385 49.7476 58.5953 49.8671 58.6447C49.9865 58.694 50.1084 58.7355 50.2329 58.7691C50.3582 58.8026 50.4851 58.8283 50.6135 58.8461C50.7418 58.8629 50.8707 58.8713 51 58.8713C51.1293 58.8713 51.2582 58.8629 51.3865 58.8461C51.5149 58.8283 51.6418 58.8026 51.7671 58.7691C51.8925 58.7355 52.0145 58.694 52.1329 58.6447C52.2524 58.5953 52.3684 58.5385 52.481 58.4744L61.0618 53.519C61.1743 53.4548 61.2819 53.3832 61.3846 53.3043C61.4873 53.2253 61.5841 53.1404 61.6749 53.0495C61.7667 52.9577 61.8521 52.8605 61.9311 52.7578C62.0101 52.6551 62.0817 52.5475 62.1458 52.4349C62.211 52.3224 62.2683 52.2069 62.3176 52.0884C62.367 51.9689 62.409 51.8465 62.4435 51.7211C62.4761 51.5957 62.5008 51.4688 62.5176 51.3405C62.5343 51.2121 62.5427 51.0833 62.5427 50.954V41.0447C62.5427 40.9164 62.5343 40.788 62.5176 40.6597C62.5008 40.5313 62.4756 40.4045 62.442 40.2791C62.4085 40.1537 62.367 40.0312 62.3176 39.9118C62.2683 39.7923 62.211 39.6768 62.1458 39.5652C62.0817 39.4527 62.0101 39.3451 61.9311 39.2424C61.8521 39.1397 61.7667 39.0424 61.6749 38.9506C61.5841 38.8598 61.4873 38.7749 61.3846 38.6959ZM45.527 48.2671C45.2293 47.5484 45.0761 46.778 45.0761 46.0001C45.0761 45.2222 45.2293 44.4518 45.527 43.7331C45.8247 43.0144 46.2611 42.3613 46.8112 41.8113C47.3612 41.2612 48.0143 40.8248 48.733 40.5271C49.4517 40.2294 50.2221 40.0762 51 40.0762C51.7779 40.0762 52.5483 40.2294 53.267 40.5271C53.9857 40.8248 54.6387 41.2612 55.1888 41.8113C55.7389 42.3613 56.1753 43.0144 56.473 43.7331C56.7707 44.4518 56.9239 45.2222 56.9239 46.0001C56.9239 46.778 56.7707 47.5484 56.473 48.2671C56.1753 48.9858 55.7389 49.6388 55.1888 50.1889C54.6387 50.739 53.9857 51.1754 53.267 51.4731C52.5483 51.7708 51.7779 51.924 51 51.924C50.2221 51.924 49.4517 51.7708 48.733 51.4731C48.0143 51.1754 47.3612 50.739 46.8112 50.1889C46.2611 49.6388 45.8247 48.9858 45.527 48.2671Z" fill="#17D19F"/>
<path d="M47.2975 46.0003C47.2975 46.9822 47.6876 47.924 48.382 48.6183C49.0763 49.3127 50.018 49.7027 51 49.7027C51.9819 49.7027 52.9237 49.3127 53.618 48.6183C54.3124 47.924 54.7024 46.9822 54.7024 46.0003C54.7024 45.5141 54.6067 45.0326 54.4206 44.5834C54.2345 44.1342 53.9618 43.7261 53.618 43.3823C53.2742 43.0385 52.8661 42.7657 52.4169 42.5797C51.9677 42.3936 51.4862 42.2979 51 42.2979C50.5138 42.2979 50.0323 42.3936 49.5831 42.5797C49.1339 42.7657 48.7258 43.0385 48.382 43.3823C48.0382 43.7261 47.7654 44.1342 47.5794 44.5834C47.3933 45.0326 47.2975 45.5141 47.2975 46.0003Z" fill="#9CF8DC"/>
</g>
</g>
<defs>
<filter id="filter0_d_529_6861" x="0.333328" y="0" width="100" height="100" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.258824 0 0 0 0 0.27451 0 0 0 0 0.305882 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_529_6861"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_529_6861" result="shape"/>
</filter>
<linearGradient id="paint0_linear_529_6861" x1="105.376" y1="60.1764" x2="63.2763" y2="-11.0525" gradientUnits="userSpaceOnUse">
<stop stop-color="#00AD8B"/>
<stop offset="1" stop-color="#2AF0B1"/>
</linearGradient>
<linearGradient id="paint1_linear_529_6861" x1="59.244" y1="49.8921" x2="82.9701" y2="75.1623" gradientUnits="userSpaceOnUse">
<stop stop-color="#16A484"/>
<stop offset="1" stop-color="#16A484" stop-opacity="0.1"/>
</linearGradient>
<linearGradient id="paint2_linear_529_6861" x1="48.7433" y1="11.04" x2="48.7433" y2="65.32" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#D2FFF7"/>
</linearGradient>
<clipPath id="clip0_529_6861">
<rect x="6.33333" y="2" width="88" height="88" rx="12" fill="white"/>
</clipPath>
</defs>
</svg>
