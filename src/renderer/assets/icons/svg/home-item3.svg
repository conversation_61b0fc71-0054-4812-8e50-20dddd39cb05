<svg width="303" height="307" viewBox="0 0 303 307" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Group 427318905">
<g id="Rectangle 1159" filter="url(#filter0_d_168_21534)">
<rect x="86.3268" y="8" width="200" height="200" rx="66" fill="url(#paint0_linear_168_21534)"/>
</g>
<g id="Rectangle 1160" filter="url(#filter1_bdiii_168_21534)">
<path d="M66.7404 92.0868C66.7404 55.636 96.2896 26.0868 132.74 26.0868H200.74C237.191 26.0868 266.74 55.636 266.74 92.0868V160.087C266.74 196.538 237.191 226.087 200.74 226.087H132.74C96.2896 226.087 66.7404 196.538 66.7404 160.087V92.0868Z" fill="#FFC8D1" fill-opacity="0.35" shape-rendering="crispEdges"/>
<path d="M67.2404 92.0868C67.2404 55.9121 96.5657 26.5868 132.74 26.5868H200.74C236.915 26.5868 266.24 55.9121 266.24 92.0868V160.087C266.24 196.261 236.915 225.587 200.74 225.587H132.74C96.5657 225.587 67.2404 196.261 67.2404 160.087V92.0868Z" stroke="url(#paint1_linear_168_21534)" stroke-opacity="0.2" shape-rendering="crispEdges"/>
</g>
<g id="Group 237485" filter="url(#filter2_d_168_21534)">
<path id="Subtract" fill-rule="evenodd" clip-rule="evenodd" d="M210.003 81.676H195.26C194.335 68.1279 183.051 57.425 169.268 57.425C155.485 57.425 144.202 68.1279 143.276 81.676H128.534C121.906 81.676 116.534 87.0486 116.534 93.676V175.146C116.534 181.773 121.906 187.146 128.534 187.146H187.26C182.844 181.981 180.177 175.276 180.177 167.949C180.177 151.625 193.41 138.392 209.734 138.392C214.111 138.392 218.266 139.344 222.003 141.051V93.676C222.003 87.0486 216.631 81.676 210.003 81.676ZM169.268 64.7762C178.989 64.7762 186.978 72.1927 187.885 81.676H150.652C151.559 72.1927 159.547 64.7762 169.268 64.7762ZM137.782 114.855C136.06 112.991 133.153 112.875 131.289 114.597C129.425 116.318 129.309 119.225 131.031 121.089C140.532 131.378 154.15 137.832 169.268 137.832C184.387 137.832 198.005 131.378 207.506 121.089C209.228 119.225 209.112 116.318 207.248 114.597C205.383 112.875 202.477 112.991 200.755 114.855C192.921 123.34 181.717 128.643 169.268 128.643C156.82 128.643 145.616 123.34 137.782 114.855Z" fill="white"/>
<path id="Exclude" fill-rule="evenodd" clip-rule="evenodd" d="M209.734 194.749C224.535 194.749 236.534 182.75 236.534 167.949C236.534 153.148 224.535 141.149 209.734 141.149C194.933 141.149 182.934 153.148 182.934 167.949C182.934 182.75 194.933 194.749 209.734 194.749ZM220.602 178.817C219.167 180.252 216.839 180.252 215.404 178.817L209.734 173.147L204.064 178.817C202.629 180.252 200.301 180.252 198.866 178.817C197.43 177.381 197.43 175.054 198.866 173.619L204.536 167.949L198.866 162.279C197.431 160.844 197.431 158.516 198.866 157.081C200.301 155.645 202.629 155.645 204.064 157.081L209.734 162.751L215.404 157.081C216.839 155.645 219.166 155.645 220.602 157.081C222.037 158.516 222.037 160.844 220.602 162.279L214.932 167.949L220.602 173.619C222.037 175.054 222.037 177.381 220.602 178.817Z" fill="white"/>
</g>
</g>
<defs>
<filter id="filter0_d_168_21534" x="78.3268" y="0" width="224" height="224" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4" dy="4"/>
<feGaussianBlur stdDeviation="6"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.00392157 0 0 0 0 0.266667 0 0 0 0 0 0 0 0 0.04 0"/>
<feBlend mode="multiply" in2="BackgroundImageFix" result="effect1_dropShadow_168_21534"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_168_21534" result="shape"/>
</filter>
<filter id="filter1_bdiii_168_21534" x="0.740356" y="6.08676" width="300" height="300" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="5"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_168_21534"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-16" dy="30"/>
<feGaussianBlur stdDeviation="25"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.791667 0 0 0 0 0.791667 0 0 0 0.2 0"/>
<feBlend mode="multiply" in2="effect1_backgroundBlur_168_21534" result="effect2_dropShadow_168_21534"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_168_21534" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-3" dy="3"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_168_21534"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="5" dy="-5"/>
<feGaussianBlur stdDeviation="12.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.8125 0 0 0 0 0.8125 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_168_21534" result="effect4_innerShadow_168_21534"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="-2"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="effect4_innerShadow_168_21534" result="effect5_innerShadow_168_21534"/>
</filter>
<filter id="filter2_d_168_21534" x="96.5336" y="57.425" width="160" height="177.324" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="20"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.733333 0 0 0 0 0.0855556 0 0 0 0 0.0855556 0 0 0 0.7 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_168_21534"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_168_21534" result="shape"/>
</filter>
<linearGradient id="paint0_linear_168_21534" x1="260.018" y1="208" x2="100.463" y2="-3.08856" gradientUnits="userSpaceOnUse">
<stop stop-color="#FA2941"/>
<stop offset="0.546875" stop-color="#FF738A"/>
<stop offset="1" stop-color="#FF6179"/>
</linearGradient>
<linearGradient id="paint1_linear_168_21534" x1="232.455" y1="54.6648" x2="89.4784" y2="237.083" gradientUnits="userSpaceOnUse">
<stop stop-color="#EB3B50"/>
<stop offset="1" stop-color="#FC769E" stop-opacity="0.52"/>
</linearGradient>
</defs>
</svg>
