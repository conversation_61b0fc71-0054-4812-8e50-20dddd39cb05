<svg width="101" height="100" viewBox="0 0 101 100" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_482_2047)">
<g clip-path="url(#clip0_482_2047)">
<path d="M6.33501 14C6.33501 7.37258 11.7076 2 18.335 2H82.335C88.9624 2 94.335 7.37258 94.335 14V78C94.335 84.6274 88.9624 90 82.335 90H18.335C11.7076 90 6.33501 84.6274 6.33501 78V14Z" fill="url(#paint0_linear_482_2047)"/>
<g opacity="0.6">
<path fill-rule="evenodd" clip-rule="evenodd" d="M28.8265 68.3183L59.5911 96.7597L93.0225 88.6341L112.995 68.3184L58 24.5L33.9831 40.9411L28.8265 68.3183Z" fill="url(#paint1_linear_482_2047)" style="mix-blend-mode:multiply"/>
</g>
<g filter="url(#filter1_d_482_2047)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M40.9456 50.4518C40.6529 49.4322 39.7204 48.7297 38.6596 48.7297H30.7134C29.3998 48.7297 28.335 49.7946 28.335 51.1081V67.2916C28.335 68.6052 29.3998 69.67 30.7134 69.67H69.9566C71.2702 69.67 72.335 68.6052 72.335 67.2916V51.1081C72.335 49.7946 71.2702 48.7297 69.9566 48.7297H62.0105C60.9497 48.7297 60.0171 49.4322 59.7244 50.4518L58.5943 54.3886C58.3016 55.4082 57.369 56.1107 56.3082 56.1107H44.3618C43.301 56.1107 42.3685 55.4082 42.0758 54.3886L40.9456 50.4518Z" fill="url(#paint2_linear_482_2047)"/>
<path d="M30.532 50.4378L34.7408 38.7414L40.0278 38.7414" stroke="white" stroke-width="4.19048" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M70.0074 50.4378L65.7986 38.7414L60.5116 38.7414" stroke="white" stroke-width="4.19048" stroke-linecap="round" stroke-linejoin="round"/>
<circle cx="50.335" cy="34" r="12" fill="url(#paint3_linear_482_2047)"/>
<path d="M50.335 26.5879L50.335 34.9335" stroke="#858FA3" stroke-width="3.63636" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M50.335 40.6438L50.335 40.6958" stroke="#838DA2" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_482_2047" x="0.335007" y="0" width="100" height="100" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.259809 0 0 0 0 0.274891 0 0 0 0 0.304167 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_482_2047"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_482_2047" result="shape"/>
</filter>
<filter id="filter1_d_482_2047" x="26.455" y="22" width="47.76" height="51.4299" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.88"/>
<feGaussianBlur stdDeviation="0.94"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.293576 0 0 0 0 0.334479 0 0 0 0 0.395833 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_482_2047"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_482_2047" result="shape"/>
</filter>
<linearGradient id="paint0_linear_482_2047" x1="50.335" y1="2" x2="50.335" y2="90" gradientUnits="userSpaceOnUse">
<stop stop-color="#9BABC9"/>
<stop offset="1" stop-color="#6B7BA0"/>
</linearGradient>
<linearGradient id="paint1_linear_482_2047" x1="51.7537" y1="63.4002" x2="66.2453" y2="82.3841" gradientUnits="userSpaceOnUse">
<stop stop-color="#535F76" stop-opacity="0.6"/>
<stop offset="1" stop-color="#535F76" stop-opacity="0.05"/>
</linearGradient>
<linearGradient id="paint2_linear_482_2047" x1="46.6138" y1="49.9801" x2="46.6138" y2="78.0812" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#B9BFCC"/>
</linearGradient>
<linearGradient id="paint3_linear_482_2047" x1="48.3053" y1="22" x2="48.3053" y2="56.2524" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#B9BFCC"/>
</linearGradient>
<clipPath id="clip0_482_2047">
<path d="M6.33501 14C6.33501 7.37258 11.7076 2 18.335 2H82.335C88.9624 2 94.335 7.37258 94.335 14V78C94.335 84.6274 88.9624 90 82.335 90H18.335C11.7076 90 6.33501 84.6274 6.33501 78V14Z" fill="white"/>
</clipPath>
</defs>
</svg>
