<svg width="303" height="307" viewBox="0 0 303 307" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Group 427318903">
<g id="Rectangle 1158" filter="url(#filter0_d_168_21514)">
<rect x="86.8242" y="8" width="200" height="200" rx="66" fill="url(#paint0_linear_168_21514)"/>
</g>
<g id="Rectangle 1159" filter="url(#filter1_bdiii_168_21514)">
<path d="M66.2429 92.0868C66.2429 55.636 95.7921 26.0868 132.243 26.0868H200.243C236.694 26.0868 266.243 55.636 266.243 92.0868V160.087C266.243 196.538 236.694 226.087 200.243 226.087H132.243C95.7921 226.087 66.2429 196.538 66.2429 160.087V92.0868Z" fill="#B8FCE6" fill-opacity="0.5" shape-rendering="crispEdges"/>
<path d="M66.7429 92.0868C66.7429 55.9121 96.0683 26.5868 132.243 26.5868H200.243C236.418 26.5868 265.743 55.9121 265.743 92.0868V160.087C265.743 196.261 236.418 225.587 200.243 225.587H132.243C96.0683 225.587 66.7429 196.261 66.7429 160.087V92.0868Z" stroke="url(#paint1_linear_168_21514)" stroke-opacity="0.2" shape-rendering="crispEdges"/>
</g>
<g id="Group 237475" filter="url(#filter2_d_168_21514)">
<path id="Exclude" fill-rule="evenodd" clip-rule="evenodd" d="M200.887 78.0678H214.243C220.87 78.0678 226.243 83.4404 226.243 90.0678V166.068C226.243 172.695 220.87 178.068 214.243 178.068H138.243C131.616 178.068 126.243 172.695 126.243 166.068V90.0678C126.243 83.4404 131.616 78.0678 138.243 78.0678H151.599C152.477 65.2223 163.175 55.0745 176.243 55.0745C189.311 55.0745 200.009 65.2223 200.887 78.0678ZM193.894 78.0678C193.034 69.0763 185.46 62.0444 176.243 62.0444C167.026 62.0444 159.452 69.0763 158.592 78.0678H193.894ZM140.233 109.281C142.001 107.649 144.757 107.759 146.389 109.527C153.817 117.571 164.44 122.599 176.243 122.599C188.046 122.599 198.669 117.571 206.097 109.527C207.729 107.759 210.485 107.649 212.253 109.281C214.02 110.914 214.13 113.67 212.498 115.437C203.489 125.192 190.578 131.311 176.243 131.311C161.908 131.311 148.996 125.192 139.988 115.437C138.356 113.67 138.466 110.914 140.233 109.281Z" fill="white"/>
</g>
</g>
<defs>
<filter id="filter0_d_168_21514" x="78.8242" y="0" width="224" height="224" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4" dy="4"/>
<feGaussianBlur stdDeviation="6"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.00392157 0 0 0 0 0.266667 0 0 0 0 0 0 0 0 0.04 0"/>
<feBlend mode="multiply" in2="BackgroundImageFix" result="effect1_dropShadow_168_21514"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_168_21514" result="shape"/>
</filter>
<filter id="filter1_bdiii_168_21514" x="0.24292" y="6.08679" width="300" height="300" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="5"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_168_21514"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-16" dy="30"/>
<feGaussianBlur stdDeviation="25"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.655433 0 0 0 0 0.833333 0 0 0 0 0.652778 0 0 0 0.2 0"/>
<feBlend mode="multiply" in2="effect1_backgroundBlur_168_21514" result="effect2_dropShadow_168_21514"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_168_21514" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-3" dy="3"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="shape" result="effect3_innerShadow_168_21514"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="5" dy="-5"/>
<feGaussianBlur stdDeviation="12.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.620833 0 0 0 0 1 0 0 0 0 0.79525 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_168_21514" result="effect4_innerShadow_168_21514"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="-2"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="effect4_innerShadow_168_21514" result="effect5_innerShadow_168_21514"/>
</filter>
<filter id="filter2_d_168_21514" x="106.243" y="55.0745" width="140" height="162.993" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="20"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.623529 0 0 0 0 0.392157 0 0 0 0.7 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_168_21514"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_168_21514" result="shape"/>
</filter>
<linearGradient id="paint0_linear_168_21514" x1="133.074" y1="195.72" x2="254.683" y2="17.9833" gradientUnits="userSpaceOnUse">
<stop stop-color="#0ACD90"/>
<stop offset="0.515625" stop-color="#39F08E"/>
<stop offset="1" stop-color="#60E676"/>
</linearGradient>
<linearGradient id="paint1_linear_168_21514" x1="231.957" y1="54.6648" x2="88.981" y2="237.083" gradientUnits="userSpaceOnUse">
<stop stop-color="#3BEBA4"/>
<stop offset="1" stop-color="#50EA9A" stop-opacity="0.52"/>
</linearGradient>
</defs>
</svg>
