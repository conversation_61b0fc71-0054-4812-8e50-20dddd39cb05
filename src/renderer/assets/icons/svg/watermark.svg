<svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_529_6868)">
<g clip-path="url(#clip0_529_6868)">
<path d="M6 15.4654C6 8.02867 12.0287 2 19.4654 2H80.5346C87.9713 2 94 8.02867 94 15.4654V76.5346C94 83.9713 87.9713 90 80.5346 90H19.4654C12.0287 90 6 83.9713 6 76.5346V15.4654Z" fill="url(#paint0_linear_529_6868)"/>
<path d="M6 15.4654C6 8.02867 12.0287 2 19.4654 2H80.5346C87.9713 2 94 8.02867 94 15.4654V76.5346C94 83.9713 87.9713 90 80.5346 90H19.4654C12.0287 90 6 83.9713 6 76.5346V15.4654Z" fill="url(#paint1_linear_529_6868)"/>
<g opacity="0.7">
<path fill-rule="evenodd" clip-rule="evenodd" d="M25.981 64.8351L61.9269 104.715L100.418 95.9851L114.895 74.9631L71.1446 28.3408L63.3867 47.0003L25.981 64.8351Z" fill="url(#paint2_linear_529_6868)" style="mix-blend-mode:multiply"/>
</g>
<g filter="url(#filter1_d_529_6868)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M67.457 26.4498H49.4211C49.4211 23.4632 46.997 21.0391 44.0104 21.0391H40.4032C37.4166 21.0391 34.9925 23.4632 34.9925 26.4498H29.5817C26.5951 26.4498 24.171 28.874 24.171 31.8606V60.7179C24.171 63.7045 26.5951 66.1287 29.5817 66.1287H51.135C51.6489 66.1287 52.1337 65.9056 52.4828 65.5275C52.8222 65.1493 52.987 64.6354 52.9289 64.1215C52.8804 63.6463 52.8416 63.1712 52.8416 62.6863C52.9289 52.8333 60.8925 46.0468 70.6181 48.4782C71.1611 48.614 71.7332 48.4976 72.1696 48.1582C72.6059 47.8188 72.8677 47.2952 72.8677 46.7328V31.8606C72.8677 28.874 70.4436 26.4498 67.457 26.4498ZM75.6385 59.5132C75.6459 59.5444 75.6507 59.5761 75.6527 59.6081V59.6074C75.7083 59.9463 75.833 60.7439 75.8289 61.3945C75.8289 62.0992 75.7015 62.8697 75.6466 63.1991C75.6405 63.2357 75.6358 63.2621 75.6317 63.2804V63.296L75.6236 63.3407C75.488 64.1329 75.0157 64.6276 74.3848 64.6276H74.2797C73.2544 64.6276 72.4161 65.512 72.4161 66.603V66.6254L72.4121 66.6478C72.3782 67.0042 72.5585 67.402 72.5625 67.404L72.5666 67.4135L72.5713 67.4223C72.8654 68.1203 72.6303 69.0006 72.0522 69.3842L72.0441 69.3937L69.7427 70.7429L69.7346 70.747C69.4893 70.8751 69.2731 70.9605 68.981 70.9605C68.5494 70.9605 68.1278 70.7741 67.8229 70.4556C67.2198 69.8152 66.6207 69.4188 66.2629 69.4188C65.7824 69.4188 65.0546 70.0829 64.7835 70.3336C64.7722 70.344 64.7615 70.3543 64.7512 70.3643C64.7345 70.3804 64.7188 70.3956 64.7029 70.4082C64.4386 70.7226 64.0151 70.913 63.5454 70.913C63.333 70.913 63.1465 70.8487 62.9815 70.7919L62.9633 70.7856L62.9206 70.7694L60.6768 69.4452L60.6599 69.4364C60.0378 68.9959 59.7708 68.1231 60.0527 67.4427C60.0988 67.3166 60.2092 66.9595 60.2092 66.7088C60.2092 65.6218 59.375 64.7327 58.3463 64.7327H58.2955C57.6924 64.7327 57.2078 64.2319 57.0574 63.4553L57.0486 63.4058V63.3882C57.0425 63.3678 57.0384 63.3346 57.0344 63.294C56.9788 62.9545 56.8541 62.1555 56.8541 61.5022C56.8541 60.8542 56.9788 60.0634 57.0331 59.7193L57.0344 59.7111C57.0366 59.6964 57.039 59.6828 57.0413 59.6704C57.0453 59.6486 57.0486 59.6305 57.0486 59.6162V59.6007L57.0574 59.5559C57.1929 58.7631 57.6653 58.2704 58.2955 58.2704H58.3951C59.4198 58.2704 60.2574 57.3853 60.2574 56.2943C60.2574 55.969 60.1137 55.5963 60.0676 55.4865L60.0629 55.477L60.0588 55.4682C59.764 54.7702 59.9998 53.8899 60.5772 53.5064L60.5921 53.4975L62.9735 52.13L63.0114 52.1144C63.1808 52.0541 63.3713 51.9863 63.5983 51.9863C63.8152 51.9884 64.0295 52.0342 64.2284 52.1209C64.4273 52.2077 64.6066 52.3336 64.7557 52.4912C65.1136 52.8687 65.8421 53.4257 66.2717 53.4257C66.7034 53.4257 67.4292 52.8666 67.787 52.4912C68.073 52.1883 68.5344 51.9863 68.9451 51.9863C69.1594 51.9863 69.3512 52.0524 69.5174 52.1096L69.5313 52.1144L69.574 52.13L71.8449 53.4928C72.5666 53.9123 72.8776 54.7363 72.5856 55.4594C72.5395 55.5848 72.429 55.9426 72.429 56.1933C72.429 57.2803 73.2632 58.1694 74.2919 58.1694H74.3916C74.9947 58.1694 75.4792 58.6695 75.6297 59.4468L75.6385 59.4956V59.5132ZM63.393 61.4758C63.393 63.2025 64.7117 64.6012 66.3395 64.6012C67.9672 64.6012 69.286 63.2025 69.286 61.4758C69.286 59.7497 67.9672 58.351 66.3395 58.351C64.7117 58.351 63.393 59.7497 63.393 61.4758Z" fill="url(#paint3_linear_529_6868)"/>
</g>
<path opacity="0.5" d="M32.6986 37.5643C32.6986 37.5643 35.5951 39.067 38.8341 39.067C42.4588 39.067 46.1368 36.5664 49.9476 36.5664C52.8496 36.5664 55.2854 37.5643 55.2854 37.5643" stroke="#31B0F3" stroke-width="3.5" stroke-linecap="round"/>
<path opacity="0.5" d="M32.6986 46.2156C32.6986 46.2156 35.5951 47.7184 38.8341 47.7184C42.4588 47.7184 46.1368 45.2178 49.9476 45.2178C52.8496 45.2178 55.2854 46.2156 55.2854 46.2156" stroke="#31B0F3" stroke-width="3.5" stroke-linecap="round"/>
</g>
</g>
<defs>
<filter id="filter0_d_529_6868" x="0" y="0" width="100" height="100" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.258824 0 0 0 0 0.27451 0 0 0 0 0.305882 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_529_6868"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_529_6868" result="shape"/>
</filter>
<filter id="filter1_d_529_6868" x="23.3863" y="21.0391" width="53.2272" height="51.4911" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.78461"/>
<feGaussianBlur stdDeviation="0.392305"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.313712 0 0 0 0 0.574421 0 0 0 0 0.856856 0 0 0 0.840445 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_529_6868"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_529_6868" result="shape"/>
</filter>
<linearGradient id="paint0_linear_529_6868" x1="-14.9168" y1="27.7785" x2="36.6403" y2="105.209" gradientUnits="userSpaceOnUse">
<stop stop-color="#57D3FD"/>
<stop offset="1" stop-color="#378DF0"/>
</linearGradient>
<linearGradient id="paint1_linear_529_6868" x1="-14.9168" y1="27.7785" x2="36.6403" y2="105.209" gradientUnits="userSpaceOnUse">
<stop stop-color="#32C5F5"/>
<stop offset="1" stop-color="#2D86ED"/>
</linearGradient>
<linearGradient id="paint2_linear_529_6868" x1="54.2724" y1="64.7637" x2="62.0692" y2="84.6024" gradientUnits="userSpaceOnUse">
<stop stop-color="#01669E" stop-opacity="0.6"/>
<stop offset="1" stop-color="#01669E" stop-opacity="0.0503988"/>
</linearGradient>
<linearGradient id="paint3_linear_529_6868" x1="57.8961" y1="28.6793" x2="57.8961" y2="69.9" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#C5DFFF"/>
</linearGradient>
<clipPath id="clip0_529_6868">
<path d="M6 15.4654C6 8.02867 12.0287 2 19.4654 2H80.5346C87.9713 2 94 8.02867 94 15.4654V76.5346C94 83.9713 87.9713 90 80.5346 90H19.4654C12.0287 90 6 83.9713 6 76.5346V15.4654Z" fill="white"/>
</clipPath>
</defs>
</svg>
