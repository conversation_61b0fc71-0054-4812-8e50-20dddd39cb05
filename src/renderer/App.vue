<template>
  <!-- <el-config-provider :locale="i18nt"> -->
  <router-view v-slot="{ Component }">
    <component :is="Component" />
  </router-view>
  <!-- </el-config-provider> -->
</template>

<script setup lang="ts">
import { computed, onMounted } from "vue";
import { ElConfigProvider } from "element-plus";
import { i18n } from "./i18n";
import { invoke } from "./utils/ipcRenderer";
// const i18nt = computed(() => i18n.global.messages.value[i18n.global.locale.value].$el);

declare global {
  interface Window {
    electron: {
      ipcRenderer: {
        invoke(channel: string, ...args: any[]): Promise<any>;
      };
    };
  }
}

async function getMacAddress() {
  try {
    const macAddress = await invoke("get-mac-address");
    localStorage.setItem("macAddress", macAddress);
  } catch (error) {
    console.error("Failed to get MAC address:", error);
  }
}

onMounted(() => {
  getMacAddress();
});
</script>
<style></style>
