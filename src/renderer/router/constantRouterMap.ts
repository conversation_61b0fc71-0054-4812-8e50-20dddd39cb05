import { RouteRecordRaw } from 'vue-router'

const routes: Array<RouteRecordRaw> = [
    { path: '/:pathMatch(.*)*', name: '404', component: () => import('@renderer/views/common/404.vue') },
    { path: '/', name: '登录', component: () => import('@renderer/views/common/login/Login.vue') },
    {
        path: '/home',
        name: 'home',
        redirect: '/home/<USER>',
        component: () => import("@renderer/views/common/home/<USER>"),
        children: [{
            path: 'menuList',
            name: 'menu',
            props: (route) => ({ query: route.query }),
            component: () => import('@renderer/views/common/home/<USER>')
        }, {
            path: 'productSorting',
            name: 'productSorting',
            meta: {
                zh: '按商品分拣'
            },
            component: () => import("@renderer/views/sorting/product/index.vue")
        }, {
            path: 'goodsInfo',
            name: 'goodsInfo',
            meta: {
                zh: '商品清单'
            },
            component: () => import("@renderer/views/sorting/goodsInfo/index.vue")
        }, {
            path: 'linesSorting',
            name: 'linesSorting',
            meta: {
                zh: '按线路分拣'
            },
            component: () => import("@renderer/views/sorting/lines/index.vue")
        }, {
            path: 'customerSorting',
            name: 'customerSorting',
            meta: {
                zh: '按客户分拣'
            },
            component: () => import("@renderer/views/sorting/customer/index.vue")
        }, {
            path: 'videoSort',
            name: 'videoSort',
            meta: {
                zh: '分拣识别'
            },
            component: () => import("@renderer/views/sorting/video/index.vue")
        }, {
            path: 'LackStockSorting',
            name: 'LackStockSorting',
            meta: {
                zh: '缺货分拣'
            },
            component: () => import("@renderer/views/sorting/lackStock/index.vue")
        }, {
            path: 'productStore',
            name: 'productStore',
            meta: {
                zh: '按商品入库'
            },
            component: () => import("@renderer/views/store/product/index.vue")
        }, {
            path: 'supplierStore',
            name: 'supplierStore',
            meta: {
                zh: '按供应商入库'
            },
            component: () => import("@renderer/views/store/supplier/index.vue")
        }, {
            path: 'ordersStore',
            name: 'ordersStore',
            meta: {
                zh: '按订单入库'
            },
            component: () => import("@renderer/views/store/orders/index.vue")
        }, {
            path: 'recognizeStore',
            name: 'recognizeStore',
            meta: {
                zh: '识别入库'
            },
            component: () => import("@renderer/views/store/recognize/index.vue")
        },
        {
            path: 'storeGoodsInfo',
            name: 'storeGoodsInfo',
            meta: {
                zh: '入库商品清单'
            },
            component: () => import("@renderer/views/store/goodsInfo/index.vue")
        }, {
            path: 'storeVideo',
            name: 'storeVideo',
            meta: {
                zh: '分拣识别'
            },
            component: () => import("@renderer/views/store/video/index.vue")
        }, {
            path: 'idenfitySetting',
            name: 'idenfitySetting',
            meta: {
                zh: '识别相关设置'
            },
            component: () => import("@renderer/views/setting/idenfity/index.vue")
        }
        ]
    },
    // { path: '/testDemo', name: 'testDemo', component: () => import("@renderer/views/common/home/<USER>") },
    { path: '/PrintPage', name: '打印', component: () => import('@renderer/views/common/home/<USER>') },
    { path: '/sortPrintPage', name: '入库打印', component: () => import('@renderer/views/common/home/<USER>') },
]

export default routes