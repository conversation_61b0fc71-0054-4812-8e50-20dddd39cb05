<template>
  <div class="card" :class="[`btn-${data.bgType || 'normal'}`]" @click="handleItemTap(data)">
    <div class="content">
      <div class="spuname">{{ data.spuName }}</div>
      <div class="spuname">{{ data.customerName }}</div>
      <div class="goods-info">
        <div class="order-num">{{ data.totalNum }}</div>
        <div class="unit">{{ data.specInfos?.[0] }}</div>
      </div>
      <div class="sorted-num">
        <div class="num row">
          <div>{{data.whetherStandard === 'YES' ? $t('components.lackGoods.044049-0') :$t('components.lackGoods.044049-1')}}</div>
          <div>{{ data.sortedNum }}{{ data.specInfos?.[0] }}</div>
        </div>
        <div class="remark row">
          <div>{{ $t('components.lackGoods.044049-2') }}</div>
          <div>{{ data.remark || data.remark === '' ? '-' : data.remark }}</div>
        </div>
      </div>
    </div>
    <div class="lack-stautus" @click="handleAction($event)">{{ data.lackStatus === 'LACK' ? $t('components.lackGoods.044049-3') : $t('components.lackGoods.044049-4') }}</div>
  </div>
</template>

<script setup>
import { toRefs } from "vue";

const props = defineProps({
  data: {
    type: Object,
    default: () => {},
  },
});

const emits = defineEmits(["select", "clickItem"]);
const { data } = toRefs(props);

const handleAction = (event) => {
  event.stopPropagation();
  emits("clickItem", data.value);
}

function handleItemTap(data) {
  emits("select", data);
}
</script>

<style lang="scss" scoped>
.card {
  cursor: pointer;
  width: calc((100% - 72px) / 3);
  display: flex;
  border: 1px solid #fff;
  border-radius: 12px;
  transition: all 0.3s ease;

  .content {
    display: flex;
    flex: 1;
    padding: 12px 16px;
    flex-direction: column;
    gap: 8px;
    background: #fff;
    border-radius: 12px 0 0 12px;
    transition: all 0.3s ease;

    .spuname {
      color: var(---el-text-color-regular, #505762);
      font-family: "PingFang SC";
      font-size: 16px;
      font-style: normal;
      font-weight: 300;
      line-height: 24px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .goods-info {
      display: flex;
      align-items: flex-end;
      justify-content: center;
      .order-num {
        color: var(---el-text-color-primary, #1c2026);
        font-family: DINPro;
        font-size: 40px;
        font-style: normal;
        font-weight: 500;
        line-height: 46px;
      }

      .unit {
        color: var(---el-text-color-primary, #1c2026);
        text-align: center;
        font-family: "PingFang SC";
        font-size: 20px;
        font-style: normal;
        font-weight: 400;
        line-height: 28px;
      }
    }

    .sorted-num {
      color: var(---el-text-color-regular, #505762);
      font-family: "PingFang SC";
      font-size: 16px;
      font-style: normal;
      font-weight: 300;
      line-height: 24px;

      .row {
        display: flex;
        align-items: center;
        gap: 24px;
      }
    }
  }

  .lack-stautus {
    width: 24px;
    box-sizing: content-box;
    display: flex;
    padding: 8px 16px 12px 16px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    align-self: stretch;
    color: var(---el-text-color-regular, #505762);
    font-family: "PingFang SC";
    font-size: 24px;
    font-style: normal;
    font-weight: 400;
    line-height: 32px;
    border: 1px solid var(---el-border-color-lighter, #f0f2f5);
    background: #e6eaf0;
    border-radius: 0 12px 12px 0;
    transition: all 0.3s ease;
  }

  /* 正常状态 */
  &.btn-normal {
    border: 1px solid var(---el-border-color-light, #e6eaf0);
  }

  /* 选中状态 */
  &.btn-active {
    border: 1px solid #038A78;

    .content {
      background: #038A78;

      .spuname,
      .order-num,
      .unit,
      .sorted-num,
      .sorted-num .row div {
        color: #fff;
      }
    }

    .lack-stautus {
      background: #e8fff7;
      color: #25b195;
    }
  }
}
</style>
