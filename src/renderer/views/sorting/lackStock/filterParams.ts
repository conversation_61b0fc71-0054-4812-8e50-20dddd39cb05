import { ref, watch } from 'vue'
import { STATUS, SPECIMEN } from './constant'
import { userInfoT } from "@renderer/store/modules/template";
import dayjs from "dayjs";
const useUserStore = userInfoT()

export const filterForm = ref({
  sortStatus: STATUS.ALL,
  productType: SPECIMEN.ALL,
  warehouseId: '',
  deliveryDate: null,
  deliveryDateRange: [],
  shopId: useUserStore?.getShopList?.[0]?.id ?? undefined
}); // 筛选表单
export const searchInput = ref('');
export const isNeedHint = ref(true); // 是否需要提示
watch(() => filterForm.value.deliveryDate, () => {
  isNeedHint.value = true
})
export const initFilterForm = () => {
  filterForm.value = {
    sortStatus: STATUS.ALL,
    productType: SPECIMEN.ALL,
    warehouseId: '',
    deliveryDate: null,
    deliveryDateRange: [],
    shopId: useUserStore?.getShopList?.[0]?.id ?? undefined
  }
  searchInput.value = '';
}

/**
 * 初始化搜索日期时间
 */
export const handleInitSearchDateTime = () => {
  const currentTime = new Date();
  if (!filterForm.value.deliveryDate) {
    filterForm.value.deliveryDate = dayjs(currentTime).format("YYYY-MM-DD");
  }
  if (filterForm.value.deliveryDateRange?.length === 0) {
    filterForm.value.deliveryDateRange = [`${dayjs(currentTime).format("YYYY-MM-DD")}`, `${dayjs(currentTime).format("YYYY-MM-DD")}`];
  }
}