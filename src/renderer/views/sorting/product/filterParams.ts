import { ref, watch } from 'vue'
import { STATUS, SPECIMEN } from './constant'
import { userInfoT } from "@renderer/store/modules/template";
import dayjs from "dayjs";
const useUserStore = userInfoT()

export const filterForm = ref({
  sortStatus: STATUS.UNSORTED,
  productType: SPECIMEN.ALL,
  warehouseId: '',
  lineId: '',
  customerId: '',
  deliveryDate: null,
  shopId: useUserStore?.getShopList?.[0]?.id || undefined,
  show_available_stock: -1
}); // 筛选表单
export const searchInput = ref('');
export const currentGoodsType = ref(null); // 当前选择商品类型
export const isPrintReceipt = ref(false); // 是否不打印小票
export const isNeedHint = ref(true); // 是否需要提示
watch(() => filterForm.value.deliveryDate, () => {
  isNeedHint.value = true
})
export const initFilterForm = () => {
  filterForm.value = {
    sortStatus: STATUS.UNSORTED,
    productType: SPECIMEN.ALL,
    warehouseId: '',
    deliveryDate: null,
    lineId: '',
    customerId: '',
    shopId: useUserStore?.getShopList?.[0]?.id || undefined,
    show_available_stock: -1
  }
  searchInput.value = '';
  currentGoodsType.value = null;
  isPrintReceipt.value = false
}

/**
 * 初始化搜索日期时间
 */
export const handleInitSearchDateTime = () => {
  const currentTime = new Date();
  if (!filterForm.value.deliveryDate) {
    filterForm.value.deliveryDate = dayjs(currentTime).format("YYYY-MM-DD");
  }
}