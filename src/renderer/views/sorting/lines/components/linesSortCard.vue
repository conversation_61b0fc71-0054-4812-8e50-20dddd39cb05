<template>
    <div class="card" @click="handleItemTap(data)">
        <div class="status" :class="[`status-${status}`]">
            {{ $t('components.linesSortCard.016453-0') }}
            <span>{{ data.sortedCount || 0 }}/{{ data.totalCount || 0 }}</span>
        </div>
        <div class="container">
            <div class="item">
                <span class="label">{{ $t('components.linesSortCard.016453-1') }}</span>
                <span class="value">{{ data.lineCode }}</span>
            </div>
            <div class="item">
                <span class="label">{{ $t('components.linesSortCard.016453-2') }}</span>
                <span class="value">{{ `${data.drivers ? data.drivers[0].driverName : $t('components.linesSortCard.016453-3')}` }}</span>
            </div>
            <div class="item">
                <span class="label">{{ $t('components.linesSortCard.016453-4') }}</span>
                <span class="value">{{ data.goodsItemsNum }}</span>
            </div>
            <div class="item">
                <span class="label">{{ $t('components.linesSortCard.016453-5') }}</span>
                <span class="value">{{ data.deliveryDate.split(' ')[0] }}</span>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, toRefs } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";

const props = defineProps({
    data: {
        type: Object,
        default: () => { },
    },
});

const emits = defineEmits(["select"]);
const { data } = toRefs(props);

const status = computed(() => {
    return (
        data.value.sortedCount == 0 ? "UNSORTED" : data.value.sortedCount == data.value.totalCount ? "SORTED" : "SORTING"
    );
});

function handleItemTap(data) {
    console.log(data);
    emits("select", data);
}
</script>

<style lang="scss" scoped>
.card {
    cursor: pointer;
    width: calc((100% - 72px) / 4);
    display: flex;
    flex-direction: column;
    border: 1px solid var(---el-border-color-light, #E6EAF0);
    background: #fff;
    border-radius: 12px;

    .status {
        display: flex;
        height: 32px;
        padding: 0px 16px;
        justify-content: space-between;
        align-items: center;
        align-self: stretch;
        color: var(---el-color-white, #FFF);
        font-family: "PingFang SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 300;
        line-height: 24px;
        border-top-left-radius: 12px;
        border-top-right-radius: 12px;
    }

    .status-UNSORTED {
        background: #DB4646;
    }

    .status-SORTED {
        background: var(---el-color-primary, #059E84);
    }

    .status-SORTING {
        background: #2C85D7;
    }

    .container {
        display: flex;
        padding: 12px;
        flex-direction: column;
        align-items: flex-start;
        align-self: stretch;

        .item {
            display: flex;
            height: 32px;
            align-items: center;
            gap: 24px;
            align-self: stretch;

            .label {
                color: var(---el-text-color-regular, #505762);
                font-family: "PingFang SC";
                font-size: 18px;
                font-style: normal;
                font-weight: 300;
                width: 80px;
            }

            .value {
                color: var(---el-text-color-primary, #1C2026);
                font-family: "PingFang SC";
                font-size: 18px;
                font-style: normal;
                font-weight: 400;
            }
        }
    }
}
</style>
