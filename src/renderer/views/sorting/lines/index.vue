<template>
  <div class="page">
    <div class="search-bar">
      <el-icon @click="handleBack" size="28" color="#1C2026">
        <Back />
      </el-icon>
      <div class="search-bar-right">
        <el-form @submit.native.prevent>
          <el-input v-model="searchInput" ref="inputRef" class="search-input" :placeholder="$t('linesSorting.index.868009-0')"
            @focus="handleFocus" @blur="handleBlur" @keyup.enter="handleEnter">
            <template #prefix>
              <el-icon size="24" color="#7E8694">
                <Search />
              </el-icon>
            </template>
          </el-input>
        </el-form>
        <div class="search-row">
          <span class="search-label">{{ $t('linesSorting.index.868009-1') }}</span>
          <el-date-picker v-model="filterForm.deliveryDateRange" type="daterange" align="left" clearable :editable="false"
            :start-placeholder="$t('linesSorting.index.868009-2')" :end-placeholder="$t('linesSorting.index.868009-3')" value-format="YYYY-MM-DD" @change="handleDateChange">
          </el-date-picker>
        </div>
        <div class="search-row">
          <span class="search-label">{{ $t('linesSorting.index.868009-4') }}</span>
          <el-select clearable v-model="filterForm.warehouseId" remote reserve-keyword :placeholder="$t('linesSorting.index.868009-5')"
            :remote-show-suffix="true" @change="handleWarehouseChange" :filterable="false">
            <el-option v-for="item in filterCondition.warehouses" :key="item.warehouseId" :label="item.warehouseName"
              :value="item.warehouseId" />
          </el-select>
        </div>
      </div>
    </div>
    <div class="container">
      <div class="content" v-loading="loading">
        <div class="bt-select-st">
          <span v-for="spec in status"
            :class="filterForm.sortStatus == spec.value ? 'mr10 label-st-txt' : 'mr10 no-label-st-text'"
            @click="handleTabTap(spec)">{{ spec.label }}</span>
        </div>
        <div class="scroll-div">
          <div v-if="Object.keys(datas).length > 0" v-infinite-scroll="handleScroll" :infinite-scroll-delay="500"
            :infinite-scroll-immediate="true">
            <div class="group-content" v-for="[key, value] in Object.entries(datas)">
              <div class="group-title">{{ key.split(' ')[0] }}</div>
              <div class="group-list">
                <LinesSortCard v-for="item in value" :key="item.index" :data="item" @select="handleSelect">
                </LinesSortCard>
              </div>
            </div>
          </div>
          <div v-else>{{ $t('linesSorting.index.868009-6') }}</div>
        </div>
      </div>
    </div>
    <!-- <div style="position: absolute; width: 100%; bottom: 0px">
      <keyboard ref="keyboardRef" :input="searchInput" @onChange="handleChange"></keyboard>
    </div> -->
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from "vue";
import { userInfoT } from "@renderer/store/modules/template";
import { useRouter } from "vue-router";
import LinesSortCard from "./components/linesSortCard.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { defaultDeliveryTime, status } from "./constant";
import {
  queryGoodsList,
  fetchNewLineList,
  sortScheduleByDate,
} from "../../../api/sorting";
import dayjs from "dayjs";
import {
  filterForm,
  searchInput,
  isNeedHint,
  handleInitSearchDateTime,
} from "./filterParams";
import keyboard from "../../../components/keyboard.vue";
const useUserStore = userInfoT();
const router = useRouter();

const defaultProps = {
  children: "children",
  label: "lineName",
  value: "lineId",
};

const keyboardRef = ref(null);
const handleFocus = () => {};

const handleBlur = () => { };

const inputRef = ref(null);

const handleChange = (value) => {
  searchInput.value = value;
  // reLoad();
  // setTimeout(()=>{
  //   nextTick(()=>{
  //     inputRef.value.focus()
  //   })
  // },0)
};

const handleEnter = () => {
  console.log(searchInput.value);
  inputRef.value.blur();
  closeKeyBoard();
  reLoad();
};

function handleBack() {
  // 返回
  router.go(-1);
}

const filterCondition = ref({
  warehouses: []
}); // 筛选条件

// 获取筛选条件
async function getQueryCondition() {
  const res = await queryGoodsList(filterForm.value.shopId);
  filterCondition.value = { ...res.data.data };
  if (filterForm.value.warehouseId === "") {
    const warehouses = res.data.data?.warehouses || [];
    const defaultWarehouse = warehouses.find(({ defaultFlag }) => defaultFlag === true);
    filterForm.value.warehouseId = defaultWarehouse?.warehouseId || warehouses[0]?.warehouseId || undefined;
  }
  handleInitSearchDateTime();
}

function handleDateChange(value) {
  console.log(value);
  filterForm.value.deliveryDateRange = value;
  reLoad();
}

function handleWarehouseChange(value) {
  if (value.length === 0) {
    filterForm.value.warehouseId = null;
  } else {
    filterForm.value.warehouseId = value;
  }
  reLoad();
}

const pagingData = ref({
  total: 0,
  size: 16,
  current: 1,
});

// 查询发货日期是否存在未排线数据
async function getSortScheduleByDate() {
  if (!isNeedHint.value) return;
  // 提示有未排线分拣
  const { shopId } = filterForm.value || {};
  const res = await sortScheduleByDate({
    shopId: shopId,
    deliveryDate: `${filterForm.value.deliveryDate} 00:00:00`,
  });
  if (res?.data?.data) {
    ElMessageBox.confirm(
      i18n.global.t('linesSorting.index.868009-7', [filterForm.value.deliveryDate]),
      i18n.global.t('linesSorting.index.868009-8'),
      {
        cancelButtonText: i18n.global.t('linesSorting.index.868009-9'),
        showConfirmButton: false,
        type: "warning",
        customClass: "message-box-confirm",
      }
    ).catch(async () => {
      isNeedHint.value = false;
    });
  }
}
const loading = ref(false); // 列表loading
const isOpenPaging = ref(false); // 是否开启翻页

const datas = ref([]); // 列表数据

// 获取客户列表
async function getCustomerList(isScroll = false) {
  loading.value = true;
  const { shopId, ...restParams } = filterForm.value || {};

  let deliveryDateStart = '', deliveryDateEnd = '';
  if (filterForm.value.deliveryDateRange) {
    if (filterForm.value.deliveryDateRange.length === 2) {
      deliveryDateStart = `${filterForm.value.deliveryDateRange[0]} 00:00:00`;
      deliveryDateEnd = `${filterForm.value.deliveryDateRange[1]} 23:59:59`;
    } else {
      deliveryDateStart = `${filterForm.value.deliveryDate ? `${filterForm.value.deliveryDate} 00:00:00` : ''}`;
      deliveryDateEnd = `${filterForm.value.deliveryDate ? `${filterForm.value.deliveryDate} 23:59:59` : ''}`;
    }
  }

  try {
    const res = await fetchNewLineList({
      shopId: shopId,
      lineNameOrLineCode: searchInput.value || undefined,
      current: pagingData.value.current,
      size: pagingData.value.size,
      ascs: [],
      deliveryDateStart: deliveryDateStart,
      deliveryDateEnd: deliveryDateEnd,
      productType: filterForm.value.productType,
      sortStatus: filterForm.value.sortStatus,
      purchaseType: "ALL",
      goodsLack: "ALL",
      warehouseId: filterForm.value.warehouseId,
      posReqFlag: true,
    });
    // 使用requestAnimationFrame优化DOM更新
    window.requestAnimationFrame(() => {
      datas.value = (res?.data?.data || []).reduce((acc, item) => {
        const key = item.deliveryDate;
        if (!acc[key]) {
          acc[key] = [];
        }
        acc[key].push(item);
        return acc;
      }, {});
      pagingData.value.total = Number(res?.data?.data?.total) || 0;
      loading.value = false;
    });
  } catch (error) {
    if (isScroll) {
      pagingData.value.current = pagingData.value.current - 1;
    }
    loading.value = false;
  }
}

const reLoad = () => {
  // 重新加载
  datas.value = [];
  pagingData.value.current = 1;
  nextTick(() => {
    getCustomerList();
  });
};

// 添加节流函数
function throttle<T extends (...args: any[]) => any>(fn: T, delay: number) {
  let timer: number | null = null;
  return function (this: any, ...args: Parameters<T>) {
    if (timer) return;
    timer = window.setTimeout(() => {
      fn.apply(this, args);
      timer = null;
    }, delay);
  };
}

const handleScroll = throttle(handleScrollImpl, 200);

function handleScrollImpl() {
  console.log("handleScrollImpl");
  if (!isOpenPaging.value || loading.value) return;
  if (datas.value.length >= pagingData.value.total) {
    // ElMessage.warning('没有更多了')
    return;
  }
  pagingData.value.current = pagingData.value.current + 1;
  getCustomerList(true);
}

function handleTabTap(type) {
  console.log(type);
  filterForm.value.sortStatus = type.value;
  console.log(filterForm.value);
  reLoad();
}

function handleSelect(data) {
  // 选择商品进入称重页面

  router.push({
    path: "/home/<USER>",
    query: {
      ...data,
      deliveryDate: data.deliveryDate.split(" ")[0],
      warehouseId: filterForm.value.warehouseId,
    },
  });
}

onMounted(async () => {
  await getQueryCondition();
  getSortScheduleByDate();
  reLoad();
});
</script>

<script lang="ts">
import { defineComponent } from "vue";
import { initFilterForm } from "./filterParams";
import { closeKeyBoard } from "@renderer/utils/util";
import { i18n } from "@renderer/i18n";
export default defineComponent({
  beforeRouteEnter(to, from, next) {
    //to当前路由，from从哪个路由来
    //这里的vm指的就是vue实例，可以用来当做this使用
    next(() => {
      if (from.path == "/home/<USER>") {
        initFilterForm();
      }
    });
  },
});
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: #a6b6c8;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
}

.search-bar {
  display: flex;
  align-items: center;
  padding: 20px 20px 16px 20px;
  z-index: 21;
  gap: 24px;
  background: #a6b6c8;

  ::v-deep .el-button {
    display: inline-flex;
    box-sizing: border-box;
    height: auto;
    line-height: 22px;
    padding: 12px 24px;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    border-radius: 12px;
    border: none;
    background: #fff;
    color: var(--el-text-color-primary, #1c2026);
    font-family: PingFang SC;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;

    &+.el-button {
      margin-left: 0;
    }

    &>span {
      gap: 4px;
    }
  }

  .search-bar-right {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 24px;

    ::v-deep .el-form {
      display: flex;
      flex: 1;
      max-width: 554px;
    }

    .search-input {
      height: 48px;
      font-size: 18px;

      ::v-deep .el-input__wrapper {
        border-radius: 12px;
        border: 1px solid var(--el-border-color-base, #cdd2da);
        background: var(--el-color-white, #f6f7f9);
        padding: 1px 20px;

        &.is-focus {
          // border: 1px solid var(--el-border-color-base, #cdd2da);
          box-shadow: none !important;
        }
      }
    }

    .search-row {
      display: flex;
      align-items: center;
      gap: 8px;

      ::v-deep .el-input__wrapper {
        height: 48px;
        border-radius: 12px;
        border: 1px solid var(---el-border-color-base, #CDD2DA);
        background: var(---el-color-white, #FFF);
      }

      ::v-deep .el-range-input {
        font-family: "PingFang SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 300;
        line-height: 22px;
      }

      ::v-deep .el-input__inner {
        font-family: "PingFang SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 300;
        line-height: 22px;
      }

      .search-label {
        color: var(---el-text-color-regular, #505762);
        font-family: "PingFang SC";
        font-size: 18px;
        font-style: normal;
        font-weight: 300;
      }
    }
  }
}

.container {
  display: flex;
  flex: 1;
  overflow: hidden;

  .content {
    flex: 1;
    padding: 20px;
    background: #C1CDDA;
    border: 1px solid #FFF;
    border-radius: 12px;
    overflow: hidden;

    .scroll-div {
      height: calc(100vh - 250px);
      overflow-y: auto;
      margin-top: 20px;

      .group-content {
        gap: 20px;

        .group-title {
          color: var(---el-text-color-regular, #505762);
          font-family: "PingFang SC";
          font-size: 18px;
          font-style: normal;
          font-weight: 300;
          padding: 10px 0;
        }

        .group-list {
          flex-wrap: wrap;
          display: flex;
          gap: 24px;
        }
      }
    }
  }
}

.bt-select-st {
  display: flex;

  .label-st-txt {
    cursor: pointer;
    padding: 8px 24px;
    color: var(---el-color-white, #FFF);
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 300;
    line-height: 24px;
    /* 150% */
    border-radius: 8px;
    border: 1px solid var(---el-color-primary-light-4, #038A78);
    background: var(---el-color-primary, #059E84);
  }

  .no-label-st-text {
    cursor: pointer;
    padding: 8px 24px;
    color: var(---el-text-color-primary, #1C2026);
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 300;
    line-height: 24px;
    /* 150% */
    border-radius: 8px;
    border: 1px solid var(---el-color-primary, #059E84);
    background: var(---el-color-primary-light-10, #E8FFF7);
  }
}

.mr10 {
  margin-right: 10px;
}

.mr20 {
  margin-right: 20px;
}

.mr5 {
  margin-right: 5px;
}

::v-deep .filter-dialog {
  z-index: 20;
  position: relative;

  .model-content {
    height: calc(100vh - 124px);
    position: absolute !important;
  }
}
</style>
<style lang="scss">
.message-box-confirm {

  .el-button:focus,
  .el-button:hover {
    color: #059e84;
    border-color: #79d8bf;
    background-color: #e8fff7;
  }

  .el-button--primary {
    background: #009f64 !important;
    color: #fff !important;
    border-color: #009f64 !important;
  }

  .el-button--primary:hover {
    background: rgb(55, 177, 157);
    color: #fff;
    border-color: rgb(55, 177, 157);
  }

  .el-message-box__headerbtn:hover .el-message-box__close {
    color: #009f64;
  }
}
</style>
