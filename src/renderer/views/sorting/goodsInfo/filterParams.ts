import { ref, watch } from 'vue'
import { STATUS, SPECIMEN } from './constant';
import { userInfoT } from "@renderer/store/modules/template";
const useUserStore = userInfoT()

export const filterForm = ref({
  sortStatus: STATUS.UNSORTED,
  productType: SPECIMEN.ALL,
  warehouseId: '',
  deliveryDate: null,
  shopId: useUserStore?.getShopList?.[0]?.id || undefined
}); // 筛选表单
export const easyCode = ref(''); // 助记码
export const currentGoodsType = ref(null); // 当前选择商品类型
export const isPrintReceipt = ref(false); // 是否不打印小票
export const isNeedHint = ref(true); // 是否需要提示
watch(() => filterForm.value.deliveryDate, () => {
  isNeedHint.value = true
})
export const initFilterForm = () => {
  filterForm.value = {
    sortStatus: STATUS.UNSORTED,
    productType: SPECIMEN.ALL,
    warehouseId: '',
    deliveryDate: null,
    shopId: useUserStore?.getShopList?.[0]?.id || undefined
  }
  easyCode.value = '';
  currentGoodsType.value = null;
  isPrintReceipt.value = false
}