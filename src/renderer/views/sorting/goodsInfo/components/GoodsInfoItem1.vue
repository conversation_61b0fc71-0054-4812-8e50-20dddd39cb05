<template>
  <div class="goods-info">
    <div class="btn" :class="[`btn-${data.bgType}`]" @click="setInputFocus">
      <div class="btn-left">
        <div class="goods-img">
          <img :src="data.picUrl" alt="" class="pic" />
        </div>
        <div class="goods-item-info">
          <div class="customer-name">
            <label class="gird-font-st" v-if="data.tempCode != null"
              >[{{ data.tempCode }}]</label
            >
            <label class="gird-font-st">{{ data.customerName }}</label>
          </div>
          <div class="address">
            {{ data.address }}
          </div>
          <div class="grid-font-weigh">
            <label>{{ data.showOrderCount }}</label>
          </div>
          <div style="min-height: 48px">
            <div style="width: 100%; display: flex; justify-content: center;height:30px">
              <label class="gird-font-st">{{ data.showUnitConvert }}</label>
            </div>
            <div
              style="
                width: 100%;
                display: flex;
                justify-content: flex-start;
                margin-top: 5px;
              "
            >
              <label class="gird-font-st">{{ $t('components.GoodsInfoItem1.038954-0') }}{{ data.showWeight }}</label>
            </div>
          </div>
          <div
            style="
              width: 100%;
              align-items: center;
              display: flex;
              justify-content: center;
            "
          >
            <label class="gird-font-st">{{ data.orderTab }}</label>
          </div>
          <el-input
            :id="`${data.sortId}`"
            class="ofcues-style"
            @blur="focusClose"
            v-model="inputWeigh"
            @change="weighChange"
            oninput="value = value.replace(/[^\d|\.]/g, '').replace(/^[eE]/, '').replace(/[eE][+\-]?[0-9]*/, '')"
          />
        </div>
      </div>

      <div class="btn-right" @click="clickItemOption()">
        <span class="content-text">{{ $t('components.GoodsInfoItem1.038954-1') }}</span>
      </div>
    </div>
  </div>
  <el-drawer v-model="isShowDrawer" :show-close="false" size="42%">
    <div class="drawer-content-page">
      <div class="drawe-content-st">
        <span
          >{{ prodcutName }}【{{ data.tempCode }}{{ data.customerName }}】</span
        >
        <SvgIcon
          name="cancel"
          class="svg-size"
          @click="isShowDrawer = false"
        ></SvgIcon>
      </div>
      <div class="drawe-bt-st">
        <div class="st-button" @click="printOrSortBtn(true, orderWeight)">
          {{ $t('components.GoodsInfoItem1.038954-2') }}
        </div>
        <div class="st-button" @click="printOrSortBtn(false, orderWeight)">
          {{ $t('components.GoodsInfoItem1.038954-3') }}
        </div>
        <div class="st-button" @click="sendSortLack()">{{ $t('components.GoodsInfoItem1.038954-4') }}</div>
        <div class="st-button" @click="resetView()">{{ $t('components.GoodsInfoItem1.038954-5') }}</div>
      </div>
      <div class="bt-st-number">
        <label>{{ $t('components.GoodsInfoItem1.038954-6') }}</label>
        <label style="color: #ff7d00">{{ data.showOrderCount }}</label>
      </div>
      <el-input
        class="sorting-number-input"
        @input="handleInput"
        ref="refInput"
        v-focus
        clearable
        v-model="orderWeight"
        :placeholder="$t('components.GoodsInfoItem1.038954-7')"
      ></el-input>
      <el-row :gutter="8">
        <el-col :span="6"
          ><div class="grid-content" @click="keyboardGrid('1')">1</div></el-col
        >
        <el-col :span="6"
          ><div class="grid-content" @click="keyboardGrid('2')">2</div></el-col
        >
        <el-col :span="6"
          ><div class="grid-content" @click="keyboardGrid('3')">3</div></el-col
        >
        <el-col :span="6"
          ><div class="grid-content" @click="keyboardGrid('X')">
            <SvgIcon name="key-back"></SvgIcon></div
        ></el-col>
      </el-row>
      <el-row :gutter="8">
        <el-col :span="6"
          ><div class="grid-content" @click="keyboardGrid('4')">4</div></el-col
        >
        <el-col :span="6"
          ><div class="grid-content" @click="keyboardGrid('5')">5</div></el-col
        >
        <el-col :span="6"
          ><div class="grid-content" @click="keyboardGrid('6')">6</div></el-col
        >
        <el-col :span="6"
          ><div class="grid-content-font" @click="keyboardGrid('clear')">
            {{ $t('components.GoodsInfoItem1.038954-8') }}
          </div></el-col
        >
      </el-row>
      <el-row :gutter="8">
        <el-col :span="6"
          ><div class="grid-content" @click="keyboardGrid('7')">7</div></el-col
        >
        <el-col :span="6"
          ><div class="grid-content" @click="keyboardGrid('8')">8</div></el-col
        >
        <el-col :span="6"
          ><div class="grid-content" @click="keyboardGrid('9')">9</div></el-col
        >
        <el-col :span="6"
          ><div
            class="grid-content"
            style="font-weight: 550"
            @click="keyboardGrid('.')"
          >
            ·
          </div></el-col
        >
      </el-row>
      <el-row :gutter="8">
        <el-col :span="6"
          ><div class="grid-content-font" @click="keyboardGrid('back')">
            {{ $t('components.GoodsInfoItem1.038954-9') }}
          </div></el-col
        >
        <el-col :span="6"
          ><div class="grid-content" @click="keyboardGrid('0')">0</div></el-col
        >
        <el-col :span="12"
          ><div class="grid-finsh-size" @click="keyboardGrid('finish')">
            {{ $t('components.GoodsInfoItem1.038954-10') }}
          </div></el-col
        >
      </el-row>
    </div>
  </el-drawer>
</template>
<script setup lang="ts">
import { ref, toRefs, onMounted } from "vue";
import { sortLack, printSort, reSetSort } from "@renderer/api/login";
import { ElMessage, ElMessageBox } from "element-plus";
import { userInfoT } from "@renderer/store/modules/template";
import { invoke } from "../../../utils/ipcRenderer";
import { IpcChannel } from "../../../../ipc";
import { nextTick } from "process";
import { i18n } from "@renderer/i18n";
let props = defineProps({
  data: {
    type: Object,
    default: {},
  },
  datList: {
    type: Array,
    default: [],
  },
  selectIndex: {
    type: Number,
    default: "",
  },
  prodcutName: {
    type: String,
    default: "",
  },
  isPrint: {
    type: Boolean,
    default: false,
  },
});
const { data } = toRefs(props);
const userInfoTState = userInfoT();
onMounted(() => {
  //设置第一个
  setTimeout(() => {
    if (props.selectIndex != -1) {
      nextTick(() => {
        document
          .getElementById(props.datList[props.selectIndex].sortId)
          .focus();
      });
    }
  }, 300);
});
// 订单重量
let orderWeight = ref("");
const handleInput = (event) => {
  let value = event;
  if (props.data.whetherStandard == "NO") {
    value =
      value
        .replace(/[^\d^\.]+/g, "")
        .replace(/^0+(\d)/, "$1")
        .replace(/^\./, "0.")
        .match(/^\d*(\.?\d{0,2})/g)[0] || "";
  } else {
    // value = value.replace(/[^\d|]/g, '').replace(/^[eE]/, '').replace(/[eE][+\-]?[0-9]*/, '')
  }
  if (value.length > 8) {
    value = value.slice(0, 8);
  }
  orderWeight.value = value.trim();
  // if(props.data.whetherStandard == 'YES' && !Number.isInteger(parseFloat(orderWeight.value)) && orderWeight.value != ''){
  //     ElMessage.error(i18n.global.t('components.GoodsInfoItem1.038954-11'));
  //     orderWeight.value = ''
  // }
};
// 操作模块
const refInput = ref(null);
let isShowDrawer = ref(false);
const emit = defineEmits(["clickItemOption"]);
const clickItemOption = () => {
  isShowDrawer.value = true;
  orderWeight.value = "";
  setTimeout(() => {
    nextTick(() => {
      refInput.value.focus();
    });
  }, 400);
};
// 点击div获取焦点
function setInputFocus() {
  if (props.data.bgType != "disable") {
    props.data.bgType = "progress";
    nextTick(() => {
      document.getElementById(props.data.sortId).focus();
    });
  }
}
//input失去焦点时执行的方法
const inputWeigh = ref("");
function focusClose() {
  if (props.data.bgType == "progress") {
    props.data.bgType = "available";
  }
}

// 值改变---发送请求
function weighChange() {
  if (
    props.data.whetherStandard == "YES" &&
    !Number.isInteger(parseFloat(inputWeigh.value))
  ) {
    inputWeigh.value = "";
    ElMessage.error(i18n.global.t('components.GoodsInfoItem1.038954-11'));
    return;
  }
  printOrSortBtn(true, inputWeigh.value);
}

const keyboardGrid = async (val: string) => {
  if (val == "clear") {
    orderWeight.value = "";
    nextTick(() => {
      refInput.value.focus();
    });
  } else if (val == "X" && val.length > 0) {
    orderWeight.value = orderWeight.value.substring(
      0,
      orderWeight.value.length - 1
    );
    nextTick(() => {
      refInput.value.focus();
    });
  } else if (val == "back") {
    orderWeight.value = "";
    isShowDrawer.value = false;
    nextTick(() => {
      refInput.value.focus();
    });
  } else if (val == ".") {
    if (
      orderWeight.value.length > 0 &&
      orderWeight.value.indexOf(".") < 0 &&
      props.data.whetherStandard == "NO"
    ) {
      orderWeight.value = orderWeight.value + val;
    }
    nextTick(() => {
      refInput.value.focus();
    });
  } else if (val == "finish") {
    printOrSortBtn(true, orderWeight.value);
  } else {
    // 标品开头不允许输入  0
    if (
      orderWeight.value.length == 0 &&
      props.data.whetherStandard == "YES" &&
      val == "0"
    ) {
      // orderWeight.value = orderWeight.value + val
    } else if (
      orderWeight.value.length > 0 &&
      orderWeight.value.indexOf(".") != -1
    ) {
      //小数点不能超过三位
      let arr = orderWeight.value.split(".");
      if (arr[1].length < 2) {
        orderWeight.value = orderWeight.value + val;
      }
    } else {
      orderWeight.value = orderWeight.value + val;
    }
    nextTick(() => {
      refInput.value.focus();
    });
  }
};

/**
 * 订单状态
 */
async function printOrSortBtn(val: boolean, proWeigh: string) {
  //判断是否  配置过打印机
  if (props.isPrint && !val) {
    ElMessage.warning(i18n.global.t('components.GoodsInfoItem1.038954-12'));
    return;
  }
  //判断是否  配置过打印机
  if (!userInfoTState.getPrinterName && !props.isPrint) {
    ElMessage.warning(i18n.global.t('components.GoodsInfoItem1.038954-13'));
    return;
  }
  let dataParm = {
    sortId: props.data.sortId,
    sort: val, // true  分拣   false  打印
    sortQuantity: proWeigh,
  };
  if (val) {
    if (!proWeigh) {
      ElMessage.error(i18n.global.t('components.GoodsInfoItem1.038954-14'));
      return;
    }
    if (
      props.data.whetherStandard == "YES" &&
      !Number.isInteger(parseFloat(proWeigh))
    ) {
      ElMessage.error(i18n.global.t('components.GoodsInfoItem1.038954-11'));
      orderWeight.value = "";
      nextTick(() => {
        refInput.value.focus();
      });
      return;
    }
    if (props.data.orderStatus === "110" || props.data.orderStatus === "120") {
      //判断是否二次分拣
      if (props.data.bgType === "disable") {
        ElMessageBox.confirm(
          i18n.global.t('components.GoodsInfoItem1.038954-15'),
          i18n.global.t('components.GoodsInfoItem1.038954-16'),
          {
            confirmButtonText: i18n.global.t('components.GoodsInfoItem1.038954-17'),
            cancelButtonText: i18n.global.t('components.GoodsInfoItem1.038954-18'),
            type: "warning",
            customClass: "message-box-confirm",
          }
        ).then(() => {
          sendPrintSort(dataParm);
        });
      } else {
        sendPrintSort(dataParm);
      }
    } else {
      inputWeigh.value = "";
      ElMessage.error(i18n.global.t('components.GoodsInfoItem1.038954-19'));
      return;
    }
  } else {
    sendPrintSort(dataParm);
  }
}

async function sendPrintSort(dataParm) {
  await printSort(dataParm).then((res) => {
    //开始打印
    if (!props.isPrint) {
      if (res.data != null && res.data.data != null) {
        sendPrintStart(res.data.data);
      } else {
        ElMessage.success(i18n.global.t('components.GoodsInfoItem1.038954-20'));
      }
    }
    //如果是打印，不切换颜色
    if (!dataParm.sort) {
      // 打印
      //TODO
    } else {
      // 分拣
      if (orderWeight.value && orderWeight.value != "") {
        props.data.showWeight = orderWeight.value + props.data.baseUnit;
      } else {
        props.data.showWeight = inputWeigh.value + props.data.baseUnit;
      }
      //颜色
      if (inputWeigh.value && props.data.orderCount > inputWeigh.value) {
        props.data.bgType = "disableRed";
      } else if (
        orderWeight.value &&
        props.data.orderCount > orderWeight.value
      ) {
        props.data.bgType = "disableRed";
      } else {
        props.data.bgType = "disable";
      }
    }
    //清空
    inputWeigh.value = "";
    orderWeight.value = "";
    isShowDrawer.value = false;
    //延迟设置下一个
    setTimeout(() => {
      let slectFalg = true;
      props.datList.forEach((item, index) => {
        if (item.bgType != "disable" && slectFalg) {
          props.datList[index].bgType = "progress";
          nextTick(() => {
            document.getElementById(props.datList[index].sortId).focus();
          });
          slectFalg = false;
        }
      });
    }, 800);
  });
}
// 开启打印
async function sendPrintStart(val) {
  nextTick(() => {
    userInfoTState.setPerinterObj(JSON.stringify(val));
    invoke(IpcChannel.OpenWin, { url: "/PrintPage" });
  });
}
// 标记缺货
async function sendSortLack() {
  if (props.data.orderStatus === "110" || props.data.orderStatus === "120") {
    if (props.data.bgType === "disable") {
      ElMessageBox.confirm(
        i18n.global.t('components.GoodsInfoItem1.038954-21'),
        i18n.global.t('components.GoodsInfoItem1.038954-16'),
        {
          confirmButtonText: i18n.global.t('components.GoodsInfoItem1.038954-17'),
          cancelButtonText: i18n.global.t('components.GoodsInfoItem1.038954-18'),
          type: "warning",
          customClass: "message-box-confirm",
        }
      ).then(() => {
        sortLackSend();
      });
    } else {
      sortLackSend();
    }
  } else {
    inputWeigh.value = "";
    ElMessage.error(i18n.global.t('components.GoodsInfoItem1.038954-19'));
    return;
  }
}
// 重置
async function resetView() {
  if (props.data.orderStatus === "110" || props.data.orderStatus === "120") {
    await reSetSort(props.data.sortId).then((res) => {
      isShowDrawer.value = false;
      props.data.bgType = "available";
      props.data.showWeight = "0.0";
      let slectFalg = true;
      props.datList.forEach((item, index) => {
        if (item.bgType != "disable" && slectFalg) {
          props.datList[index].bgType = "progress";
          nextTick(() => {
            document.getElementById(props.datList[index].sortId).focus();
          });
          slectFalg = false;
        }
      });
    });
  } else {
    inputWeigh.value = "";
    ElMessage.error(i18n.global.t('components.GoodsInfoItem1.038954-19'));
    return;
  }
}

async function sortLackSend() {
  let sortId = props.data.sortId;
  await sortLack(sortId).then((res) => {
    if (res.data) {
      inputWeigh.value = "";
      orderWeight.value = "";
      isShowDrawer.value = false;
      props.data.bgType = "disable";
      //延迟设置下一个
      setTimeout(() => {
        let slectFalg = true;
        props.datList.forEach((item, index) => {
          if (item.bgType != "disable" && slectFalg) {
            props.datList[index].bgType = "progress";
            nextTick(() => {
              document.getElementById(props.datList[index].sortId).focus();
            });
            slectFalg = false;
          }
        });
      }, 400);
      ElMessage.success(i18n.global.t('components.GoodsInfoItem1.038954-22'));
    } else {
      ElMessage.error(i18n.global.t('components.GoodsInfoItem1.038954-23'));
    }
  });
}

const getImgUrl = (data) => {};
</script>
<style rel="stylesheet/scss" lang="scss">
.el-row {
  margin-bottom: 8px;
  &:last-child {
    margin-bottom: 0;
  }
}
.el-col {
  border-radius: 4px;
}
.el-drawer__header {
  margin-bottom: 0;
  padding: 0;
}
</style>
<style rel="stylesheet/scss" lang="scss" scoped>
.drawer-content-page {
  .drawe-content-st {
    .svg-size {
      width: 40px;
      height: 40px;
    }
    .svg-size:hover {
      background: var(--el-border-color-lighter, #f0f2f5);
    }
    display: flex;
    justify-content: space-between;
    span {
      color: var(--el-text-color-primary, #1c2026);
      font-family: PingFang SC;
      font-size: 24px;
      font-style: normal;
      font-weight: 600;
    }
  }
  .drawe-bt-st {
    display: flex;
    margin-top: 28px;
    .st-button {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 16px;
      height: 50px;
      border-radius: 12px;
      border: 1px solid #009f64;
      background: #009f64;
      padding: 0px 24px 0px 24px;
      color: var(--el-color-white, #fff);
      font-family: PingFang SC;
      font-size: 24px;
      font-style: normal;
      font-weight: 400;
    }
    .st-button:hover {
      border: 1px solid #009f64;
      background: linear-gradient(
          0deg,
          rgba(0, 0, 0, 0.15) 0%,
          rgba(0, 0, 0, 0.15) 100%
        ),
        #009f64;
    }
  }
  .bt-st-number {
    margin-top: 16px;
    margin-bottom: 20px;
    color: var(--el-text-color-regular, #505762);
    font-family: PingFang SC;
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
  }
  .grid-content {
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
    min-height: 64px;
    background: #f0f2f5;
    color: var(--el-text-color-primary, #1c2026);
    text-align: center;
    font-family: Avenir;
    font-size: 36px;
    font-style: normal;
    font-weight: 400;
  }
  .grid-content:hover {
    background: var(--el-border-color-base, #cdd2da);
  }
}
.grid-content-font {
  color: var(--el-text-color-primary, #1c2026);
  text-align: center;
  font-family: PingFang SC;
  font-size: 28px;
  font-style: normal;
  font-weight: 400;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  min-height: 64px;
  background: var(--el-border-color-lighter, #f0f2f5);
}
.grid-content-font:hover {
  background: var(--el-border-color-base, #cdd2da);
}
.grid-finsh-size {
  color: #009f64;
  font-family: PingFang SC;
  font-size: 28px;
  font-style: normal;
  font-weight: 400;
  display: flex;
  height: 64px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  flex: 1 0 0;
  border-radius: 4px;
  background: rgba(0, 159, 100, 0.1);
}
.grid-finsh-size:hover {
  color: var(--el-color-white, #fff);
  background: #009f64;
}
.sorting-number-input {
  margin-bottom: 22px;
  height: 64px;
  font-family: PingFang SC;
  font-size: 24px;
  font-style: normal;
  font-weight: 400;
  ::v-deep(.el-input__wrapper) {
    border-radius: 12px;
    border: 1px solid var(--el-border-color-base, #009f64);
    box-shadow: 0 0 0 1px #009f64 !important;
    background: var(--el-color-white, #fff);
    &.is-focus {
      box-shadow: 0 0 0 1px #009f64 !important;
    }
  }
}
.goods-info {
  width: calc((100% - 80px) / 3);
  display: flex;
  flex-direction: column;
  background: #f0f2f5;
  .btn {
    display: flex;
    height: 230px;
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    letter-spacing: -0.01px;
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.15);
    .btn-left {
      width: calc(100% - 95px);
      height: 100%;
      align-items: center;
      display: flex;
      .goods-img {
        height: 100%;
        img {
          width: 230px;
          height: 100%;
        }
      }
      .goods-item-info {
        flex: 1;
        padding: 20px;
        box-sizing: border-box;
        .address {
          font-weight: bold;
          min-height: 30px;
          font-size: 14px;
        }
      }
      .customer-name {
        width: 100%;
        display: flex;
        justify-content: flex-start;
        font-weight: bold;
      }
    }
    .btn-right {
      width: 95px;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      border-left: 1px solid var(--el-border-color-light, #e6eaf0);
      background: #fff;
      .content-text {
        padding-top: 25px;
        color: #009f64;
        letter-spacing: 30px;
        writing-mode: vertical-rl;
        font-family: PingFang SC;
        font-size: 24px;
        font-style: normal;
        font-weight: 600;
      }
    }
    .grid-font-weigh {
      text-align: center;
      font-family: PingFang SC;
      font-size: 36px;
      font-style: normal;
      font-weight: 600;
      align-items: center;
      display: flex;
      justify-content: center;
      margin-top: 10px;
    }

    &-progress {
      color: #fff;
      background: #00b42a;
      .gird-font-st {
        color: #fff;
      }
    }

    &-disable {
      color: #fafbfc;
      background: #505762;
      .gird-font-st {
        color: #fafbfc;
      }
    }
    &-disableRed {
      color: red;
      background: #505762;
      .gird-font-st {
        color: red;
      }
    }
    //background: #CDD2DA;
    &-available {
      color: #1c2026;
      background: #fff;
      .gird-font-st {
        color: #1c2026;
      }
    }
  }

  .gird-font-st {
    font-family: PingFang SC;
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    text-align: center;
  }
  .ofcues-style {
    width: 10px;
    height: 10px;
    caret-color: transparent;
    ::v-deep(.el-input__wrapper) {
      border-radius: 0px;
      border: 1px solid transparent;
      box-shadow: 0 0 0px 0px transparent !important;
      background-color: transparent !important;
      &.is-focus {
        box-shadow: 0 0 0px 0px transparent !important;
      }
    }
    ::v-deep(.el-input__inner) {
      color: transparent !important;
    }
  }
}

.row-pl {
  margin: 0px;
}
</style>
