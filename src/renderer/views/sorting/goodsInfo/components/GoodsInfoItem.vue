<template>
  <div class="goods-info" :class="[`btn-${data.bgType}`]">
    <div class="content" @click="handleSelect">
      <div class="temp-code">{{ data.tempCode }}</div>
      <div class="order-count">
        <div class="weight-num">{{ data.showOrderCount }}</div>
        <div class="unit">{{ data.showUnitConvert }}</div>
      </div>
      <div class="weight info">
        <div class="label">{{ $t('components.GoodsInfoItem.684955-0') }}</div>
        <div class="value">{{ data.showWeight || '-' }}</div>
      </div>
      <div class="remark info">
        <div class="label">{{ $t('components.GoodsInfoItem.684955-1') }}</div>
        <div class="value">{{ showOrderTab }}</div>
      </div>
    </div>
    <div class="btn-wrp" @click="handleAction">{{ $t('components.GoodsInfoItem.684955-2') }}</div>
  </div>
</template>
<script setup lang="ts">
import { computed, toRefs } from "vue";
let props = defineProps({
  data: {
    type: Object,
    default: {},
  },
  datList: {
    type: Array,
    default: [],
  },
  selectIndex: {
    type: Number,
    default: "",
  },
  prodcutName: {
    type: String,
    default: "",
  },
  isPrint: {
    type: Boolean,
    default: false,
  },
});
const { data } = toRefs(props);

const emit = defineEmits(["clickItem", "selectItem"]);

const handleAction = (event: MouseEvent) => {
  event.stopPropagation();
  emit('clickItem', data.value)
};

const handleSelect = () => {
  emit('selectItem', data.value);
};

const showOrderTab = computed(() => {
  if(data.value.orderTab || data.value.orderTab === '') return '-';
  return data.value.orderTab;
});

</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.goods-info {
  width: 360px;
  height: 168px;
  display: flex;
  border-radius: 12px;
  border: 1px solid #fff;
  cursor: pointer;

  .content {
    flex: 1;
    height: 100%;
    padding: 12px 16px;
    background: #fff;
    border-radius: 12px 0 0 12px;

    .temp-code {
      color: #1C2026;
      font-family: "PingFang SC";
      font-size: 18px;
      font-style: normal;
      font-weight: 300;
      line-height: 26px;
    }

    .order-count {
      margin: 8px 0;
      display: flex;
      align-items: flex-end;
      justify-content: center;

      .weight-num {
        color: #1C2026;
        font-family: DINPro;
        font-size: 40px;
        font-style: normal;
        font-weight: 500;
        line-height: 46px;
      }

      .unit {
        color: #1C2026;
        font-family: "PingFang SC";
        font-size: 20px;
        font-style: normal;
        font-weight: 400;
        line-height: 28px;
      }
    }

    .info {
      display: flex;
      align-items: center;
      gap: 24px;

      &.weight {
        margin-bottom: 4px;
      }

      .label {
        color: #505762;
        font-family: "PingFang SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 300;
        line-height: 24px;
        flex-shrink: 0;
      }

      .value {
        color: #505762;
        font-family: "PingFang SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 300;
        line-height: 24px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  .btn-wrp {
    width: 56px;
    height: 100%;
    display: flex;
    padding: 8px 16px 12px 16px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    align-self: stretch;
    color: #1C2026;
    font-family: "PingFang SC";
    font-size: 24px;
    font-style: normal;
    font-weight: 400;
    line-height: 32px;
    background: #e6eaf0;
    border-radius: 0 12px 12px 0;
    cursor: pointer;
  }

  &.btn-active {
    border: 1px solid #038A78;

    .content {
      background: #038A78;

      .temp-code,
      .weight-num,
      .unit,
      .info .label,
      .info .value {
        color: #fff;
      }
    }

    .btn-wrp {
      background: #e8fff7;
      color: #25b195;
    }
  }
}
</style>
