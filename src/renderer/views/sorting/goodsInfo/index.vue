<template>
  <div class="common-layout">
    <div class="search-bar">
      <el-icon @click="pageBack" size="28" color="#1C2026">
        <Back />
      </el-icon>
      <div class="search-bar-right">
        <el-form @submit.native.prevent>
          <el-input
            v-model="searchVuale"
            @focus="handleFocus"
            ref="inputRef"
            @keyup.enter="seachChage"
            :placeholder="$t('sortingGoodsInfo.index.093127-0')"
            class="search-input"
          >
            <template #prefix>
              <el-icon size="24" color="#7E8694">
                <Search />
              </el-icon>
            </template>
          </el-input>
        </el-form>
        <el-button @click="handleOneKeySorting(true)">
          <SvgIcon name="sort"></SvgIcon>
          {{ $t("sortingGoodsInfo.index.093127-1") }}
        </el-button>
        <el-button @click="handleOneKeySorting(false)">
          <SvgIcon name="print"></SvgIcon
          >{{ $t("sortingGoodsInfo.index.093127-2") }}
        </el-button>
        <div class="divider"></div>
        <el-button @click="changePrint()">
          <SvgIcon name="print" class="mr5"></SvgIcon
          >{{ $t("sortingGoodsInfo.index.093127-3") }}
        </el-button>
      </div>
    </div>

    <div class="content">
      <div class="div-child-st">
        <div>
          <span class="txt-left-size">{{ prodcutName }}</span>
          <span class="txt-left-num"
            >({{ $t("sortingGoodsInfo.index.093127-4") }}{{ stage }})</span
          >
        </div>
        <div class="txt-right-num">
          <el-checkbox
            v-model="isPrint"
            :label="true"
            @click.prevent="radioOnClick(isPrint)"
          >
            {{ $t("sortingGoodsInfo.index.093127-5") }}
          </el-checkbox>
        </div>
      </div>

      <div class="bt-select-st">
        <span
          :class="searchParam == '' ? 'label-st-txt' : 'no-label-st-text'"
          @click="getGoodsItemList('', false, 1)"
          >{{ $t("sortingGoodsInfo.index.093127-6") }}</span
        >
        <span
          v-for="spec in specInfos"
          :class="
            searchParam == spec ? 'ml5 label-st-txt' : 'ml5 no-label-st-text'
          "
          @click="getGoodsItemList(spec, false, 1)"
          >{{ spec }}</span
        >
      </div>

      <div
        class="goods-item-list"
        v-infinite-scroll="handleScroll"
        :infinite-scroll-delay="500"
        :infinite-scroll-distance="10"
        :infinite-scroll-immediate="true"
      >
        <GoodInfoItem
          v-for="item in goodsItemList"
          :isPrint="isPrint"
          :datList="goodsItemList"
          :selectIndex="indexSelect"
          :data="item"
          :prodcutName="prodcutName"
          @clickItem="clickItem"
          @selectItem="selectItem"
          ></GoodInfoItem>
      </div>
    </div>

    <!-- <div style="position: absolute; width: 100%; bottom: 0px">
      <keyboard
        ref="keyboardRef"
        :input="searchVuale"
        @onChange="handleChange"
      ></keyboard>
    </div> -->

    <PrinterDialogSelect
      ref="PrinterDialogSelectRef"
      :printerList="printerList"
    />
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import { invoke } from "../../../utils/ipcRenderer";
import { IpcChannel } from "../../../../ipc";
import { userInfoT } from "@renderer/store/modules/template";
import GoodInfoItem from "./components/GoodsInfoItem.vue";
import {
  goodsDetailPage,
  batchPrintSort,
  sortFinsh,
} from "@renderer/api/login";
import { filterForm, easyCode, currentGoodsType } from "./filterParams";
import { useRouter, useRoute } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import PrinterDialogSelect from "../../../components/PrinterDialogSelect.vue";
import keyboard from "../../../components/keyboard.vue";
import { i18n } from "@renderer/i18n";

const requestLoading = ref(false);
const isOpenPaging = ref(false); // 是否开启翻页
const goodsItemList = ref([]); // 商品列表
const userInfoTState = userInfoT();
const router = useRouter();
// 初始化传递参数
const route = useRoute();
const typeName =
  route.query.typeName || i18n.global.t("sortingGoodsInfo.index.093127-7");
const specInfos = route.query.specInfos;
const prodcutName = route.query.spuName;

const stage = route.query.showAvailableStock;
//分页
const pagingData = ref({
  total: 0,
  size: 16,
  current: 1,
});
//获取传递参数
const searchVuale = ref("");
const isPrint = ref(route.query.isPrint === 'true'); // 是否打印小票
let searchParam = ref(""); // 规格单位
let indexSelect = ref(-1);
const keyboardRef = ref(null);
const handleFocus = () => {
};
const handleChange = (value) => {
  searchVuale.value = value;
  getGoodsItemList("", false, 1);
};
const printerList = ref<Electron.PrinterInfo[]>([]);
onMounted(async () => {
  //获取打印机列表
  printerList.value = await invoke(IpcChannel.GetPrinters);
  getGoodsItemList("", false, 1);
});
// 商品列表
async function getGoodsItemList(val, isScroll, currentPage) {
  pagingData.value.current = currentPage;
  if (currentPage === 1) goodsItemList.value = [];
  const { ...restParams } = filterForm.value || {};
  searchParam.value = val;
  try {
    let requestParams = {
      shopId: route.query.shopId,
      spuId: route.query.spuId,
      customerName: searchVuale.value,
      deliveryDate: `${route.query.deliveryDate} 00:00:00`,
      condition: {
        ...restParams,
        lineId: route.query.lineId || undefined,
        customerId: route.query.customerId || undefined,
        userAddressId: route.query.userAddressId || undefined,
        deliveryDate: `${route.query.deliveryDate} 00:00:00`,
        sortStatus: route.query.sortStatus || undefined,
      },
      specInfo: searchParam.value,
      size: pagingData.value.size,
      current: pagingData.value.current,
    };
    requestLoading.value = true;
    await goodsDetailPage(requestParams).then((res) => {
      if (res.data.data != null && res.data.data.records != null) {
        const newList = res?.data?.data?.records || [];
        goodsItemList.value = [...goodsItemList.value, ...newList];
        pagingData.value.total = Number(res?.data?.data?.total) || 0;
        isOpenPaging.value = true;
        //默认 选中第一个
        let indexNum = 0;
        goodsItemList.value.forEach((item, index) => {
          item.picUrl = item.picUrl?.[0] ?? "";
          if (item.sortStatus != "SORTED" && indexNum == 0) {
            // 未分拣
            indexSelect.value = index;
            item.bgType = "active";
            indexNum++;
          }
        });
      } else {
        goodsItemList.value = [];
        pagingData.value.total = 0;
      }
      isOpenPaging.value = true;
      requestLoading.value = false;
    });
  } catch (error) {
    console.log(error, "error");
    if (isScroll) {
      pagingData.value.current = pagingData.value.current - 1;
    }
    requestLoading.value = false;
    ElMessage.error(i18n.global.t("sortingGoodsInfo.index.093127-8"));
  }
}
// 头部搜索
const inputRef = ref(null);
function seachChage() {
  inputRef.value.blur();
  getGoodsItemList(searchParam.value, false, 1);
}
// 加载更多
function handleScroll() {
  if (!isOpenPaging.value || requestLoading.value) return;
  if (goodsItemList.value.length >= pagingData.value.total) {
    return;
  }
  pagingData.value.current = pagingData.value.current + 1;
  getGoodsItemList(searchParam.value, false, pagingData.value.current);
}
//切换打印
function changePrint() {
  PrinterDialogSelectRef.value.show();
}
const PrinterDialogSelectRef = ref(null); // 打印机
//一键分拣
async function handleOneKeySorting(val) {
  //判断是否  配置过打印机
  if (isPrint.value && !val) {
    ElMessage.warning(i18n.global.t("sortingGoodsInfo.index.093127-9"));
    return;
  }
  //判断是否  配置过打印机
  if (!userInfoTState.getPrinterName && !isPrint.value) {
    PrinterDialogSelectRef.value.show();
    return;
  }
  const { ...restParams } = filterForm.value || {};
  let data = {
    shopId: route.query.shopId,
    spuId: route.query.spuId,
    print: true,
    sort: val, //false 一键打印    true  一键分拣
    easyCode: easyCode.value, // 助记码
    categoryCode: currentGoodsType.value, // 分类编码
    condition: {
      ...restParams,
      deliveryDate: `${route.query.deliveryDate} 00:00:00`,
      lineId: route.query.lineId || undefined,
      userAddressId: route.query.userAddressId || undefined,
    },
    specInfo: searchParam.value,
    customerName: searchVuale.value,
  };

  if (val) {
    await sortFinsh({
      shopId: route.query.shopId,
      spuId: route.query.spuId,
      specInfo: searchParam.value,
      condition: {
        ...restParams,
        userAddressId: route.query.userAddressId || undefined,
        lineId: route.query.lineId || undefined,
        deliveryDate: `${route.query.deliveryDate} 00:00:00`,
      },
    }).then((res) => {
      if (res.data.data == null || res.data.data == 0) {
        ElMessage.warning(i18n.global.t("sortingGoodsInfo.index.093127-10"));
        return;
      }
      let tipStr =
        i18n.global.t("sortingGoodsInfo.index.093127-11") +
        res.data.data +
        i18n.global.t("sortingGoodsInfo.index.093127-12");
      ElMessageBox.confirm(
        tipStr,
        i18n.global.t("sortingGoodsInfo.index.093127-1"),
        {
          confirmButtonText: i18n.global.t("sortingGoodsInfo.index.093127-13"),
          cancelButtonText: i18n.global.t("sortingGoodsInfo.index.093127-14"),
          type: "warning",
          customClass: "message-box-confirm",
        }
      ).then(() => {
        printAndSrotSend(data);
      });
    });
  } else {
    printAndSrotSend(data);
  }
}
// 一键打印 or 一键分拣
function printAndSrotSend(val) {
  batchPrintSort(val).then((res) => {
    if (res.data) {
      if (res.data.data != null && res.data.data.length > 0) {
        //是否需要打印
        if (!isPrint.value) {
          PrinterDialogSelectRef.value.sendPrintStart(res.data.data);
        }
      }
      requestLoading.value = false;
      //刷新页面
      getGoodsItemList(searchParam.value, false, 1);
    } else {
      ElMessage.error(i18n.global.t("sortingGoodsInfo.index.093127-15"));
      requestLoading.value = false;
    }
  });
}

//返回
function pageBack() {
  router.go(-1);
}

const clickItem = (data) => {
  router.push({
    path: "/home/<USER>",
    query: {
      ...route.query,
      filterForm: JSON.stringify(filterForm.value),
      searchParam: searchParam.value,
      sortId: data.sortId,
      isPrint: isPrint.value,
      from: "sortingGoodsInfo",
    },
  });
};

const selectItem = (data) => {
  goodsItemList.value.forEach((item) => {
    item.bgType = "normal";
  });
  data.bgType = data.bgType === "active" ? "available" : "active";
};

//单选按钮点击事件
function radioOnClick(val: boolean) {
  isPrint.value = !val;
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
::v-deep(.el-button) {
  display: inline-flex;
  box-sizing: border-box;
  height: auto;
  line-height: 22px;
  padding: 14px 20px;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  border-radius: 12px;
  border: none;
  background: var(--fills-tertiary, rgba(118, 118, 128, 0.12));
  color: var(--el-text-color-primary, #1c2026);
  font-family: PingFang SC;
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;

  & + .el-button {
    margin-left: 0;
  }

  & > span {
    gap: 4px;
  }
}

.common-layout {
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: #a6b6c8;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  display: flex;
  flex-direction: column;

  .search-bar {
    display: flex;
    align-items: center;
    padding: 20px 20px 16px 20px;
    z-index: 18;
    gap: 24px;
    background: #a6b6c8;

    ::v-deep .el-button {
      display: inline-flex;
      box-sizing: border-box;
      height: auto;
      line-height: 22px;
      padding: 12px 24px;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;
      border-radius: 12px;
      border: none;
      background: #fff;
      color: var(--el-text-color-primary, #1c2026);
      font-family: PingFang SC;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 24px;

      & + .el-button {
        margin-left: 0;
      }

      & > span {
        gap: 4px;
      }
    }

    .search-bar-right {
      flex: 1;
      display: flex;
      align-items: center;
      gap: 24px;

      .search-input {
        width: 554px;
        height: 48px;
        font-size: 18px;
        ::v-deep .el-input__wrapper {
          border-radius: 12px;
          border: 1px solid var(--el-border-color-base, #cdd2da);
          background: var(--el-color-white, #f6f7f9);
          padding: 1px 20px;
          &.is-focus {
            box-shadow: none !important;
          }
        }
      }

      .divider {
        width: 1px;
        height: 32px;
        background: #cdd2da;
      }
    }
  }

  .content {
    flex: 1;
    display: flex;
    padding: 20px;
    flex-direction: column;
    align-items: flex-start;
    flex: 1 0 0;
    align-self: stretch;
    border-radius: 20px 20px 0px 0px;
    border: 1px solid #fff;
    background: #c1cdda;

    .div-child-st {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .txt-left-size {
        color: var(---el-text-color-primary, #1c2026);
        font-family: "PingFang SC";
        font-size: 24px;
        font-style: normal;
        font-weight: 400;
        line-height: 32px;
      }

      .txt-left-num {
        color: var(---el-text-color-regular, #505762);
        /* 标准/400/H2-18 */
        font-family: "PingFang SC";
        font-size: 18px;
        font-style: normal;
        font-weight: 400;
        line-height: 26px;
        margin-left: 4px;
      }
      ::v-deep .el-checkbox__label {
        color: #1c2026;
        font-family: "PingFang SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px;
      }

      ::v-deep .el-checkbox__input.is-checked .el-checkbox__inner::after {
          top: 4px;
          left: 7px;
      }

      ::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
        color: #009f64 !important;
        background-color: #009f64 !important;
        border-color: #009f64 !important;
      }

      ::v-deep(.el-checkbox) {
        --el-checkbox-input-border-color-hover: #009f64 !important;
        --el-checkbox-border-radius: #009f64 !important;
        --el-checkbox-input-width: 20px !important;
        --el-checkbox-input-height: 20px !important;
      }
    }

    .bt-select-st {
      margin-top: 16px;
      width: 100%;

      /* 标准/400/H3-16 */
      font-family: "PingFang SC";
      font-size: 16px;
      font-style: normal;
      font-weight: 300;
      line-height: 24px;

      .label-st-txt {
        padding: 8px 19px;
        justify-content: center;
        align-items: center;
        gap: 4px;
        align-self: stretch;
        border-radius: 8px;
        border: 1px solid var(---el-color-primary-light-4, #038a78);
        background: var(---el-color-primary, #059e84);
        cursor: pointer;
        color: var(---el-color-white, #fff);
      }

      .no-label-st-text {
        padding: 8px 19px;
        justify-content: center;
        align-items: center;
        gap: 4px;
        align-self: stretch;
        border-radius: 8px;
        border: 1px solid var(---el-color-primary, #059e84);
        background: var(---el-color-primary-light-10, #e8fff7);
        cursor: pointer;
        color: var(---el-text-color-primary, #1c2026);
      }
    }

    .goods-item-list {
      margin-top: 24px;
      width: 100%;
      flex: 1;
      flex-wrap: wrap;
      display: flex;
      gap: 40px;
      ::v-deep(.el-overlay) {
        top: 126px !important;
      }
    }
  }
}

.dialog-footer button:first-child {
  margin-right: 80px;
}

.ml5 {
  margin-left: 10px;
}
.mr20 {
  margin-right: 20px;
}
.mr5 {
  margin-right: 5px;
}
.txt-right-num {
  ::v-deep(.el-radio__label) {
    font-size: var(--el-radio-font-size);
    color: var(--el-text-color-primary, #1c2026);
    font-family: PingFang SC;
    font-size: 22px;
    font-style: normal;
    font-weight: 400;
  }
  ::v-deep(.el-radio__inner:hover) {
    border-color: #009f64;
  }
  ::v-deep(.is-checked) {
    .el-radio__input.is-checked .el-radio__inner {
      border-color: #009f64;
      background: #009f64;
    }

    .el-radio__label {
      color: #009f64;
    }
  }
}
</style>
<style lang="scss">
.message-box-confirm {
  .el-button:focus,
  .el-button:hover {
    color: #059e84;
    border-color: #79d8bf;
    background-color: #e8fff7;
  }
  .el-button--primary {
    background: #009f64 !important;
    color: #fff !important;
    border-color: #009f64 !important;
  }
  .el-button--primary:hover {
    background: rgb(55, 177, 157);
    color: #fff;
    border-color: rgb(55, 177, 157);
  }
  .el-message-box__headerbtn:hover .el-message-box__close {
    color: #009f64;
  }
}
</style>
