<template>
  <div class="common-layout">
    <div class="div-main-st">
      <div class="left-div-title">
        <el-icon @click="pageBack" size="28" color="#1C2026">
          <Back />
        </el-icon>
      </div>
      <div v-if="nextSortedItem" class="next-sort-task">
        <el-icon>
          <InfoFilled />
        </el-icon>
        <div style="margin-left: 8px">
          {{
            `下个分拣任务：客户${nextSortedItem?.customerName}（商品：${nextSortedItem?.spuName}
          分拣数量：${nextSortedItem?.showOrderCount}）`
          }}
        </div>
      </div>
    </div>
    <div class="el-main">
      <div class="video-container">
        <video
          ref="videoElement"
          class="video-item"
          :class="{ empty: !isLoadVideo }"
        ></video>
        <canvas ref="canvasElement" style="display: none"></canvas>
        <div class="header-bar">
          <div class="order-bar bar">
            <div class="logo">
              <img
                v-if="selectSortedItem?.picUrl?.[0]"
                :src="selectSortedItem?.picUrl?.[0]"
              />
              <img
                v-else
                src="https://oss-public.yunlizhi.cn/frontend/delivery-center/sort-weight/img_empty.png"
              />
            </div>
            <div class="content">
              <div class="spuName">{{ selectSortedItem?.spuName }}</div>
              <div>
                <span class="label">客户</span
                ><span class="value">{{ selectSortedItem?.customerName }}</span>
              </div>
              <div>
                <span class="label">下单数量 </span>
                <span class="value">
                  {{ selectSortedItem?.showOrderCount }}
                  {{
                    selectSortedItem?.whetherStandard !== "YES"
                      ? `(${
                          selectSortedItem?.orderCount *
                          selectSortedItem?.convertRatio
                        }kg)`
                      : ""
                  }}
                </span>
              </div>
              <div v-if="selectSortedItem?.sortStatus !== 'UNSORTED'">
                <span class="label">已分拣数量 </span>
                <span class="value">
                  {{ selectSortedItem?.weight }}
                  {{
                    selectSortedItem?.whetherStandard !== "YES"
                      ? "kg"
                      : selectSortedItem.baseUnit
                  }}
                </span>

                <span
                  v-if="
                    selectSortedItem &&
                    selectSortedItem?.whetherStandard === 'YES' &&
                    selectSortedItem?.orderCount !== selectSortedItem?.weight
                  "
                  style="color: red"
                  class="value"
                >
                  {{
                    `(${
                      selectSortedItem?.orderCount > selectSortedItem?.weight
                        ? "少"
                        : "多"
                    }${Math.abs(
                      selectSortedItem?.orderCount - selectSortedItem?.weight
                    ).toFixed(2)}${
                      selectSortedItem?.whetherStandard !== "YES"
                        ? "kg"
                        : selectSortedItem?.baseUnit
                    })`
                  }}
                </span>
                <span
                  v-else-if="
                    selectSortedItem &&
                    selectSortedItem?.whetherStandard !== 'YES' &&
                    Number(
                      (
                        selectSortedItem?.orderCount *
                        selectSortedItem?.convertRatio
                      ).toFixed(2)
                    ) !== selectSortedItem?.weight
                  "
                  style="color: red"
                  class="value"
                >
                  {{
                    `(${
                      Number(
                        (
                          selectSortedItem?.orderCount *
                          selectSortedItem?.convertRatio
                        ).toFixed(2)
                      ) > selectSortedItem?.weight
                        ? "少"
                        : "多"
                    }${Math.abs(
                      Number(
                        (
                          selectSortedItem?.orderCount *
                          selectSortedItem?.convertRatio
                        ).toFixed(2)
                      ) - selectSortedItem?.weight
                    ).toFixed(2)}${
                      selectSortedItem?.whetherStandard !== "YES"
                        ? "kg"
                        : selectSortedItem?.baseUnit
                    })`
                  }}
                </span>
              </div>
            </div>
          </div>
          <div class="sorted-bar bar">
            <div class="data-entry" v-if="selectSortedItem">
              <div class="switch-container" v-if="!isStandardGoods">
                <el-switch
                  v-model="allowManualEdit"
                  active-text="手动输入"
                  inactive-text=""
                />
              </div>
              <div style="margin-bottom: 8px">
                <span>{{
                  selectSortedItem?.whetherStandard !== "YES"
                    ? "重量"
                    : "分拣数量"
                }}</span>
              </div>
              <div style="flex: 1">
                <el-input
                  ref="inputRef"
                  v-model="orderWeight"
                  @input="handleInput"
                  style="width: 100%; height: 60px"
                  :disabled="!isStandardGoods && !allowManualEdit"
                >
                  <template #append>
                    <div class="unit">
                      {{
                        selectSortedItem?.whetherStandard !== "YES"
                          ? "kg"
                          : selectSortedItem.baseUnit
                      }}
                    </div>
                  </template>
                </el-input>
              </div>
            </div>
          </div>
        </div>

        <div class="footer-btn">
          <div class="l-section">
            <el-button
              class="normal"
              @click="handlePrev"
              :class="{ disabled: !prevSortedItem }"
            >
              <el-icon size="32" color="#1C2026">
                <DArrowLeft />
              </el-icon>
            </el-button>
            <el-button
              @click="resetView"
              class="normal"
              :class="{
                disabled: selectSortedItem?.sortStatus !== 'SORTED',
              }"
            >
              <el-icon size="32" color="#1C2026">
                <RefreshRight />
              </el-icon>
              <span>重置</span>
            </el-button>
          </div>
          <div class="r-section">
            <el-button
              class="high-ligth-blue"
              @click="handleValidateBeforeSorting(true, true)"
            >
              确认分拣
            </el-button>
            <el-button class="high-ligth-green" @click="printOrSortBtn(false)">
              再次打印
            </el-button>
            <el-button @click="handleOpenLackModal" class="high-ligth-green"
              >分拣缺货</el-button
            >
            <el-button
              class="normal"
              @click="handleNext"
              :class="{ disabled: !nextSortedItem }"
            >
              <el-icon size="32" color="#1C2026">
                <DArrowRight />
              </el-icon>
            </el-button>
          </div>
        </div>
      </div>
      <div class="evidence-container">
        <div>
          <div
            v-if="isEvidence"
            class="content"
            :style="{ height: imgHeight + 'px' }"
          >
            <div class="pic">
              <img ref="evidenceContainer" :src="evidenceUrl" />
            </div>
          </div>
          <div
            class="empty-content"
            :style="{ height: imgHeight + 'px', minHeight: 275 + 'px' }"
            v-else
          >
            <div class="empty-logo"></div>
            <div class="empty-desc">暂无存证照片</div>
          </div>

          <div class="recognize-rst">
            <div class="label">商品</div>
            <el-input v-model="skuName.spuName" disabled>
              <template #append>
                <el-button class="normal" @click="handleOpenLearn"
                  >校正</el-button
                >
              </template>
            </el-input>
          </div>
        </div>

        <div class="footer">
          <el-button @click="capture(true)" class="high-ligth-blue">
            截图存证
          </el-button>
          <el-button @click="handleRecognize(true)" class="normal">
            重新识别
          </el-button>
        </div>
      </div>
    </div>

    <el-dialog
      v-model="learnVisible"
      title=""
      width="980"
      center
      :close-on-click-modal="false"
      :show-close="false"
      top="25vh"
      class="learnModal"
    >
      <div class="learn">
        <div class="learn-header">
          <div>未识别出商品，输入商品编码或名称点击学习商品</div>
          <el-icon @click="learnVisible = false" size="24" color="#1C2026">
            <Close />
          </el-icon>
        </div>
        <div class="learn-search">
          <el-input
            v-model="searchKey"
            placeholder="输入商品名称或商品编码"
            clearable
          />
        </div>
        <div class="learn-result">
          <div
            v-for="item in RecommendList"
            @click="handleRecoginzeConfirm(item)"
            :key="item.spuCode"
            class="learn-result-item"
          >
            <div class="learn-result-item-logo">
              <img :src="item.url" />
            </div>
            <div class="learn-result-item-footer">
              <div class="goodsName">{{ item.spuName }}</div>
              <div class="spec">{{ item.unit }}</div>
            </div>
            <div class="learn-result-item-code">{{ item.spuCode }}</div>
          </div>
        </div>
      </div>
    </el-dialog>

    <el-dialog
      v-model="skuDiffVisible"
      title=""
      width="696"
      center
      :close-on-click-modal="false"
      :show-close="false"
      top="25vh"
      class="tipModal"
    >
      <div class="tip-container">
        <div class="tips-header">提醒</div>
        <div class="tips-desc">
          {{
            skuName.spuName
              ? `请确认当前分拣商品品类是否一致？`
              : `当前未识别出商品`
          }}
        </div>
        <div class="tips-item-wrp">
          <div class="item">
            <div class="sku">
              <div>{{ selectSortedItem?.spuName }}</div>
            </div>
            <div class="weight-label">下单商品</div>
          </div>
          <div class="item">
            <div class="sku diff">
              <div>
                {{
                  spuName === "" || !skuName.spuName ? "--" : skuName.spuName
                }}
              </div>
            </div>
            <div class="weight-label">识别结果</div>
          </div>
        </div>

        <div class="tips-footer">
          <el-button class="normal" @click="handleOpenLearn">
            {{ spuName === "" || !skuName.spuName ? "学习" : "校正" }}
          </el-button>
          <el-button
            class="high-ligth-green"
            @click="handleValidateBeforeSorting(true, false)"
          >
            继续分拣
          </el-button>
        </div>
        <div @click="skuDiffVisible = false" class="close-icon">
          <el-icon size="24" color="#1C2026">
            <close />
          </el-icon>
        </div>
      </div>
    </el-dialog>

    <el-dialog
      v-model="weightTipVisible"
      title=""
      width="830"
      center
      :close-on-click-modal="false"
      :show-close="false"
      top="25vh"
      class="tipModal"
    >
      <div class="tip-container">
        <div class="tips-header">提醒</div>
        <div class="tips-desc">
          {{
            selectSortedItem?.whetherStandard === "YES"
              ? `请确认当前分拣商品数量是否一致？`
              : `请确认当前分拣商品重量是否一致？`
          }}
        </div>
        <div class="tips-item-wrp">
          <div class="item">
            <div class="weight">
              <div class="weight-num">
                {{
                  selectSortedItem?.whetherStandard === "YES"
                    ? selectSortedItem?.orderCount
                    : Number(
                        (
                          selectSortedItem?.orderCount *
                          selectSortedItem?.convertRatio
                        ).toFixed(2)
                      )
                }}
              </div>
              <div class="weight-unit">
                {{
                  selectSortedItem?.whetherStandard !== "YES"
                    ? "kg"
                    : selectSortedItem.baseUnit
                }}
              </div>
            </div>
            <div class="weight-label">
              {{
                selectSortedItem?.whetherStandard === "YES"
                  ? `订单数量`
                  : `订单重量`
              }}
            </div>
          </div>
          <div class="item">
            <div class="weight">
              <div
                class="weight-num"
                v-if="selectSortedItem?.sortStatus !== 'UNSORTED'"
              >
                {{ Number(selectSortedItem?.weight) + Number(orderWeight) }}
                <!-- <div>
                  <div>{{ selectSortedItem?.weight }}</div>
                  <div class="weight-unit">上次分拣</div>
                </div>
                <div>+</div>
                <div>
                  <div>{{ orderWeight }}</div>
                  <div class="weight-unit">本次分拣</div>
                </div> -->
              </div>
              <div class="weight-num" v-else>{{ orderWeight }}</div>
              <!-- <div class="weight-unit"  v-if="selectSortedItem?.sortStatus !== 'UNSORTED'">
                {{
                  selectSortedItem?.whetherStandard !== "YES"
                    ? "kg"
                    : selectSortedItem.baseUnit
                }}
              </div>  -->
              <div class="weight-unit">
                {{
                  selectSortedItem?.whetherStandard !== "YES"
                    ? "kg"
                    : selectSortedItem.baseUnit
                }}
              </div>
            </div>

            <div
              class="weight-label"
              v-if="selectSortedItem?.sortStatus !== 'UNSORTED'"
            >
              {{
                selectSortedItem?.whetherStandard === "YES"
                  ? `分拣数量`
                  : `称重重量`
              }}{{ `(本次分拣:${orderWeight})` }}
            </div>
            <div class="weight-label" v-else>
              {{
                selectSortedItem?.whetherStandard === "YES"
                  ? `分拣数量`
                  : `称重重量`
              }}
            </div>
          </div>
          <div class="item">
            <div class="weight">
              <div class="weight-num diff">
                {{
                  Number(
                    (
                      Number(orderWeight) +
                      Number(selectSortedItem?.weight ?? 0) -
                      (selectSortedItem?.whetherStandard === "YES"
                        ? selectSortedItem?.orderCount
                        : Number(
                            (
                              selectSortedItem?.orderCount *
                              selectSortedItem?.convertRatio
                            ).toFixed(2)
                          ))
                    ).toFixed(2)
                  ) > 0
                    ? "+"
                    : ""
                }}
                {{
                  Number(
                    (
                      Number(orderWeight) +
                      Number(selectSortedItem?.weight ?? 0) -
                      (selectSortedItem?.whetherStandard === "YES"
                        ? selectSortedItem?.orderCount
                        : Number(
                            (
                              selectSortedItem?.orderCount *
                              selectSortedItem?.convertRatio
                            ).toFixed(2)
                          ))
                    ).toFixed(2)
                  )
                }}
              </div>
              <div class="weight-unit diff">
                {{
                  selectSortedItem?.whetherStandard !== "YES"
                    ? "kg"
                    : selectSortedItem.baseUnit
                }}
              </div>
            </div>
            <div class="weight-label">
              {{
                selectSortedItem?.whetherStandard === "YES"
                  ? `数量差值`
                  : `重量差值`
              }}
            </div>
          </div>
        </div>

        <div class="tips-footer">
          <el-button class="normal" @click="weightTipVisible = false">
            取消
          </el-button>
          <el-button
            class="high-ligth-green"
            @click="handleValidateBeforeSorting(false, false)"
          >
            继续分拣
          </el-button>
        </div>
      </div>
    </el-dialog>

    <el-dialog
      v-model="lackModalVisible"
      title=""
      width="696"
      center
      :close-on-click-modal="false"
      :show-close="false"
      top="25vh"
      class="tipModal lackModal"
    >
      <div class="tip-container">
        <div class="tips-header">提醒</div>
        <div class="tips-desc">请确认当前分拣商品的缺货方式？</div>
        <div class="tips-item-wrp">
          <div class="item">
            <div
              class="box"
              :class="{ active: isLackAll }"
              @click="handleLackType(true)"
            >
              全部缺货
              <div v-if="isLackAll" class="mark">
                <el-icon size="18" color="#fff">
                  <Select />
                </el-icon>
              </div>
            </div>
            <div class="lack-desc">当前商品全部无货</div>
          </div>
          <div class="item">
            <div
              class="box"
              @click="handleLackType(false)"
              :class="{ active: !isLackAll }"
            >
              部分缺货
              <div v-if="!isLackAll" class="mark">
                <el-icon size="18" color="#fff">
                  <Select />
                </el-icon>
              </div>
            </div>
            <div class="lack-desc">以当前录入记录分拣数量</div>
          </div>
        </div>

        <div class="tips-footer">
          <el-button class="normal" @click="lackModalVisible = false">
            取消
          </el-button>
          <el-button class="high-ligth-green" @click="handleConfirmLack">
            确认
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, computed, nextTick, onUnmounted, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import { goodsDetailPage, sortLackv2 } from "@renderer/api/login";
import { ElMessage, ElMessageBox } from "element-plus";
import { printSort, reSetSort, fetchSpuMap } from "@renderer/api/login";
import { upload } from "@renderer/api/sorting";
import { userInfoT } from "@renderer/store/modules/template";
import axios from "axios";
import dayjs from "dayjs";
import {
  invoke,
  serialClose,
  recongnize,
  recognizeConfirm,
  connect,
} from "../../../utils/ipcRenderer";
import { IpcChannel } from "../../../../ipc";

const { ipcRenderer } = require("electron");
const userInfoTState = userInfoT();
const router = useRouter();
const route = useRoute();
const GAO_DE_TOKEN = "0878b28876891adf09905cea88cd4c5b";
const inputRef = ref(null);
const videoElement = ref(null);
const canvasElement = ref(null);
const evidenceContainer = ref(null);
const requestLoading = ref(false);
const isEvidence = ref(false);
const evidenceUrl = ref("");
const orderWeight = ref("0.00");
const customerItemList = ref([]);
const indexSelect = ref(0);
const location = ref({ rectangle: "", address: "" });
const isPrint = ref(route.query.isPrint === "true");
const RecommendList = ref([]);
const captureLoading = ref(false);
const isLoadVideo = ref(false);
const learnVisible = ref(false);
const weightTipVisible = ref(false);
const lackModalVisible = ref(false);
const skuDiffVisible = ref(false);
const skuName = ref({ spuName: "", spuCode: "" });
const searchKey = ref("");
const isLackAll = ref(false);
const requestIdTemp = ref("");
const logger = ref("");
const isLoadData = ref(false);
const allowManualEdit = ref(false);
const imgHeight = ref(0);

// 分拣识别设置
const sortingSettings = ref({
  showVideoStream: true, // 视频流显示开关
  autoRecognize: true, // 自动识别开关
  autoCapture: true, // 自动拍照开关
});

//返回分拣列表
const pageBack = () => {
  router.go(-1);
};

const watermarkImage1 = new Image();
watermarkImage1.src =
  "https://oss-public.yunlizhi.cn/frontend/delivery-center/fe-saas-fresh-admin/location.png";
const watermarkImage2 = new Image();
watermarkImage2.src =
  "https://oss-public.yunlizhi.cn/frontend/delivery-center/fe-saas-fresh-admin/network.png";
const watermarkImage3 = new Image();
watermarkImage3.src =
  "https://oss-public.yunlizhi.cn/frontend/delivery-center/fe-saas-fresh-admin/time.png";
const watermarkImage4 = new Image();
watermarkImage4.src =
  "https://oss-public.yunlizhi.cn/frontend/delivery-center/fe-saas-fresh-admin/user.png";

let mediaStream = null;

const openVideoSuccsess = (stream) => {
  mediaStream = stream;
  videoElement.value.srcObject = stream;
  videoElement.value.play();
};

const getUserMedia = () => {
  // 只有当视频流显示开关打开时才初始化摄像头
  if (sortingSettings.value.showVideoStream) {
    navigator.mediaDevices
      .getUserMedia({ video: true })
      .then(function (stream) {
        isLoadVideo.value = true;
        openVideoSuccsess(stream);
      })
      .catch(function (err) {
        isLoadVideo.value = false;
        ElMessage.warning("未获取到视频");
      });
  } else {
    isLoadVideo.value = false;
  }
};

const closeCamera = () => {
  mediaStream?.getTracks().forEach((track) => {
    track.stop(); // 立即停止轨道
    track.enabled = false; // 双重保险
  });
  if (videoElement.value) {
    videoElement.value.srcObject = null;
  }
  mediaStream = null;
};

// 商品列表
const getcustomerItemList = async () => {
  const restParams = route.query.filterForm
    ? JSON.parse(route.query.filterForm ?? {})
    : {};
  try {
    const requestParams = {
      shopId: route.query.shopId,
      spuId: route.query.spuId,
      spuName: route.query.spuName,
      condition: {
        ...restParams,
        sortStatus: "ALL",
        userAddressId: route.query.userAddressId || undefined,
        lineId: route.query.lineId || undefined,
        customerId: route.query.customerId || undefined,
        deliveryDateStart: route.query.deliveryDateStart || undefined,
        deliveryDateEnd: route.query.deliveryDateEnd || undefined,
      },
      lackStatus: route.query.lackStatus ?? "ALL",
      specInfo: route.query.searchPara,
      size: 9999,
      current: 1,
    };
    if (route.query.deliveryDate) {
      requestParams.condition.deliveryDate = `${route.query.deliveryDate} 00:00:00`;
      requestParams.deliveryDate = `${route.query.deliveryDate} 00:00:00`;
    }
    requestLoading.value = true;
    await goodsDetailPage(requestParams).then((res) => {
      if (res.data.data != null && res.data.data.records != null) {
        const newList = res?.data?.data?.records || [];
        newList.forEach((item) => {
          if (item.sortStatus == "SORTED") {
            item.bgType = "disable";
            item.showWeight = item.sortQuantity || 0;
          } else {
            item.bgType = "available";
          }
        });
        customerItemList.value = [...newList];
        if (route.query.sortId) {
          customerItemList.value.forEach((item, ind) => {
            if (item.sortId === route.query.sortId) {
              item.bgType = "progress";
              indexSelect.value = ind;
            }
          });
        } else {
          //默认 选中第一个
          let indexNum = 0;
          customerItemList.value.forEach((item, ind) => {
            if (item.sortStatus != "SORTED" && indexNum == 0) {
              item.bgType = "progress";
              indexSelect.value = ind;
              indexNum++;
            }
          });
        }
      } else {
        customerItemList.value = [];
      }
      requestLoading.value = false;
      setTimeout(() => (isLoadData.value = true), 700);
    });
  } catch (error) {
    requestLoading.value = false;
    ElMessage.error("请求接口异常，请联系管理员");
  }
};

const reverseGeocode = async (position) => {
  try {
    const response = await axios.get(
      "https://restapi.amap.com/v3/geocode/regeo",
      {
        params: {
          key: GAO_DE_TOKEN,
          location: position,
          extensions: "base", // 或 'all' 获取周边POI
          radius: 1000,
        },
      }
    );
    const rst = response?.data;
    if (rst.status === "1") {
      location.value = {
        ...location.value,
        address: rst.regeocode.formatted_address,
      };
    }
  } catch (err) {
    ElMessage.warning("定位获取异常");
    console.log(err);
  }
};

const selectSortedItem = computed(() => {
  const rst = customerItemList.value.find(
    (item, ind) => ind === indexSelect.value
  );
  orderWeight.value = rst?.whetherStandard == "YES" ? "" : "0.00";
  evidenceUrl.value = "";
  skuName.value.spuName = "";
  skuName.value.spuCode = "";
  isEvidence.value = false;
  return rst;
});

const nextSortedItem = computed(() => {
  const unSorted = customerItemList.value.find(
    (item, ind) => item.bgType !== "disable" && ind > indexSelect.value
  );
  const sorted = customerItemList.value.find(
    (item, ind) => ind > indexSelect.value
  );
  return unSorted ? unSorted : sorted;
});

const prevSortedItem = computed(() => {
  // 从后往前遍历，找到最接近的未禁用项
  const unSorted = customerItemList.value
    .slice(0, indexSelect.value) // 只取 indexSelect 之前的项
    .reverse() // 反转数组，从后往前找
    .find((item) => item.bgType !== "disable");

  // 如果没有找到未禁用项，则找最近的任何项
  const sorted = customerItemList.value
    .slice(0, indexSelect.value) // 只取 indexSelect 之前的项
    .reverse() // 反转数组，从后往前找
    .find((item) => item);

  return unSorted || sorted || null;
});

const capture = (isTip) => {
  if (!isLoadVideo.value && isTip) {
    ElMessage.warning("未获取到视频截图");
    return;
  }

  if (!canvasElement.value) {
    return;
  }

  try {
    captureLoading.value = true;
    const video = videoElement.value;
    const canvas = canvasElement.value;
    const context = canvas?.getContext("2d");
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    imgHeight.value = 367 / (canvas.width / canvas.height);
    context.drawImage(video, 0, 0, canvas.width, canvas.height);
    // 绘制水印
    nextTick(() => {
      const addressPosY = canvas.height - 12;
      const [lat, lon] =
        location.value?.rectangle?.split(";")?.[0]?.split(",") ?? [];
      const { address } = location.value;
      const dataTime = dayjs().format("YYYY-MM-DD HH:mm:ss");

      // 计算背景区域
      const topTextY = addressPosY - 90; // 最顶部文字（客户名称）的Y坐标
      const bgTop = topTextY - 25; // 背景顶部位置
      const bgHeight = canvas.height - bgTop; // 背景高度

      // 绘制背景层
      context.fillStyle = "rgba(94, 99, 105, 0.70)";
      context.fillRect(
        0, // x起始位置（左边缘）
        bgTop, // y起始位置（顶部）
        canvas.width, // 宽度（右边缘）
        bgHeight // 高度（到底部）
      );

      context.font = "16px PingFang SC";
      context.fillStyle = "rgba(255, 255, 255, 1)";
      context.fillText(
        `分拣员: ${userInfoTState.getUserNameT}`,
        42,
        addressPosY - 12
      );
      context.fillText(
        `分拣仓库: ${selectSortedItem.value.warehouseName}`,
        39,
        addressPosY - 36
      );
      context.fillText(`分拣时间: ${dataTime}`, 42, addressPosY - 63);
      context.fillText(
        `客户: ${selectSortedItem.value.customerName}`,
        42,
        addressPosY - 88
      );
      context.drawImage(watermarkImage1, 16, addressPosY - 23, 18, 16);
      context.drawImage(watermarkImage2, 10, addressPosY - 53, 30, 22);
      context.drawImage(watermarkImage3, 16, addressPosY - 77, 18, 16);
      context.drawImage(watermarkImage4, 16, addressPosY - 102, 18, 16);
      const dataUrl = canvas.toDataURL("image/png");
      isEvidence.value = true;
      evidenceUrl.value = dataUrl;
      captureLoading.value = false;
    });
  } catch (error) {
    logger.value = error;
  }
};

const handleInput = (event) => {
  // 如果是标品，允许直接编辑；如果是非标品，需要检查allowManualEdit
  if (!isStandardGoods.value && !allowManualEdit.value) {
    return;
  }

  let value = event;
  if (selectSortedItem.value?.whetherStandard == "NO") {
    value =
      value
        .replace(/[^\d^\.]+/g, "")
        .replace(/^0+(\d)/, "$1")
        .replace(/^\./, "0.")
        .match(/^\d*(\.?\d{0,2})/g)[0] || "";
  }
  if (value.length > 8) {
    value = value.slice(0, 8);
  }
  orderWeight.value = value.trim();
};

const handleEdit = () => {
  inputRef.value.focus();
};

/**
 * 分拣之前 进行品类，重量校验
 */
const handleValidateBeforeSorting = (weight, spuType) => {
  if (!orderWeight.value) {
    ElMessage.error("商品重量不能为空");
    return;
  }

  if (!/^-?\d+(\.\d+)?$/.test(orderWeight.value)) {
    ElMessage.error("请输入正确的数字");
    orderWeight.value = "";
    handleEdit();
    return;
  }

  if (
    selectSortedItem.value?.whetherStandard === "YES" &&
    !Number.isInteger(parseFloat(orderWeight.value))
  ) {
    ElMessage.error("标品重量不能为小数");
    orderWeight.value = "";
    handleEdit();
    return;
  }

  if (isLoadVideo.value && skuName.value && spuType) {
    if (skuName.value.spuName !== selectSortedItem.value?.spuName) {
      skuDiffVisible.value = true;
      return;
    } else {
      handleRecoginzeConfirm(skuName.value);
    }
  } else {
    skuDiffVisible.value = false;
  }

  const diffAbs = Math.abs(
    Number(
      (
        Number(orderWeight.value) -
        (selectSortedItem?.value.whetherStandard === "YES"
          ? selectSortedItem?.value.orderCount
          : Number(
              (
                selectSortedItem?.value.orderCount *
                selectSortedItem.value.convertRatio
              ).toFixed(2)
            ))
      ).toFixed(2)
    )
  );

  if (
    selectSortedItem.value.whetherStandard !== "YES" &&
    diffAbs > 0 &&
    weight
  ) {
    skuDiffVisible.value = false;
    weightTipVisible.value = true;
    return;
  }

  printOrSortBtn(true);
};

/**
 * 分拣
 */
async function printOrSortBtn(val: boolean) {
  //判断是否  配置打印小票
  if (isPrint.value && !val) {
    ElMessage.warning("已勾选不打印分拣小票,如需打印请取消勾选");
    return;
  }
  //判断是否  配置过打印机
  if (!userInfoTState.getPrinterName && !isPrint.value) {
    ElMessage.warning("未配置打印机,请右上角先切换打印机");
    return;
  }

  let res = "";
  if (evidenceUrl.value) {
    const blob = dataURLToBlob(evidenceUrl.value);
    const file = new File([blob], "image.png", { type: "image/png" }); // 包装成 File 对象

    // 使用 FormData 包装（更规范）
    const formData = new FormData();
    formData.append("file", file);
    res = await upload(formData);
  }

  skuDiffVisible.value = false;
  weightTipVisible.value = false;

  let dataParm = {
    sortId: selectSortedItem.value?.sortId,
    sort: val, // true  分拣   false  打印
    sortQuantity: Number(
      (
        Number(orderWeight.value) + Number(selectSortedItem.value?.weight ?? 0)
      ).toFixed(3)
    ),
    goodsNameMatch: selectSortedItem.value.isRecoginze ?? false,
    recognizeInfo: selectSortedItem.value.recognizeInfo,
    sortPic: evidenceUrl.value ? res?.data?.data?.url ?? "" : "",
  };

  if (val) {
    //判断是否二次分拣
    if (selectSortedItem.value?.bgType === "disable") {
      ElMessageBox.confirm(
        "此商品已经分拣完成，是否需要再次分拣并打印标签?",
        "二次确认",
        {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning",
          customClass: "message-box-confirm",
        }
      ).then(() => {
        sendPrintSort(dataParm);
      });
    } else {
      sendPrintSort(dataParm);
    }
  } else {
    sendPrintSort(dataParm);
  }
}

/**
 *
 * @param dataParm 识别
 */

async function sendPrintSort(dataParm) {
  await printSort(dataParm).then((res) => {
    //开始打印
    if (!isPrint.value) {
      if (res.data != null && res.data.data != null) {
        sendPrintStart(res.data.data);
      } else {
        ElMessage.success("打印数据错误");
      }
    }

    //如果是打印，不切换颜色
    if (!dataParm.sort) {
      // 打印
      //TODO
    } else {
      ElMessage.success("分拣成功，已自动打印");
      // 标记为分拣完成
      selectSortedItem.value.sortStatus = "SORTED";
      selectSortedItem.value.weight = orderWeight.value;
      if (orderWeight.value && orderWeight.value != "") {
        selectSortedItem.value.showWeight = orderWeight.value ?? "";
      }
      if (
        orderWeight.value &&
        selectSortedItem.value.orderCount > orderWeight.value
      ) {
        selectSortedItem.value.bgType = "disableRed";
      } else {
        selectSortedItem.value.bgType = "disable";
      }

      //延迟设置下一个
      setTimeout(() => {
        let slectFalg = true;
        customerItemList.value.forEach((item, index) => {
          if (item.bgType != "disable" && slectFalg) {
            customerItemList.value[index].bgType = "progress";
            handleEdit();
            indexSelect.value = index;
            slectFalg = false;
            evidenceUrl.value = "";
            isEvidence.value = false;
          }
        });
        // 没有下一个则返回上一页
        slectFalg === true && pageBack();
      }, 800);
    }
  });
}

const handleRecoginzeConfirm = async (data) => {
  learnVisible.value = false;
  skuName.value.spuName = data.spuName;
  skuName.value.spuCode = data.spuCode;
  requestIdTemp.value = "";
  recognizeConfirm(data.spuCode, data.spuName);
};

const handleOpenLearn = () => {
  if (!isLoadVideo.value) {
    ElMessage.warning("未获取到视频无法进行识别");
    return;
  }
  if (requestIdTemp.value) {
    skuDiffVisible.value = false;
    RecommendList.value = [
      {
        url: selectSortedItem.value?.picUrl?.[0],
        spuName: selectSortedItem.value?.spuName,
        spuCode: selectSortedItem.value.spuCode,
        unit: selectSortedItem.value.baseUnit,
      },
    ];
    searchKey.value = "";
    learnVisible.value = true;
  } else {
    ElMessage.warning("请先进行识别，再校正");
  }
};

// 开启打印
async function sendPrintStart(val) {
  nextTick(() => {
    userInfoTState.setPerinterObj(JSON.stringify(val));
    invoke(IpcChannel.OpenWin, { url: "/PrintPage" });
  });
}

const resetView = async () => {
  if (selectSortedItem.value?.sortStatus === "SORTED") {
    await reSetSort(selectSortedItem.value?.sortId).then((res) => {
      selectSortedItem.value.bgType = "available";
      selectSortedItem.value.showWeight = "";
      selectSortedItem.value.sortStatus = "UNSORTED";
      let slectFalg = true;
      customerItemList.value.forEach((item, index) => {
        if (item.bgType != "disable" && slectFalg) {
          customerItemList.value[index].bgType = "progress";
          slectFalg = false;
        }
      });
      evidenceUrl.value = "";
      isEvidence.value = false;
      selectSortedItem.value.recognizeInfo = "";
      ElMessage.success("重置成功");
    });
  }
};

const dataURLToBlob = (dataURL) => {
  const [meta, base64Data] = dataURL.split(",");
  const mime = meta.match(/:(.*?);/)[1];
  const binary = atob(base64Data);
  const array = [];
  for (let i = 0; i < binary.length; i++) {
    array.push(binary.charCodeAt(i));
  }
  // 返回 Blob 对象
  return new Blob([new Uint8Array(array)], { type: mime });
};

const compressCanvasToBase64 = (sourceCanvas, quality = 1) => {
  const offscreenCanvas = document.createElement("canvas");
  const ctx = offscreenCanvas.getContext("2d");

  // 设置目标尺寸（可选：保持宽高比）
  const srcWidth = videoElement.value.videoWidth;
  const srcHeight = videoElement.value.videoHeight;
  const targetWidth = 640;
  const targetHeight = 480;

  // 计算保持比例的缩放尺寸
  const scaleRatio = Math.min(targetWidth / srcWidth, targetHeight / srcHeight);
  const scaledWidth = srcWidth * scaleRatio;
  const scaledHeight = srcHeight * scaleRatio;

  // 设置离屏Canvas尺寸（可选居中绘制）
  offscreenCanvas.width = targetWidth;
  offscreenCanvas.height = targetHeight;

  // 提高缩放质量
  ctx.imageSmoothingEnabled = true;
  ctx.imageSmoothingQuality = "high";

  // 居中绘制并缩放，保持比例（若需拉伸则直接使用目标尺寸）
  ctx.drawImage(
    sourceCanvas,
    0,
    0,
    srcWidth,
    srcHeight,
    (targetWidth - scaledWidth) / 2, // 水平居中
    (targetHeight - scaledHeight) / 2, // 垂直居中
    scaledWidth,
    scaledHeight
  );
  return offscreenCanvas.toDataURL("image/jpg", 0.7);
};

const handleRecognize = (isTip) => {
  if (isTip && !isLoadVideo.value) {
    ElMessage.warning("未获取到视频无法识别");
    return;
  }
  const base64Url = compressCanvasToBase64(videoElement.value);
  recongnize(base64Url.split(",")[1]);
};

const handleSearchChange = async (val) => {
  if (val) {
    const result = await fetchSpuMap({ name: val });
    const filterRst = (result?.data?.data?.records ?? []).filter(
      (item, ind) => item.spuCode !== selectSortedItem.value.spuCode && ind < 5
    );
    RecommendList.value = [
      {
        url: selectSortedItem.value.picUrl?.[0],
        spuName: selectSortedItem.value?.spuName,
        spuCode: selectSortedItem.value.spuCode,
        unit: selectSortedItem.value.baseUnit,
      },
      ...filterRst.map((item) => ({
        url: item.picUrl?.[0],
        unit: item.baseUnitName,
        spuCode: item.spuCode,
        spuName: item.name,
      })),
    ];
  } else {
    RecommendList.value = [
      {
        url: selectSortedItem.value.picUrl?.[0],
        spuName: selectSortedItem.value?.spuName,
        spuCode: selectSortedItem.value.spuCode,
        unit: selectSortedItem.value.baseUnit,
      },
    ];
  }
};

const handleNext = () => {
  if (!nextSortedItem.value) {
    ElMessage.info("暂无下一个分拣任务");
    return;
  }
  const unSortedIndex = customerItemList.value.findIndex(
    (item, ind) => item.bgType !== "disable" && ind > indexSelect.value
  );
  const sortedIndex = customerItemList.value.findIndex(
    (item, ind) => ind > indexSelect.value
  );
  indexSelect.value = unSortedIndex !== -1 ? unSortedIndex : sortedIndex;
};

const handlePrev = () => {
  if (!prevSortedItem.value) {
    ElMessage.info("暂无上一个入库任务");
    return;
  }

  // 从 indexSelect 开始向前遍历，找到最接近的未禁用项
  const unSortedIndex = customerItemList.value
    .slice(0, indexSelect.value) // 只取 indexSelect 之前的项
    .reverse() // 反转数组，从后往前找
    .findIndex((item) => item.bgType !== "disable");

  // 如果没有找到未禁用项，则找最近的任何项
  const sortedIndex = customerItemList.value
    .slice(0, indexSelect.value) // 只取 indexSelect 之前的项
    .reverse() // 反转数组，从后往前找
    .findIndex((item) => item);

  // 如果找到了项，需要计算正确的索引
  indexSelect.value =
    unSortedIndex !== -1
      ? indexSelect.value - unSortedIndex - 1
      : sortedIndex !== -1
      ? indexSelect.value - sortedIndex - 1
      : indexSelect.value;
};

const handleOpenLackModal = () => {
  lackModalVisible.value = true;
  isLackAll.value = true;
};

const handleLackType = (type) => {
  isLackAll.value = type;
};

const handleConfirmLack = () => {
  if (!isLackAll.value && !orderWeight.value) {
    ElMessage.warning("部分缺货请输入当前分拣数量");
    return;
  }
  lackModalVisible.value = false;
  sendSortLack();
};

async function sendSortLack() {
  if (selectSortedItem.value.bgType === "disable") {
    ElMessageBox.confirm(
      "此商品已经分拣完成，是否需要重新标记为缺货?",
      "二次确认",
      {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning",
        customClass: "message-box-confirm",
      }
    ).then(() => {
      sortLackSend();
    });
  } else {
    sortLackSend();
  }
}

async function sortLackSend() {
  let sortId = selectSortedItem.value.sortId;
  await sortLackv2({
    sortId,
    lackStatus: isLackAll.value ? "LACK" : "PART_LACK",
    sortQuantity: orderWeight.value,
  }).then((res) => {
    if (res.data) {
      selectSortedItem.value.bgType = "disable";
      setTimeout(() => {
        handleNext();
      }, 400);
      ElMessage.success("标记缺货成功");
    } else {
      ElMessage.error("请求接口异常，请联系管理员");
    }
  });
}

// 是否标品
const isStandardGoods = computed(() => {
  return selectSortedItem.value?.whetherStandard === "YES";
});

ipcRenderer.on("weight-update", (event, data) => {
  if (
    data?.status === "S" &&
    orderWeight.value !== data.weight &&
    selectSortedItem.value?.whetherStandard !== "YES" &&
    isLoadData.value &&
    selectSortedItem.value &&
    !allowManualEdit.value
  ) {
    orderWeight.value = data.weight;
    if (
      !["0", "", 0, undefined, null].includes(data.weight) &&
      data.weight > 0
    ) {
      // 根据设置决定是否自动识别和自动拍照
      if (sortingSettings.value.autoRecognize) {
        handleRecognize(false);
      }
      if (sortingSettings.value.autoCapture) {
        setTimeout(() => {
          capture(false);
        }, 300);
      }
    }
  }
});

/**
 * 识别服务向Electron发出的消息
 */
ipcRenderer.on("electron-receive-message", async (event, message) => {
  logger.value = message;
  const { cmd, data, status, requestId } = JSON.parse(message ?? {});
  if (cmd == 200 && status === 0) {
    requestIdTemp.value = requestId;
    if (data.length) {
      const res = await fetchSpuMap({ spuCode: data[0].code });
      skuName.value.spuName = res?.data?.data?.records?.[0]?.name ?? "";
      skuName.value.spuCode = res?.data?.data?.records?.[0]?.spuCode ?? "";
    } else {
      handleOpenLearn();
    }
  }
});

ipcRenderer.on("electron-send-message", (event, message) => {
  logger.value = message;
});

// 连接串口
connect("COM2");

watch(
  () => searchKey.value,
  (val) => {
    handleSearchChange(val);
  }
);

// 加载分拣识别设置
const loadSortingSettings = () => {
  try {
    const savedSortingSettings = localStorage.getItem("sortingSettings");
    if (savedSortingSettings) {
      const parsedSettings = JSON.parse(savedSortingSettings);
      sortingSettings.value = {
        ...sortingSettings.value,
        ...parsedSettings,
      };
    }
  } catch (error) {
    console.error("加载设置失败:", error);
  }
};

onMounted(async () => {
  const { spuName, spuId, imgUrl } = route.query;
  loadSortingSettings();
  getUserMedia();
  getcustomerItemList();
});

onUnmounted(() => {
  serialClose();
  // 离开页面关闭摄像头
  closeCamera();
});
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.common-layout {
  display: flex;
  flex-direction: column;
  height: 100%;
}

::v-deep(.el-button) {
  display: inline-flex;
  box-sizing: border-box;
  height: auto;
  line-height: 22px;
  padding: 14px 20px;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  border-radius: 12px;
  border: none;
  color: var(---el-text-color-primary, #1c2026);
  font-family: "PingFang SC";
  font-size: 24px;
  font-style: normal;
  font-weight: 400;
  line-height: 32px;

  & + .el-button {
    margin-left: 0;
  }

  & > span {
    gap: 4px;
  }
}

::v-deep(.el-button.normal) {
  border: 1px solid #b0b7c2;
  background: #fff;
  padding: 12px 20px;
  color: #505762;
  font-size: 24px;
  font-style: normal;
  font-weight: 400;
  line-height: 32px;
  border: 1px solid var(---el-text-color-placeholder, #b0b7c2);
  background: linear-gradient(180deg, #fff 0%, #bbc1c3 100%);

  &.disabled {
    background: rgba(92, 96, 100) !important;
    border: 1px solid rgba(92, 96, 100) !important;
    color: #1c2026;
  }
}

::v-deep(.el-button.high-ligth-blue) {
  border: 1px solid #9bedff;
  background: linear-gradient(180deg, #b5f2ff 28.57%, #2b8be4 125%);
}

::v-deep(.el-button.high-ligth-green) {
  border: 1px solid #6dd8b1;
  background: linear-gradient(180deg, #b5ffdb 28.57%, #00af70 125%);
}

::v-deep(.data-entry .el-input__wrapper) {
  background: rgba(0, 0, 0, 0) !important;
  border: none !important;
  box-shadow: none !important;
  padding-right: 0px !important;
}

::v-deep(.data-entry .el-input.is-disabled .el-input__wrapper) {
  background: rgba(0, 0, 0, 0) !important;
  border: none !important;
  box-shadow: none !important;
  opacity: 1;
}

::v-deep(.data-entry .el-input-group__append) {
  background: none;
  border: none;
  box-shadow: none;
  padding: 0;
  display: flex;
  align-items: flex-end;
}

::v-deep(.data-entry .el-input) {
  border: none !important;
}

::v-deep(.data-entry .el-input__inner) {
  text-align: center !important;
  color: var(---el-text-color-primary, #f0f6fc);
  text-align: center;
  font-family: DIN;
  font-size: 72px;
  font-style: normal;
  font-weight: 700;
  line-height: 60px;
  height: 60px;
}

::v-deep(.data-entry .el-input.is-disabled .el-input__inner) {
  color: var(---el-text-color-primary, #f0f6fc);
  -webkit-text-fill-color: var(---el-text-color-primary, #f0f6fc);
  background-color: transparent;
  cursor: text;
}

::v-deep(.el-dialog.recongize) {
  background: rgba(0, 0, 0, 0);
  box-shadow: none;

  .el-dialog__header,
  .el-dialog__body,
  .el-dialog__footer {
    padding: 0 !important;
  }

  .el-dialog__footer {
    margin-top: 40px;
  }
}

::v-deep(.el-checkbox__inner::after) {
  box-sizing: content-box;
  content: "";
  border: 1px solid transparent;
  border-left: 0;
  border-top: 0;
  height: 20px;
  left: 14px;
  position: absolute;
  top: 6px;
  transform: rotate(45deg) scaleY(0);
  width: 10px;
  transition: transform 0.15s ease-in 50ms;
  transform-origin: center;
}

::v-deep(.el-checkbox) {
  position: absolute;
  top: -2px;
  right: -2px;
}

::v-deep(.el-checkbox:last-of-type) {
  padding-right: 0;
}

::v-deep(.el-checkbox.el-checkbox--large .el-checkbox__inner) {
  background: #059e84;
  width: 41px;
  height: 41px;
  border-bottom-left-radius: 16px;
  border-top-right-radius: 16px;
}

.title {
  display: flex;
  padding: 12px 20px;
  align-items: center;
  gap: 8px;
  align-self: stretch;
  background: linear-gradient(90deg, #185a9d 0%, #43cea2 100%);
  color: #fff;
  font-family: PingFang SC;
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: 26px;
  -webkit-app-region: drag;
}

.el-main {
  height: calc(100% - 62px);
  display: flex;
  align-items: center;
  padding: 0 !important;
  overflow: hidden;
  background: #a6b6c8;

  .video-container {
    height: 100%;
    flex: 1;
    position: relative;

    .canvas {
      width: 100%;
      height: calc(100% - 88px);
    }

    .header-bar {
      width: 100%;
      height: 164px;
      box-sizing: border-box;
      position: absolute;
      left: 0;
      top: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;

      .order-bar {
        flex: 1;
        max-width: 480px;
        display: flex;
        align-items: center;
        border: 1px solid #e6eaf0;
        background: #fff;
        padding: 20px;

        .logo {
          width: 115px;
          height: 115px;
          margin-right: 16px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          overflow: hidden;

          img {
            width: 100%;
            height: auto;
            object-fit: contain;
            border-radius: 12px;
          }
        }

        .content {
          color: #f0f6fc;
          font-size: 24px;
          font-style: normal;
          line-height: 32px;

          .spuName {
            max-width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            height: 32px;
          }

          .label {
            width: 92px;
            color: var(---el-text-color-regular, #b1bac6);
            font-family: "PingFang SC";
            font-size: 18px;
            font-style: normal;
            font-weight: 300;
            line-height: 26px;
            display: inline-block;
            margin-right: 20px;
          }

          .value {
            color: #f0f6fc;
            font-size: 18px;
            font-weight: 300;
          }
        }
      }

      .sorted-bar {
        width: 385px;
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .icon {
          position: absolute;
          top: 16px;
          right: 16px;
          width: 24px;
          height: 24px;
          background: url("../../assets/img/edit-icon.png") no-repeat;
          background-size: cover;
          cursor: pointer;
        }

        .data-entry {
          display: flex;
          flex-direction: column;
          // justify-content: center;
          max-width: 68%;
          padding-bottom: 8px;
          border-bottom: 2px solid var(---el-text-color-placeholder, #b0b7c2);
          color: var(---el-border-color-extra-light, #fafbfc);
          text-align: center;
          /* 标准/500/H1-24 */
          font-family: "PingFang SC";
          font-size: 24px;
          font-style: normal;
          font-weight: 400;
          line-height: 32px;
          // position: relative;

          .unit {
            color: var(---el-text-color-primary, #f0f6fc);
            text-align: left;
            font-family: "PingFang SC";
            font-size: 28px;
            font-style: normal;
            font-weight: 400;
            line-height: 36px;
          }

          .switch-container {
            position: absolute;
            top: 0;
            right: 8px;
          }
        }

        .desc {
          color: #b1bac6;
          text-align: center;
          font-size: 18px;
          font-style: normal;
          font-weight: 300;
          line-height: 26px;
          margin-top: 12px;
        }
      }

      .bar {
        display: flex;
        padding: 16px;
        border-radius: 12px;
        border: 1px solid rgba(255, 255, 255, 0.6);
        background: rgba(33, 38, 45, 0.65);
        backdrop-filter: blur(4px);
        backdrop-filter: blur(4px);
        height: 100%;
      }
    }

    .video-item {
      width: 100%;
      height: calc(100% - 88px);
      object-fit: cover;
      border-top-left-radius: 12px;

      &.empty {
        background: url("../../assets/img/empty-video.png") no-repeat;
        background-size: cover;
      }
    }

    .footer-btn {
      width: 100%;
      height: 88px;
      box-sizing: border-box;
      padding: 12px 20px 20px 20px;
      position: absolute;
      left: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
      overflow: hidden;
      background: #21262d;

      .l-section {
        display: flex;
        align-items: center;
        gap: 16px;
      }

      .r-section {
        display: flex;
        align-items: center;
        gap: 16px;
      }
    }
  }

  .evidence-container {
    width: 367px;
    height: 100%;
    padding: 20px;
    border-top-right-radius: 12px;
    box-sizing: border-box;
    border: 1px solid var(---el-color-white, #fff);
    background: var(---el-border-color-light, #e6eaf0);
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .content {
      width: 100%;
      height: 424px;
      background: #f6f7f9;
      display: flex;
      flex-direction: column;
      border-radius: 12px;
      overflow: hidden;
      justify-content: center;

      .pic {
        width: 100%;
        height: 100%;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .footer {
        margin-top: 16px;
        width: 100%;
        height: 86px;
        padding: 0 16px 26px 16px;
        display: flex;
        flex-wrap: wrap;
        align-items: flex-start;

        .item {
          width: 50%;
          display: flex;
          align-items: center;
          color: #7e8694;
          font-size: 18px;
          font-style: normal;
          font-weight: 300;
          line-height: 26px;
          height: 26px;

          .label {
            text-align: right;
          }

          .value {
            text-align: left;
            color: #1c2026;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
    }

    .empty-content {
      width: 100%;
      height: 424px;
      border-radius: 12px;
      background: #f6f7f9;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;

      .empty-logo {
        width: 175px;
        height: 189px;
        background: url("../../assets/img/empty-logo.png") no-repeat;
        background-size: contain;
      }

      .empty-desc {
        color: #7e8694;
        text-align: center;
        font-size: 18px;
        font-style: normal;
        font-weight: 400;
        line-height: 26px;
        margin-top: 8px;
      }
    }

    .recognize-rst {
      display: flex;
      align-items: center;
      margin-top: 16px;
      color: var(---el-text-color-primary, #1c2026);
      font-family: "PingFang SC";
      font-size: 20px;
      font-style: normal;
      font-weight: 400;
      line-height: 28px;
      gap: 10px;

      .label {
        width: 50px;
      }

      ::v-deep(.el-input-group__append) {
        .el-button {
          padding: 7px 20px;
          border-radius: 0px 12px 12px 0px;
          border-top: 1px solid var(---el-text-color-placeholder, #b0b7c2);
          border-right: 1px solid var(---el-text-color-placeholder, #b0b7c2);
          border-bottom: 1px solid var(---el-text-color-placeholder, #b0b7c2);
          background: linear-gradient(180deg, #fff 0%, #bbc1c3 100%);
        }
      }

      ::v-deep(.el-input__wrapper) {
        border-radius: 12px 0px 0px 12px;
        border: 1px solid var(---el-border-color-base, #cdd2da);
        background: var(---el-color-normal-bg, #f6f7f9);
      }

      ::v-deep .el-input__inner {
        font-family: "PingFang SC";
        font-size: 20px;
        font-style: normal;
        font-weight: 300;
        line-height: 28px;
      }

      ::v-deep(.el-input) {
        --el-input-text-color: #038a78;
        --el-disabled-text-color: #038a78;
        --el-text-color-placeholder: #038a78;
      }
    }

    .footer {
      width: 100%;
      display: flex;
      gap: 16px;

      ::v-deep(.el-button) {
        flex: 1;
      }
    }
  }
}

.div-main-st {
  width: 100%;
  background: #a6b6c8;
  border-radius: 20px 20px 0px 0px;
  height: 62px;
  padding: 0 20px;
  display: flex;
  align-items: center;

  .left-div-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .next-sort-task {
    flex: 1;
    display: flex;
    height: 48px;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    color: var(---el-text-color-primary, #1c2026);
    font-family: "PingFang SC";
    font-size: 18px;
    font-style: normal;
    font-weight: 300;
    line-height: 26px;
  }
}

.ml5 {
  margin-left: 10px;
}

.mr20 {
  margin-right: 20px;
}

.mr5 {
  margin-right: 5px;
}

.recongize-container {
  width: 100%;
  box-sizing: border-box;
  padding: 40x 0 40px 40px;
  gap: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow-x: scroll;
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    display: none;
  }

  &::-webkit-scrollbar-track {
    display: none;
  }

  .item {
    width: 280px;
    height: 320px;
    border-radius: 12px;
    background: #f6f7f9;
    display: flex;
    flex-direction: column;
    position: relative;

    &.active {
      border: 4px solid #059e84;
    }

    .logo {
      flex: 1;

      img {
        width: 100%;
        height: 100%;
        border-top-left-radius: 12px;
        border-top-right-radius: 12px;
      }
    }

    .goods-name {
      width: 100%;
      height: 80px;
      box-sizing: border-box;
      padding: 16px;
      color: #7e8694;
      display: flex;
      align-items: flex-start;
      font-size: 24px;
      font-style: normal;
      font-weight: 400;
      line-height: 32px;

      .value {
        color: #1c2026;
      }
    }
  }
}

.dialog-footer {
  .recoginze-cancle {
    padding: 12px 32px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: 98px;
    border: 1px solid #b0b7c2;
    color: #fff;
    font-size: 24px;
    font-style: normal;
    font-weight: 400;
    line-height: 32px;
    margin-right: 24px;
  }

  .recoginze-confirm {
    padding: 12px 32px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border: 1px solid #48c6c0;
    border-radius: 98px;
    color: #fff;
    font-size: 24px;
    font-style: normal;
    font-weight: 400;
    line-height: 32px;
    background: linear-gradient(
      180deg,
      #9bffff 0%,
      #3bbfb1 16.11%,
      #26a99c 32.28%,
      #099980 53.21%,
      #03a287 74.6%,
      #1bb294 87.27%,
      #5ed9b4 100%
    );
  }
}

::v-deep(.learnModal) {
  border-radius: 16px 16px 16px 16px;

  .el-dialog__header {
    display: none;
  }

  .el-dialog__body {
    padding: 0;
    border-radius: 16px 16px 16px 16px;
  }

  .learn {
    border-radius: 16px 16px 0 0;

    &-header {
      border-radius: 16px 16px 0 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 50px;
      background: #cfd9e4;
      color: var(---el-text-color-primary, #1c2026);
      font-family: "PingFang SC";
      font-size: 18px;
      font-style: normal;
      font-weight: 400;
      line-height: 26px;
      padding: 20px;
    }

    &-result {
      padding: 0 20px 20px 20px;
      display: flex;
      align-items: center;
      gap: 20px;
      width: calc(100% - 40px);
      overflow-x: auto;

      .add-item {
        border: 1px dashed #059e84;
        border-radius: 12px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s;
        background: #fff;

        &:hover {
          border-color: #00b42a;

          .add-text {
            color: #00b42a;
          }

          .add-icon {
            color: #00b42a;
          }
        }

        .add-icon {
          color: #00b42a;
          font-size: 32px;
          font-weight: 200;
          margin-bottom: 4px;
          transition: all 0.3s;
        }

        .add-text {
          color: #00b42a;
          color: var(---el-color-primary, #059e84);
          text-align: center;
          text-overflow: ellipsis;
          font-family: "PingFang SC";
          font-size: 18px;
          font-style: normal;
          font-weight: 400;
          line-height: 26px;
        }
      }

      &-item {
        width: 248px;
        min-width: 248px;
        height: 255px;
        border-radius: 12px;
        border: 1px solid var(---el-border-color-light, #e6eaf0);
        background: var(---el-color-white, #fff);
        position: relative;
        cursor: pointer;

        &-logo {
          width: 100%;
          height: 180px;
          background: red;
          border-radius: 12px 12px 0 0;

          img {
            width: 100%;
            height: 100%;
          }
        }

        &-footer {
          border-top: 1px solid var(---el-border-color-light, #e6eaf0);
          padding: 12px 16px;

          .goodsName {
            overflow: hidden;
            color: var(---el-text-color-primary, #1c2026);
            text-align: center;
            text-overflow: ellipsis;
            font-family: "PingFang SC";
            font-size: 18px;
            font-style: normal;
            font-weight: 400;
            line-height: 26px;
            height: 26px;
          }

          .spec {
            color: var(---el-text-color-secondary, #7e8694);
            text-align: center;
            text-overflow: ellipsis;
            font-family: "PingFang SC";
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px;
          }
        }

        &-code {
          position: absolute;
          right: 0;
          top: 10px;
          display: inline-flex;
          padding: 2px 8px;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          border-radius: 22px 0px 0px 22px;
          background: var(---el-color-warning-light-9, #ffe4ba);
          backdrop-filter: blur(2px);
          color: var(---el-text-color-primary, #1c2026);
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
          letter-spacing: -0.01px;
        }
      }
    }

    &-search {
      margin-top: 20px;
      padding: 0 20px 20px 20px;
    }

    .el-input__wrapper {
      background: var(---el-color-normal-bg, #f6f7f9);
    }

    .el-input__inner {
      font-family: DIN;
      font-size: 24px;
      font-style: normal;
      font-weight: 400;
      line-height: 40px;
      height: 40px;

      &::placeholder {
        font-size: 24px;
      }
    }
  }
}

::v-deep(.tipModal) {
  border-radius: 16px 16px 16px 16px;

  .el-dialog__header {
    display: none;
  }

  .el-dialog__body {
    padding: 0;
    border-radius: 16px 16px 16px 16px;
  }

  .tip-container {
    padding: 40px;
    display: flex;
    flex-direction: column;
    position: relative;

    .close-icon {
      position: absolute;
      top: 12px;
      right: 12px;
    }

    .tips-header {
      color: var(---el-text-color-primary, #1c2026);
      font-family: "PingFang SC";
      font-size: 24px;
      font-style: normal;
      font-weight: 400;
      line-height: 32px;
      text-align: center;
    }

    .tips-desc {
      color: var(---el-text-color-regular, #505762);
      font-family: "PingFang SC";
      font-size: 24px;
      font-style: normal;
      font-weight: 400;
      line-height: 32px;
      margin: 16px 0;
      text-align: center;
    }

    .tips-item-wrp {
      display: flex;
      align-items: center;
      gap: 16px;

      .item {
        flex: 1;
        border-radius: 8px;
        background: var(---el-color-normal-bg, #f6f7f9);
        display: flex;
        padding: 16px;
        flex-direction: column;
        align-items: center;
        gap: 16px;

        .weight {
          display: flex;
          align-items: flex-end;

          &-num {
            color: var(---el-text-color-regular, #505762);
            text-align: center;
            font-family: DIN;
            font-size: 72px;
            font-style: normal;
            font-weight: 700;
            line-height: 70px;
            display: flex;
            align-items: center;

            .weight-unit {
              font-size: 12px;
              font-style: normal;
              font-weight: 500;
              line-height: 32px;
              font-style: normal;
              color: var(---el-text-color-regular, #505762);
            }

            &.diff {
              color: var(---el-color-danger-light-6, #f76560);
            }
          }

          &-unit {
            color: var(---el-text-color-regular, #505762);
            text-align: center;
            font-family: "PingFang SC";
            font-size: 24px;
            font-style: normal;
            font-weight: 500;
            line-height: 32px;
          }
        }

        .sku {
          color: var(---el-text-color-regular, #505762);
          line-height: 56px;
          min-height: 146px;
          text-overflow: ellipsis;
          display: flex;
          align-items: center;

          div {
            max-height: 56px;
            overflow: hidden;
            text-align: center;
            font-family: "PingFang SC";
            font-size: 48px;
            font-style: normal;
            font-weight: 500;
          }

          &.diff {
            color: var(---el-color-danger-light-6, #f76560);
          }
        }

        .weight-label {
          color: var(---el-text-color-regular, #505762);
          text-align: center;
          font-family: "PingFang SC";
          font-size: 18px;
          font-style: normal;
          font-weight: 400;
          line-height: 26px;
        }
      }
    }

    .tips-footer {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 24px;
      gap: 24px;

      .el-button {
        min-width: 138px;
      }
    }
  }

  &.lackModal {
    .tip-container {
      .tips-item-wrp {
        gap: 24px;
        align-items: flex-start;
        justify-content: center;

        .item {
          background: none;
          border-radius: 0;
          padding: 24px 0;
          gap: 8px;
          max-width: 240px;

          .box {
            padding: 24px 16px;
            gap: 16px;
            align-self: stretch;
            border-radius: 8px;
            border: 1px solid var(---el-text-color-secondary, #7e8694);
            color: var(---el-text-color-regular, #505762);
            text-align: center;
            font-family: "PingFang SC";
            font-size: 24px;
            font-style: normal;
            font-weight: 400;
            line-height: 32px;
            position: relative;
            cursor: pointer;

            &.active {
              color: var(---el-color-primary-light-4, #038a78);
              border: 1px solid var(---el-color-primary-light-6, #25b195);
            }

            .mark {
              width: 24px;
              height: 24px;
              background: #059e84;
              top: -1px;
              right: -1px;
              position: absolute;
              border-radius: 0 8px 0 8px;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }

          .lack-desc {
            color: var(---el-color-danger, #f53f3f);
            text-align: center;
            font-family: "PingFang SC";
            font-size: 18px;
            font-style: normal;
            font-weight: 300;
            line-height: 26px;
          }
        }
      }
    }
  }
}

:deep(.el-switch) {
  --el-switch-on-color: #059e84;
  --el-switch-off-color: #cfd9e4;

  .el-switch__label {
    color: #fff;
    margin-left: 10px;
    font-size: 14px;

    &.is-active {
      color: #fff;
    }
  }
}
</style>
