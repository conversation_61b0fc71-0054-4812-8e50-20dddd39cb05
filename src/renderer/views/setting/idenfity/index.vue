<template>
  <div class="page">
    <div class="header-bar">
      <el-icon @click="handleBack" size="28" color="#1C2026">
        <Back />
      </el-icon>
    </div>
    <div class="container">
      <div class="content">
        <div class="setting-section">
          <div class="section-title">{{ $t('idenfity.index.319197-0') }}</div>
          <div class="setting-group">
            <div class="setting-item">
              <div class="setting-label">{{ $t('idenfity.index.319197-1') }}</div>
              <el-switch v-model="sortingSettings.showVideoStream" @change="handleVideoStreamChange('sorting')" />
            </div>
            <div class="setting-item">
              <div class="setting-label">{{ $t('idenfity.index.319197-2') }}</div>
              <el-switch
                v-model="sortingSettings.autoRecognize"
                :disabled="!sortingSettings.showVideoStream"
              />
            </div>
            <div class="setting-item">
              <div class="setting-label">{{ $t('idenfity.index.319197-3') }}</div>
              <el-switch
                v-model="sortingSettings.autoCapture"
                :disabled="!sortingSettings.showVideoStream"
              />
            </div>
          </div>
        </div>

        <div class="setting-section">
          <div class="section-title">{{ $t('idenfity.index.319197-4') }}</div>
          <div class="setting-group">
            <div class="setting-item">
              <div class="setting-label">{{ $t('idenfity.index.319197-1') }}</div>
              <el-switch v-model="storeSettings.showVideoStream" @change="handleVideoStreamChange('store')" />
            </div>
            <div class="setting-item">
              <div class="setting-label">{{ $t('idenfity.index.319197-2') }}</div>
              <el-switch
                v-model="storeSettings.autoRecognize"
                :disabled="!storeSettings.showVideoStream"
              />
            </div>
            <div class="setting-item">
              <div class="setting-label">{{ $t('idenfity.index.319197-3') }}</div>
              <el-switch
                v-model="storeSettings.autoCapture"
                :disabled="!storeSettings.showVideoStream"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { ElMessageBox } from 'element-plus';
import { useRouter } from 'vue-router';
import { i18n } from '@renderer/i18n';

const router = useRouter();

// 返回上一页
function handleBack() {
  router.go(-1);
}

// 处理视频流显示开关变化
function handleVideoStreamChange(type: 'sorting' | 'store') {
  const settings = type === 'sorting' ? sortingSettings : storeSettings;

  // 如果是关闭操作，显示确认弹窗
  if (!settings.value.showVideoStream) {
    // 在弹出确认框之前，保存当前的设置状态
    const previousState = {
      showVideoStream: true, // 当前要关闭，所以之前是打开的
      autoRecognize: settings.value.autoRecognize,
      autoCapture: settings.value.autoCapture
    };

    ElMessageBox.confirm(
      i18n.global.t('idenfity.index.319197-5'),
      i18n.global.t('idenfity.index.319197-6'),
      {
        confirmButtonText: i18n.global.t('idenfity.index.319197-7'),
        cancelButtonText: i18n.global.t('idenfity.index.319197-8'),
        type: 'warning',
      }
    )
      .then(() => {
        // 用户点击确认，保持关闭状态
        settings.value.showVideoStream = false;
        // 自动关闭依赖的开关
        settings.value.autoRecognize = false;
        settings.value.autoCapture = false;
        saveSettings();
      })
      .catch(() => {
        // 用户点击取消，恢复所有设置到之前的状态
        settings.value.showVideoStream = previousState.showVideoStream;
        settings.value.autoRecognize = previousState.autoRecognize;
        settings.value.autoCapture = previousState.autoCapture;
        saveSettings();
      });
  }
}

// 定义分拣识别设置
const sortingSettings = ref({
  showVideoStream: true,
  autoRecognize: true,
  autoCapture: true
});

// 定义入库识别设置
const storeSettings = ref({
  showVideoStream: true,
  autoRecognize: true,
  autoCapture: true
});

// 监听开关变化
const saveSettings = async () => {
  try {
    // 保存分拣识别设置为一个对象
    localStorage.setItem('sortingSettings', JSON.stringify(sortingSettings.value));

    // 保存入库识别设置为一个对象
    localStorage.setItem('storeSettings', JSON.stringify(storeSettings.value));

    // 如果需要通过IPC调用保存到主进程或数据库，可以添加相应代码
    // 需要时取消注释以下代码并导入相关模块
    // import { invoke } from '@renderer/utils/ipcRenderer';
    // import { IpcChannel } from '../../../../ipc';
    // await invoke(IpcChannel.SaveSettings, {
    //   sortingSettings: sortingSettings.value,
    //   storeSettings: storeSettings.value
    // });
  } catch (error) {
    console.error(i18n.global.t('idenfity.index.319197-9'), error);
  }
};

// 加载设置
const loadSettings = () => {
  try {
    // 加载分拣识别设置
    const savedSortingSettings = localStorage.getItem('sortingSettings');
    if (savedSortingSettings) {
      const parsedSettings = JSON.parse(savedSortingSettings);
      sortingSettings.value = {
        ...sortingSettings.value,
        ...parsedSettings
      };
    }

    // 加载入库识别设置
    const savedStoreSettings = localStorage.getItem('storeSettings');
    if (savedStoreSettings) {
      const parsedSettings = JSON.parse(savedStoreSettings);
      storeSettings.value = {
        ...storeSettings.value,
        ...parsedSettings
      };
    }

    // 如果需要从主进程或数据库加载设置，可以添加相应代码
    // 需要时取消注释以下代码并导入相关模块
    // import { invoke } from '@renderer/utils/ipcRenderer';
    // import { IpcChannel } from '../../../../ipc';
    // const settings = await invoke(IpcChannel.GetSettings);
    // sortingSettings.value = settings.sortingSettings;
    // storeSettings.value = settings.storeSettings;
  } catch (error) {
    console.error(i18n.global.t('idenfity.index.319197-10'), error);
  }
};

// 监听开关变化并保存设置
watch(sortingSettings, (newVal) => {
  // 当视频流显示开关关闭时，自动关闭其他开关
  if (!newVal.showVideoStream) {
    sortingSettings.value.autoRecognize = false;
    sortingSettings.value.autoCapture = false;
  }
  saveSettings();
}, { deep: true });

watch(storeSettings, (newVal) => {
  // 当视频流显示开关关闭时，自动关闭其他开关
  if (!newVal.showVideoStream) {
    storeSettings.value.autoRecognize = false;
    storeSettings.value.autoCapture = false;
  }
  saveSettings();
}, { deep: true });

onMounted(() => {
    loadSettings();
});
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: #E6EAF0;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
}

.header-bar {
  display: flex;
  align-items: center;
  padding: 20px 20px 16px 20px;
  z-index: 21;
  gap: 24px;
  background: #E6EAF0;
}

.container {
  display: flex;
  flex: 1;
  overflow: hidden;
  padding: 0 20px 20px 20px;

  .content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
  }
}

.setting-section {
  margin-bottom: 30px;

  &:last-child {
    margin-bottom: 0;
  }

  .section-title {
    font-family: "PingFang SC";
    font-size: 18px;
    font-weight: 500;
    color: #1C2026;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #cfd9e4;
  }

  .setting-group {
    padding: 0 16px;
  }
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #e0e6ec;

  &:last-child {
    border-bottom: none;
  }

  .setting-label {
    color: var(---el-text-color-primary, #1C2026);
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
  }
}

:deep(.el-switch) {
  --el-switch-on-color: #059e84;
  --el-switch-off-color: #cfd9e4;
}
</style>

<style lang="scss">
.message-box-confirm {
  .el-button:focus,
  .el-button:hover {
    color: #059e84;
    border-color: #79d8bf;
    background-color: #e8fff7;
  }

  .el-button--primary {
    background: #009f64 !important;
    color: #fff !important;
    border-color: #009f64 !important;
  }

  .el-button--primary:hover {
    background: rgb(55, 177, 157);
    color: #fff;
    border-color: rgb(55, 177, 157);
  }

  .el-message-box__headerbtn:hover .el-message-box__close {
    color: #009f64;
  }
}
</style>
