<template>
  <div class="common-layout">
    <div class="div-main-st">
      <div class="left-div-title">
        <el-icon @click="pageBack" size="28" color="#1C2026">
          <Back />
        </el-icon>
      </div>
    </div>
    <div class="el-main">
      <div class="video-container">
        <video
          ref="videoElement"
          class="video-item"
          :class="{ empty: !isLoadVideo }"
        ></video>
        <canvas ref="canvasElement" style="display: none"></canvas>
        <div class="header-bar">
          <div class="order-bar bar">
            <div class="logo">
              <img
                v-if="selectSortedItem?.picUrls?.[0]"
                :src="selectSortedItem?.picUrls?.[0]"
              />
              <img
                v-else
                src="https://oss-public.yunlizhi.cn/frontend/delivery-center/sort-weight/img_empty.png"
              />
            </div>
            <div class="content">
              <div class="spuName">
                {{
                  selectSortedItem?.spuName || selectSortedItem?.spuName || "--"
                }}
              </div>
              <div>
                <span class="label">{{ $t("recognize.index.532611-0") }}</span
                ><span class="value">{{
                  selectSortedItem?.businessUserName ||
                  selectSortedItem?.defaultSupplierName ||
                  "--"
                }}</span>
              </div>
              <div>
                <span class="label">{{ $t("recognize.index.532611-1") }} </span>
                <span class="value">
                  {{ orderWeight || "--" }}
                  {{
                    selectSortedItem?.whetherStandard !== "YES"
                      ? "kg"
                      : selectSortedItem?.unit
                  }}
                </span>
              </div>
            </div>
          </div>
          <div class="sorted-bar bar">
            <div class="data-entry">
              <div class="switch-container">
                <el-switch
                  v-model="allowManualEdit"
                  :active-text="$t('recognize.index.532611-2')"
                  inactive-text=""
                />
              </div>
              <div style="margin-bottom: 8px">
                <span>{{
                  selectSortedItem?.whetherStandard !== "YES"
                    ? $t("recognize.index.532611-3")
                    : $t("recognize.index.532611-4")
                }}</span>
              </div>
              <div style="flex: 1">
                <el-input
                  ref="inputRef"
                  v-model="orderWeight"
                  @input="handleInput"
                  style="width: 100%; height: 60px"
                  :disabled="!allowManualEdit"
                >
                  <template #append>
                    <div class="unit">
                      {{
                        selectSortedItem?.whetherStandard !== "YES"
                          ? "kg"
                          : selectSortedItem?.unit
                      }}
                    </div>
                  </template>
                </el-input>
              </div>
            </div>
          </div>
        </div>

        <div class="footer-btn">
          <div class="r-section">
            <el-button
              class="high-ligth-blue"
              @click="handleValidateBeforeSorting(true)"
            >
              {{ $t("recognize.index.532611-5") }}
            </el-button>
            <el-button
              v-if="selectSortedItem"
              class="high-ligth-green"
              @click="handleValidateBeforeSorting(false)"
            >
              {{ $t("recognize.index.532611-6") }}
            </el-button>
          </div>
        </div>
      </div>
      <div class="evidence-container">
        <div>
          <div
            v-if="isEvidence"
            class="content"
            :style="{ height: imgHeight + 'px' }"
          >
            <div class="pic">
              <img ref="evidenceContainer" :src="evidenceUrl" />
            </div>
          </div>
          <div
            class="empty-content"
            :style="{ height: imgHeight + 'px', minHeight: 275 + 'px' }"
            v-else
          >
            <div class="empty-logo"></div>
            <div class="empty-desc">{{ $t("recognize.index.532611-7") }}</div>
          </div>

          <div class="recognize-rst">
            <div class="label">{{ $t("recognize.index.532611-8") }}</div>
            <el-input v-model="skuName.spuName" disabled>
              <template #append>
                <el-button class="normal" @click="handleOpenLearn">{{
                  $t("recognize.index.532611-9")
                }}</el-button>
              </template>
            </el-input>
          </div>
        </div>

        <div class="footer">
          <el-button @click="capture(true)" class="high-ligth-blue">
            {{ $t("recognize.index.532611-10") }}
          </el-button>
          <el-button @click="handleRecognize(true)" class="normal">
            {{ $t("recognize.index.532611-11") }}
          </el-button>
        </div>
      </div>
    </div>

    <el-dialog
      v-model="learnVisible"
      title=""
      width="980"
      center
      :close-on-click-modal="false"
      :show-close="false"
      top="25vh"
      class="learnModal"
    >
      <div class="learn">
        <div class="learn-header">
          <div>{{ $t("recognize.index.532611-12") }}</div>
          <el-icon @click="learnVisible = false" size="24" color="#1C2026">
            <Close />
          </el-icon>
        </div>

        <div class="learn-search">
          <el-input
            v-model="searchKey"
            :placeholder="$t('recognize.index.532611-13')"
            clearable
          />
        </div>

        <div class="learn-result">
          <div class="add-item learn-result-item" @click="handleAddProduct">
            <div class="add-icon">+</div>
            <div class="add-text">{{ $t("recognize.index.532611-14") }}</div>
          </div>
          <div
            v-for="item in RecommendList"
            @click="handleRecoginzeConfirm(item)"
            :key="item.spuCode"
            class="learn-result-item"
          >
            <div class="learn-result-item-logo">
              <img :src="item?.picUrls?.[0]" />
            </div>
            <div class="learn-result-item-footer">
              <div class="goodsName">{{ item.spuName }}</div>
              <div class="spec">{{ item.baseUnitName }}</div>
            </div>
            <div class="learn-result-item-code">{{ item.spuCode }}</div>
          </div>
        </div>
      </div>
    </el-dialog>

    <el-dialog
      v-model="lackModalVisible"
      title=""
      width="516"
      center
      :close-on-click-modal="false"
      :show-close="false"
      top="25vh"
      class="tipModal lackModal addProductModal"
    >
      <div class="tip-container">
        <div class="tips-header">{{ $t("recognize.index.532611-14") }}</div>
        <div class="tips-content">
          <div>
            <div class="label-wrap">
              <span class="required">*</span>
              <span class="label">{{ $t("recognize.index.532611-15") }}</span>
            </div>
            <el-input
              v-model="newProductName"
              :placeholder="$t('recognize.index.532611-16')"
              class="input"
            />
          </div>
        </div>
        <div class="tips-footer">
          <el-button class="normal" @click="lackModalVisible = false">
            {{ $t("recognize.index.532611-17") }}
          </el-button>
          <el-button class="high-ligth-green" @click="handleSaveProduct">
            {{ $t("recognize.index.532611-18") }}
          </el-button>
        </div>
      </div>
    </el-dialog>

    <el-dialog
      v-model="successModalVisible"
      title=""
      width="516"
      center
      :close-on-click-modal="false"
      :show-close="false"
      top="25vh"
      class="tipModal lackModal addProductModal"
    >
      <div class="tip-container">
        <div class="tips-header">{{ $t("recognize.index.532611-19") }}</div>

        <el-icon
          class="close-icon"
          @click="successModalVisible = false"
          size="24"
          color="#1C2026"
        >
          <Close />
        </el-icon>
        <div class="tips-content">
          <div style="font-size: 24px; font-weight: 600; text-align: center">
            {{ selectSortedItem?.spuName || "--" }}
          </div>
          <div style="text-align: center; font-size: 18px; margin-top: 12px">
            {{ orderWeight || "--" }}
            {{
              selectSortedItem?.whetherStandard !== "YES"
                ? "kg"
                : selectSortedItem?.unit
            }}
          </div>
        </div>
        <div class="tips-footer">
          <el-button class="normal" @click="handleCancelInStore">
            {{ $t("recognize.index.532611-20") }}
          </el-button>
          <el-button class="high-ligth-green" @click="handleContinueInStore">
            {{ $t("recognize.index.532611-21") }}
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, nextTick, onUnmounted, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { fetchSpuMap } from "@renderer/api/login";
import { upload } from "@renderer/api/sorting";
import { userInfoT } from "@renderer/store/modules/template";
import {
  addGoodsSpuInfo,
  identifyIn,
  cancelWarehouseIn,
} from "@renderer/api/inStore";
import dayjs from "dayjs";
import {
  invoke,
  serialClose,
  recongnize,
  recognizeConfirm,
  connect,
} from "../../../utils/ipcRenderer";
import { IpcChannel } from "../../../../ipc";
import { i18n } from "@renderer/i18n";

const { ipcRenderer } = require("electron");
const userInfoTState = userInfoT();
const router = useRouter();
const route = useRoute();
const inputRef = ref(null);
const videoElement = ref(null);
const canvasElement = ref(null);
const evidenceContainer = ref(null);
const isEvidence = ref(false);
const evidenceUrl = ref("");
const orderWeight = ref("0.00");
const isPrint = ref(route.query.isPrint === "true");
const RecommendList = ref([]);
const captureLoading = ref(false);
const isLoadVideo = ref(false);
const learnVisible = ref(false);
const skuName = ref({ spuName: "", spuCode: "" });
const searchKey = ref("");
const requestIdTemp = ref("");
const logger = ref("");
const allowManualEdit = ref(false);
const imgHeight = ref(0);
const selectSortedItem = ref({});
const lackModalVisible = ref(false);
const newProductName = ref("");
const successModalVisible = ref(false);
const inStoreInfo = ref({});

// 入库识别设置
const storeSettings = ref({
  showVideoStream: true, // 视频流显示开关
  autoRecognize: true, // 自动识别开关
  autoCapture: true, // 自动拍照开关
});

//返回入库列表
const pageBack = () => {
  router.go(-1);
};

const watermarkImage1 = new Image();
watermarkImage1.src =
  "https://oss-public.yunlizhi.cn/frontend/delivery-center/fe-saas-fresh-admin/location.png";
const watermarkImage2 = new Image();
watermarkImage2.src =
  "https://oss-public.yunlizhi.cn/frontend/delivery-center/fe-saas-fresh-admin/network.png";
const watermarkImage3 = new Image();
watermarkImage3.src =
  "https://oss-public.yunlizhi.cn/frontend/delivery-center/fe-saas-fresh-admin/time.png";
const watermarkImage4 = new Image();
watermarkImage4.src =
  "https://oss-public.yunlizhi.cn/frontend/delivery-center/fe-saas-fresh-admin/user.png";

let mediaStream = null;

const openVideoSuccsess = (stream) => {
  mediaStream = stream;
  videoElement.value.srcObject = stream;
  videoElement.value.play();
  setTimeout(() => {
    isLoadVideo.value = true;
  }, 600);
};

const getUserMedia = () => {
  if (storeSettings.value.showVideoStream) {
    navigator.mediaDevices
      .getUserMedia({ video: true })
      .then(function (stream) {
        openVideoSuccsess(stream);
      })
      .catch(function (err) {
        isLoadVideo.value = false;
        ElMessage.warning(i18n.global.t("recognize.index.532611-22"));
      });
  } else {
    isLoadVideo.value = false;
  }
};

const closeCamera = () => {
  mediaStream?.getTracks().forEach((track) => {
    track.stop(); // 立即停止轨道
    track.enabled = false; // 双重保险
  });
  if (videoElement.value) {
    videoElement.value.srcObject = null;
  }
  mediaStream = null;
};

const capture = (isTip) => {
  if (!isLoadVideo.value && isTip) {
    ElMessage.warning(i18n.global.t("recognize.index.532611-23"));
    return;
  }

  if (!selectSortedItem.value?.id) {
    if (isTip) {
      ElMessage.warning(i18n.global.t("recognize.index.532611-24"));
    }
    return;
  }

  if (!canvasElement.value) {
    return;
  }

  try {
    captureLoading.value = true;
    const video = videoElement.value;
    const canvas = canvasElement.value;
    const context = canvas?.getContext("2d");
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    imgHeight.value = 367 / (canvas.width / canvas.height);
    context.drawImage(video, 0, 0, canvas.width, canvas.height);
    // 绘制水印
    const addressPosY = canvas.height - 12;
    const dataTime = dayjs().format("YYYY-MM-DD HH:mm:ss");

    // 计算背景区域
    const topTextY = addressPosY - 90; // 最顶部文字（客户名称）的Y坐标
    const bgTop = topTextY - 25; // 背景顶部位置
    const bgHeight = canvas.height - bgTop; // 背景高度

    // 绘制背景层
    context.fillStyle = "rgba(94, 99, 105, 0.70)";
    context.fillRect(
      0, // x起始位置（左边缘）
      bgTop, // y起始位置（顶部）
      canvas.width, // 宽度（右边缘）
      bgHeight // 高度（到底部）
    );
    context.font = "16px PingFang SC";
    context.fillStyle = "rgba(255, 255, 255, 1)";
    context.fillText(
      i18n.global.t("recognize.index.532611-25", [userInfoTState.getUserNameT]),
      42,
      addressPosY - 12
    );
    context.fillText(
      i18n.global.t("recognize.index.532611-26", [
        selectSortedItem.value.warehouseName ?? "--",
      ]),
      39,
      addressPosY - 36
    );
    context.fillText(
      i18n.global.t("recognize.index.532611-27", [dataTime]),
      42,
      addressPosY - 63
    );
    context.fillText(
      i18n.global.t("recognize.index.532611-28", [
        selectSortedItem.value.businessUserName ?? "--",
      ]),
      42,
      addressPosY - 88
    );
    context.drawImage(watermarkImage1, 16, addressPosY - 23, 18, 16);
    context.drawImage(watermarkImage2, 10, addressPosY - 53, 30, 22);
    context.drawImage(watermarkImage3, 16, addressPosY - 77, 18, 16);
    context.drawImage(watermarkImage4, 16, addressPosY - 102, 18, 16);
    const dataUrl = canvas.toDataURL("image/png");
    isEvidence.value = true;
    evidenceUrl.value = dataUrl;
    captureLoading.value = false;
  } catch (error) {
    logger.value = error;
  }
};

const handleInput = (event) => {
  if (!allowManualEdit.value) {
    return;
  }

  let value = event;
  if (selectSortedItem.value?.whetherStandard == "NO") {
    value =
      value
        .replace(/[^\d^\.]+/g, "")
        .replace(/^0+(\d)/, "$1")
        .replace(/^\./, "0.")
        .match(/^\d*(\.?\d{0,2})/g)[0] || "";
  }
  if (value.length > 8) {
    value = value.slice(0, 8);
  }
  orderWeight.value = value.trim();
};

const handleEdit = () => {
  inputRef.value.focus();
};

/**
 * 入库之前 进行品类，重量校验
 */
const handleValidateBeforeSorting = (val) => {
  if (!orderWeight.value) {
    ElMessage.error(i18n.global.t("recognize.index.532611-29"));
    return;
  }

  if (!/^-?\d+(\.\d+)?$/.test(orderWeight.value)) {
    ElMessage.error(i18n.global.t("recognize.index.532611-30"));
    orderWeight.value = "";
    handleEdit();
    return;
  }

  if (
    selectSortedItem.value?.whetherStandard === "YES" &&
    !Number.isInteger(parseFloat(orderWeight.value))
  ) {
    ElMessage.error(i18n.global.t("recognize.index.532611-31"));
    orderWeight.value = "";
    handleEdit();
    return;
  }

  printOrSortBtn(val);
};

/**
 * 入库
 */
async function printOrSortBtn(val: boolean) {
  //判断是否  配置打印小票
  if (isPrint.value && !val) {
    ElMessage.warning(i18n.global.t("recognize.index.532611-32"));
    return;
  }
  //判断是否  配置过打印机
  if (!userInfoTState.getPrinterName && !isPrint.value) {
    ElMessage.warning(i18n.global.t("recognize.index.532611-33"));
    return;
  }

  let res = "";
  if (evidenceUrl.value) {
    const blob = dataURLToBlob(evidenceUrl.value);
    const file = new File([blob], "image.png", { type: "image/png" }); // 包装成 File 对象=
    // 使用 FormData 包装（更规范）
    const formData = new FormData();
    formData.append("file", file);
    res = await upload(formData);
  }

  if (!selectSortedItem.value?.id) {
    ElMessage.warning(i18n.global.t("recognize.index.532611-24"));
    return;
  }

  let dataParm = {
    sort: val, // true  入库   false  打印
    warehouseInTime: dayjs().format("YYYY-MM-DD HH:mm:ss"),
    warehouseId: selectSortedItem.value?.warehouseId,
    supplierId: selectSortedItem.value?.supplierId,
    supplierName: selectSortedItem.value?.businessUserName,
    shopId: userInfoTState.getShopList?.[0]?.id,
    goodsList: [
      {
        spuId: selectSortedItem.value.id,
        spuName: selectSortedItem.value.spuName,
        reserveNumber: selectSortedItem.value.reserveNumber,
        price: selectSortedItem.value.price,
        posInPic: evidenceUrl.value ? res?.data?.data?.url ?? "" : "",
        realNumber: Number(
          orderWeight.value * selectSortedItem.value.convertRatio
        ).toFixed(2),
        skuId: selectSortedItem.value.skus?.[0]?.id,
      },
    ],
  };

  if (val) {
    // 入库并打印
    sendPrintSort(dataParm);
  } else {
    // 直接打印
    !isPrint.value &&
      sendPrintStart([
        {
          ...selectSortedItem.value,
          orderWeight: orderWeight.value,
          userName: userInfoTState.getUserNameT,
          storeTime: dayjs().format("YYYY-MM-DD HH:mm:ss"),
        },
      ]);
  }
}

/**
 *
 * @param dataParm 识别
 */
async function sendPrintSort(dataParm) {
  await identifyIn(dataParm).then((res) => {
    inStoreInfo.value = res.data.data;
    //开始打印
    !isPrint.value &&
      sendPrintStart([
        {
          ...selectSortedItem.value,
          orderWeight: orderWeight.value,
          userName: userInfoTState.getUserNameT,
          storeTime: dayjs().format("YYYY-MM-DD HH:mm:ss"),
        },
      ]);

    //如果是打印，不切换颜色
    if (!dataParm.sort) {
      // 打印
      //TODO
    } else {
      successModalVisible.value = true;
    }
  });
}

const handleRecoginzeConfirm = async (data) => {
  learnVisible.value = false;
  skuName.value.spuName = data.spuName;
  skuName.value.spuCode = data.spuCode;
  selectSortedItem.value = { ...data, convertRatio: data?.skus?.[0]?.convertRatio ?? 1 };
  requestIdTemp.value = "";
  recognizeConfirm(data.spuCode, data.spuName);
};

const handleOpenLearn = () => {
  if (!isLoadVideo.value) {
    ElMessage.warning(i18n.global.t("recognize.index.532611-34"));
    return;
  }
  if (requestIdTemp.value) {
    RecommendList.value = [];
    searchKey.value = "";
    learnVisible.value = true;
  } else {
    ElMessage.warning(i18n.global.t("recognize.index.532611-35"));
  }
};

// 开启打印
async function sendPrintStart(val) {
  nextTick(() => {
    userInfoTState.setPerinterObj(JSON.stringify(val));
    invoke(IpcChannel.OpenWin, { url: "/sortPrintPage" });
  });
}

const dataURLToBlob = (dataURL) => {
  const [meta, base64Data] = dataURL.split(",");
  const mime = meta.match(/:(.*?);/)[1];
  const binary = atob(base64Data);
  const array = [];
  for (let i = 0; i < binary.length; i++) {
    array.push(binary.charCodeAt(i));
  }
  // 返回 Blob 对象
  return new Blob([new Uint8Array(array)], { type: mime });
};

const compressCanvasToBase64 = (sourceCanvas, quality = 1) => {
  const offscreenCanvas = document.createElement("canvas");
  const ctx = offscreenCanvas.getContext("2d");

  // 设置目标尺寸（可选：保持宽高比）
  const srcWidth = videoElement.value.videoWidth;
  const srcHeight = videoElement.value.videoHeight;
  const targetWidth = 640;
  const targetHeight = 480;

  // 计算保持比例的缩放尺寸
  const scaleRatio = Math.min(targetWidth / srcWidth, targetHeight / srcHeight);
  const scaledWidth = srcWidth * scaleRatio;
  const scaledHeight = srcHeight * scaleRatio;

  // 设置离屏Canvas尺寸（可选居中绘制）
  offscreenCanvas.width = targetWidth;
  offscreenCanvas.height = targetHeight;

  // 提高缩放质量
  ctx.imageSmoothingEnabled = true;
  ctx.imageSmoothingQuality = "high";

  // 居中绘制并缩放，保持比例（若需拉伸则直接使用目标尺寸）
  ctx.drawImage(
    sourceCanvas,
    0,
    0,
    srcWidth,
    srcHeight,
    (targetWidth - scaledWidth) / 2, // 水平居中
    (targetHeight - scaledHeight) / 2, // 垂直居中
    scaledWidth,
    scaledHeight
  );
  return offscreenCanvas.toDataURL("image/jpg", 0.7);
};

const handleRecognize = (isTip) => {
  if (isTip && !isLoadVideo.value) {
    ElMessage.warning(i18n.global.t("recognize.index.532611-36"));
    return;
  }
  const base64Url = compressCanvasToBase64(videoElement.value);
  recongnize(base64Url.split(",")[1]);
};

const handleSearchChange = async (val) => {
  if (val) {
    const result = await fetchSpuMap({ name: val });
    const filterRst = (result?.data?.data?.records ?? []).filter(
      (item, ind) => item.spuCode !== selectSortedItem.value.spuCode && ind < 5
    );
    RecommendList.value = [
      ...filterRst.map((item) => ({
        ...item,
        url: item.picUrl?.[0],
        unit: item.baseUnitName,
        spuCode: item.spuCode,
        spuName: item.name,
      })),
    ];
  } else {
    RecommendList.value = [];
  }
};

const handleCancelInStore = async () => {
  try {
    await cancelWarehouseIn(inStoreInfo.value);
    ElMessage.success(i18n.global.t("recognize.index.532611-37"));
    successModalVisible.value = false;
    selectSortedItem.value = {};
    inStoreInfo.value = {};
    skuName.value = { spuName: "", spuCode: "" };
    requestIdTemp.value = "";
  } catch (error) {
    console.error(i18n.global.t("recognize.index.532611-38"), error);
  }
};

const handleContinueInStore = () => {
  successModalVisible.value = false;
  selectSortedItem.value = {};
  inStoreInfo.value = {};
  skuName.value = { spuName: "", spuCode: "" };
  requestIdTemp.value = "";
};

ipcRenderer.on("weight-update", (event, data) => {
  if (
    data?.status === "S" &&
    orderWeight.value !== data.weight &&
    isLoadVideo &&
    !allowManualEdit.value
  ) {
    orderWeight.value = data.weight;
    if (
      !["0", "", 0, undefined, null].includes(data.weight) &&
      data.weight > 0.05
    ) {
      // 根据设置决定是否自动识别和自动拍照
      if (storeSettings.value.autoRecognize) {
        handleRecognize(false);
      }
      if (storeSettings.value.autoCapture) {
        capture(false);
      }
    }
  }
});

const fetchSkus = (code) => {
  let cache = "";
  return async () => {
    if (cache === code) {
      return;
    }
    cache = code;
    const res = await fetchSpuMap({ spuCode: code });
    skuName.value.spuName = res?.data?.data?.records?.[0]?.name ?? "";
    skuName.value.spuCode = res?.data?.data?.records?.[0]?.spuCode ?? "";
    selectSortedItem.value = { ...(res?.data?.data?.records?.[0] ?? {}), convertRatio: 1 };
  };
};

/**
 * 识别服务向Electron发出的消息
 */
ipcRenderer.on("electron-receive-message", async (event, message) => {
  const { cmd, data, status, requestId } = JSON.parse(message ?? {});
  console.log(
    JSON.parse(message ?? {}),
    "JSON.parse(message ?? {})JSON.parse(message ?? {})"
  );
  if (cmd == 200 && status === 0) {
    requestIdTemp.value = requestId;
    if (data.length) {
      fetchSkus(data[0].code)();
    } else {
      handleOpenLearn();
    }
  }
});

ipcRenderer.on("electron-send-message", (event, message) => {});

// 连接串口
connect("COM2");

watch(
  () => searchKey.value,
  (val) => {
    handleSearchChange(val);
  }
);

watch(allowManualEdit, (newValue) => {
  localStorage.setItem("weightEditMode", newValue ? "1" : "0");
});

const handleAddProduct = () => {
  lackModalVisible.value = true;
};

const handleSaveProduct = async () => {
  if (!newProductName.value) {
    ElMessage.error(i18n.global.t("recognize.index.532611-16"));
    return;
  }

  // 先进行截图
  !evidenceUrl.value && capture(false);

  if (!evidenceUrl.value) {
    ElMessage.error(i18n.global.t("recognize.index.532611-39"));
    return;
  }

  // 上传图片
  const blob = dataURLToBlob(evidenceUrl.value);
  const file = new File([blob], "image.png", { type: "image/png" });
  const formData = new FormData();
  formData.append("file", file);
  try {
    const uploadRes = await upload(formData);
    const imageUrl = uploadRes?.data?.data?.url;

    if (!imageUrl) {
      ElMessage.error(i18n.global.t("recognize.index.532611-40"));
      return;
    }

    // 保存商品信息
    const params = {
      shopId: userInfoTState.getShopList?.[0]?.id,
      spuName: newProductName.value,
      posInPic: imageUrl,
    };

    const res = await addGoodsSpuInfo(params);

    if (res?.data?.code === 0) {
      ElMessage.success(i18n.global.t("recognize.index.532611-41"));
      selectSortedItem.value = res.data.data;
      lackModalVisible.value = false;
      learnVisible.value = false;
      newProductName.value = "";
    } else {
      ElMessage.error(
        res?.data?.message || i18n.global.t("recognize.index.532611-42")
      );
    }
  } catch (error) {
    console.error(i18n.global.t("recognize.index.532611-43"), error);
  }
};

// 加载入库识别设置
const loadStoreSettings = () => {
  try {
    const savedStoreSettings = localStorage.getItem("storeSettings");
    if (savedStoreSettings) {
      const parsedSettings = JSON.parse(savedStoreSettings);
      storeSettings.value = {
        ...storeSettings.value,
        ...parsedSettings,
      };
    }
  } catch (error) {
    console.error(i18n.global.t("video.index.961168-52"), error);
  }
};

onMounted(async () => {
  loadStoreSettings();
  getUserMedia();
});

onUnmounted(() => {
  serialClose();
  // 离开页面关闭摄像头
  closeCamera();
});
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.common-layout {
  display: flex;
  flex-direction: column;
  height: 100%;
}

::v-deep(.el-button) {
  display: inline-flex;
  box-sizing: border-box;
  height: auto;
  line-height: 22px;
  padding: 14px 20px;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  border-radius: 12px;
  border: none;
  color: var(---el-text-color-primary, #1c2026);
  font-family: "PingFang SC";
  font-size: 24px;
  font-style: normal;
  font-weight: 400;
  line-height: 32px;

  & + .el-button {
    margin-left: 0;
  }

  & > span {
    gap: 4px;
  }
}

::v-deep(.el-button.normal) {
  border: 1px solid #b0b7c2;
  background: #fff;
  padding: 12px 20px;
  color: #505762;
  font-size: 24px;
  font-style: normal;
  font-weight: 400;
  line-height: 32px;
  border: 1px solid var(---el-text-color-placeholder, #b0b7c2);
  background: linear-gradient(180deg, #fff 0%, #bbc1c3 100%);

  &.disabled {
    background: rgba(92, 96, 100) !important;
    border: 1px solid rgba(92, 96, 100) !important;
    color: #1c2026;
  }
}

::v-deep(.el-button.high-ligth-blue) {
  border: 1px solid #9bedff;
  background: linear-gradient(180deg, #b5f2ff 28.57%, #2b8be4 125%);
}

::v-deep(.el-button.high-ligth-green) {
  border: 1px solid #6dd8b1;
  background: linear-gradient(180deg, #b5ffdb 28.57%, #00af70 125%);
}

::v-deep(.data-entry .el-input__wrapper) {
  background: rgba(0, 0, 0, 0) !important;
  border: none !important;
  box-shadow: none !important;
  padding-right: 0px !important;
}

::v-deep(.data-entry .el-input.is-disabled .el-input__wrapper) {
  background: rgba(0, 0, 0, 0) !important;
  border: none !important;
  box-shadow: none !important;
  opacity: 1;
}

::v-deep(.data-entry .el-input-group__append) {
  background: none;
  border: none;
  box-shadow: none;
  padding: 0;
  display: flex;
  align-items: flex-end;
}

::v-deep(.data-entry .el-input) {
  border: none !important;
}

::v-deep(.data-entry .el-input__inner) {
  text-align: center !important;
  color: var(---el-text-color-primary, #f0f6fc);
  text-align: center;
  font-family: DIN;
  font-size: 72px;
  font-style: normal;
  font-weight: 700;
  line-height: 60px;
  height: 60px;
}

::v-deep(.data-entry .el-input.is-disabled .el-input__inner) {
  color: var(---el-text-color-primary, #f0f6fc);
  -webkit-text-fill-color: var(---el-text-color-primary, #f0f6fc);
  background-color: transparent;
  cursor: text;
}

::v-deep(.el-dialog.recongize) {
  background: rgba(0, 0, 0, 0);
  box-shadow: none;

  .el-dialog__header,
  .el-dialog__body,
  .el-dialog__footer {
    padding: 0 !important;
  }

  .el-dialog__footer {
    margin-top: 40px;
  }
}

::v-deep(.el-checkbox__inner::after) {
  box-sizing: content-box;
  content: "";
  border: 1px solid transparent;
  border-left: 0;
  border-top: 0;
  height: 20px;
  left: 14px;
  position: absolute;
  top: 6px;
  transform: rotate(45deg) scaleY(0);
  width: 10px;
  transition: transform 0.15s ease-in 50ms;
  transform-origin: center;
}

::v-deep(.el-checkbox) {
  position: absolute;
  top: -2px;
  right: -2px;
}

::v-deep(.el-checkbox:last-of-type) {
  padding-right: 0;
}

::v-deep(.el-checkbox.el-checkbox--large .el-checkbox__inner) {
  background: #059e84;
  width: 41px;
  height: 41px;
  border-bottom-left-radius: 16px;
  border-top-right-radius: 16px;
}

.title {
  display: flex;
  padding: 12px 20px;
  align-items: center;
  gap: 8px;
  align-self: stretch;
  background: linear-gradient(90deg, #185a9d 0%, #43cea2 100%);
  color: #fff;
  font-family: PingFang SC;
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: 26px;
  -webkit-app-region: drag;
}

.el-main {
  height: calc(100% - 62px);
  display: flex;
  align-items: center;
  padding: 0 !important;
  overflow: hidden;
  background: #a6b6c8;

  .video-container {
    height: 100%;
    flex: 1;
    position: relative;

    .canvas {
      width: 100%;
      height: calc(100% - 88px);
    }

    .header-bar {
      width: 100%;
      height: 164px;
      box-sizing: border-box;
      position: absolute;
      left: 0;
      top: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;

      .order-bar {
        flex: 1;
        max-width: 480px;
        display: flex;
        align-items: center;
        border: 1px solid #e6eaf0;
        background: #fff;
        padding: 20px;

        .logo {
          width: 115px;
          height: 115px;
          margin-right: 16px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          overflow: hidden;

          img {
            width: 100%;
            height: auto;
            object-fit: contain;
            border-radius: 12px;
          }
        }

        .content {
          color: #f0f6fc;
          font-size: 24px;
          font-style: normal;
          line-height: 32px;

          .spuName {
            max-width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            height: 32px;
          }

          .label {
            width: 72px;
            color: var(---el-text-color-regular, #b1bac6);
            font-family: "PingFang SC";
            font-size: 18px;
            font-style: normal;
            font-weight: 300;
            line-height: 26px;
            display: inline-block;
            margin-right: 20px;
          }

          .value {
            color: #f0f6fc;
            font-size: 18px;
            font-weight: 300;
          }
        }
      }

      .sorted-bar {
        width: 385px;
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .icon {
          position: absolute;
          top: 16px;
          right: 16px;
          width: 24px;
          height: 24px;
          background: url("../../assets/img/edit-icon.png") no-repeat;
          background-size: cover;
          cursor: pointer;
        }

        .data-entry {
          display: flex;
          flex-direction: column;
          // justify-content: center;
          max-width: 68%;
          padding-bottom: 8px;
          border-bottom: 2px solid var(---el-text-color-placeholder, #b0b7c2);
          color: var(---el-border-color-extra-light, #fafbfc);
          text-align: center;
          /* 标准/500/H1-24 */
          font-family: "PingFang SC";
          font-size: 24px;
          font-style: normal;
          font-weight: 400;
          line-height: 32px;
          // position: relative;

          .unit {
            color: var(---el-text-color-primary, #f0f6fc);
            text-align: left;
            font-family: "PingFang SC";
            font-size: 28px;
            font-style: normal;
            font-weight: 400;
            line-height: 36px;
          }

          .switch-container {
            position: absolute;
            top: 0;
            right: 8px;
          }
        }

        .desc {
          color: #b1bac6;
          text-align: center;
          font-size: 18px;
          font-style: normal;
          font-weight: 300;
          line-height: 26px;
          margin-top: 12px;
        }
      }

      .bar {
        display: flex;
        padding: 16px;
        border-radius: 12px;
        border: 1px solid rgba(255, 255, 255, 0.6);
        background: rgba(33, 38, 45, 0.65);
        backdrop-filter: blur(4px);
        backdrop-filter: blur(4px);
        height: 100%;
      }
    }

    .video-item {
      width: 100%;
      height: calc(100% - 88px);
      object-fit: cover;
      border-top-left-radius: 12px;

      &.empty {
        background: url("../../assets/img/empty-video.png") no-repeat;
        background-size: cover;
      }
    }

    .footer-btn {
      width: 100%;
      height: 88px;
      box-sizing: border-box;
      padding: 12px 20px 20px 20px;
      position: absolute;
      left: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      background: #21262d;

      .l-section {
        display: flex;
        align-items: center;
        gap: 16px;
      }

      .r-section {
        display: flex;
        align-items: center;
        gap: 16px;
      }
    }
  }

  .evidence-container {
    width: 367px;
    height: 100%;
    padding: 20px;
    border-top-right-radius: 12px;
    box-sizing: border-box;
    border: 1px solid var(---el-color-white, #fff);
    background: var(---el-border-color-light, #e6eaf0);
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .content {
      width: 100%;
      height: 424px;
      background: #f6f7f9;
      display: flex;
      flex-direction: column;
      border-radius: 12px;
      overflow: hidden;
      justify-content: center;

      .pic {
        width: 100%;
        height: 100%;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .footer {
        margin-top: 16px;
        width: 100%;
        height: 86px;
        padding: 0 16px 26px 16px;
        display: flex;
        flex-wrap: wrap;
        align-items: flex-start;

        .item {
          width: 50%;
          display: flex;
          align-items: center;
          color: #7e8694;
          font-size: 18px;
          font-style: normal;
          font-weight: 300;
          line-height: 26px;
          height: 26px;

          .label {
            text-align: right;
          }

          .value {
            text-align: left;
            color: #1c2026;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
    }

    .empty-content {
      width: 100%;
      height: 424px;
      border-radius: 12px;
      background: #f6f7f9;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;

      .empty-logo {
        width: 175px;
        height: 189px;
        background: url("../../assets/img/empty-logo.png") no-repeat;
        background-size: contain;
      }

      .empty-desc {
        color: #7e8694;
        text-align: center;
        font-size: 18px;
        font-style: normal;
        font-weight: 400;
        line-height: 26px;
        margin-top: 8px;
      }
    }

    .recognize-rst {
      display: flex;
      align-items: center;
      margin-top: 16px;
      color: var(---el-text-color-primary, #1c2026);
      font-family: "PingFang SC";
      font-size: 20px;
      font-style: normal;
      font-weight: 400;
      line-height: 28px;
      gap: 10px;

      .label {
        width: 50px;
      }

      ::v-deep(.el-input-group__append) {
        .el-button {
          padding: 7px 20px;
          border-radius: 0px 12px 12px 0px;
          border-top: 1px solid var(---el-text-color-placeholder, #b0b7c2);
          border-right: 1px solid var(---el-text-color-placeholder, #b0b7c2);
          border-bottom: 1px solid var(---el-text-color-placeholder, #b0b7c2);
          background: linear-gradient(180deg, #fff 0%, #bbc1c3 100%);
        }
      }

      ::v-deep(.el-input__wrapper) {
        border-radius: 12px 0px 0px 12px;
        border: 1px solid var(---el-border-color-base, #cdd2da);
        background: var(---el-color-normal-bg, #f6f7f9);
      }

      ::v-deep .el-input__inner {
        font-family: "PingFang SC";
        font-size: 20px;
        font-style: normal;
        font-weight: 300;
        line-height: 28px;
      }

      ::v-deep(.el-input) {
        --el-input-text-color: #038a78;
        --el-disabled-text-color: #038a78;
        --el-text-color-placeholder: #038a78;
      }
    }

    .footer {
      width: 100%;
      display: flex;
      gap: 16px;

      ::v-deep(.el-button) {
        flex: 1;
      }
    }
  }
}

.div-main-st {
  width: 100%;
  background: #a6b6c8;
  border-radius: 20px 20px 0px 0px;
  height: 62px;
  padding: 0 20px;
  display: flex;
  align-items: center;

  .left-div-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .next-sort-task {
    flex: 1;
    display: flex;
    height: 48px;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    color: var(---el-text-color-primary, #1c2026);
    font-family: "PingFang SC";
    font-size: 18px;
    font-style: normal;
    font-weight: 300;
    line-height: 26px;
  }
}

.ml5 {
  margin-left: 10px;
}

.mr20 {
  margin-right: 20px;
}

.mr5 {
  margin-right: 5px;
}

.recongize-container {
  width: 100%;
  box-sizing: border-box;
  padding: 40x 0 40px 40px;
  gap: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow-x: scroll;
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    display: none;
  }

  &::-webkit-scrollbar-track {
    display: none;
  }

  .item {
    width: 280px;
    height: 320px;
    border-radius: 12px;
    background: #f6f7f9;
    display: flex;
    flex-direction: column;
    position: relative;

    &.active {
      border: 4px solid #059e84;
    }

    .logo {
      flex: 1;

      img {
        width: 100%;
        height: 100%;
        border-top-left-radius: 12px;
        border-top-right-radius: 12px;
      }
    }

    .goods-name {
      width: 100%;
      height: 80px;
      box-sizing: border-box;
      padding: 16px;
      color: #7e8694;
      display: flex;
      align-items: flex-start;
      font-size: 24px;
      font-style: normal;
      font-weight: 400;
      line-height: 32px;

      .value {
        color: #1c2026;
      }
    }
  }
}

.dialog-footer {
  .recoginze-cancle {
    padding: 12px 32px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: 98px;
    border: 1px solid #b0b7c2;
    color: #fff;
    font-size: 24px;
    font-style: normal;
    font-weight: 400;
    line-height: 32px;
    margin-right: 24px;
  }

  .recoginze-confirm {
    padding: 12px 32px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border: 1px solid #48c6c0;
    border-radius: 98px;
    color: #fff;
    font-size: 24px;
    font-style: normal;
    font-weight: 400;
    line-height: 32px;
    background: linear-gradient(
      180deg,
      #9bffff 0%,
      #3bbfb1 16.11%,
      #26a99c 32.28%,
      #099980 53.21%,
      #03a287 74.6%,
      #1bb294 87.27%,
      #5ed9b4 100%
    );
  }
}

::v-deep(.learnModal) {
  border-radius: 16px 16px 16px 16px;

  .el-dialog__header {
    display: none;
  }

  .el-dialog__body {
    padding: 0;
    border-radius: 16px 16px 16px 16px;
  }

  .learn {
    border-radius: 16px 16px 0 0;

    &-header {
      border-radius: 16px 16px 0 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 50px;
      background: #cfd9e4;
      color: var(---el-text-color-primary, #1c2026);
      font-family: "PingFang SC";
      font-size: 18px;
      font-style: normal;
      font-weight: 400;
      line-height: 26px;
      padding: 20px;
    }

    &-result {
      padding: 0 20px 20px 20px;
      display: flex;
      align-items: center;
      gap: 20px;
      width: calc(100% - 40px);
      overflow-x: auto;

      .add-item {
        border: 1px dashed #059e84;
        border-radius: 12px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s;
        background: #fff;

        &:hover {
          border-color: #00b42a;

          .add-text {
            color: #00b42a;
          }

          .add-icon {
            color: #00b42a;
          }
        }

        .add-icon {
          color: #00b42a;
          font-size: 32px;
          font-weight: 200;
          margin-bottom: 4px;
          transition: all 0.3s;
        }

        .add-text {
          color: #00b42a;
          color: var(---el-color-primary, #059e84);
          text-align: center;
          text-overflow: ellipsis;
          font-family: "PingFang SC";
          font-size: 18px;
          font-style: normal;
          font-weight: 400;
          line-height: 26px;
        }
      }

      &-item {
        width: 248px;
        min-width: 248px;
        height: 255px;
        border-radius: 12px;
        border: 1px solid var(---el-border-color-light, #e6eaf0);
        background: var(---el-color-white, #fff);
        position: relative;
        cursor: pointer;

        &-logo {
          width: 100%;
          height: 180px;
          background: red;
          border-radius: 12px 12px 0 0;

          img {
            width: 100%;
            height: 100%;
          }
        }

        &-footer {
          border-top: 1px solid var(---el-border-color-light, #e6eaf0);
          padding: 12px 16px;

          .goodsName {
            overflow: hidden;
            color: var(---el-text-color-primary, #1c2026);
            text-align: center;
            text-overflow: ellipsis;
            font-family: "PingFang SC";
            font-size: 18px;
            font-style: normal;
            font-weight: 400;
            line-height: 26px;
            height: 26px;
          }

          .spec {
            color: var(---el-text-color-secondary, #7e8694);
            text-align: center;
            text-overflow: ellipsis;
            font-family: "PingFang SC";
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px;
          }
        }

        &-code {
          position: absolute;
          right: 0;
          top: 10px;
          display: inline-flex;
          padding: 2px 8px;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          border-radius: 22px 0px 0px 22px;
          background: var(---el-color-warning-light-9, #ffe4ba);
          backdrop-filter: blur(2px);
          color: var(---el-text-color-primary, #1c2026);
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
          letter-spacing: -0.01px;
        }
      }
    }

    &-search {
      margin-top: 20px;
      padding: 0 20px 20px 20px;
    }

    .el-input__wrapper {
      background: var(---el-color-normal-bg, #f6f7f9);
    }

    .el-input__inner {
      font-family: DIN;
      font-size: 24px;
      font-style: normal;
      font-weight: 400;
      line-height: 40px;
      height: 40px;

      &::placeholder {
        font-size: 24px;
      }
    }
  }
}

::v-deep(.tipModal) {
  border-radius: 16px 16px 16px 16px;

  .el-dialog__header {
    display: none;
  }

  .el-dialog__body {
    padding: 0;
    border-radius: 16px 16px 16px 16px;
  }

  .tip-container {
    padding: 40px;
    display: flex;
    flex-direction: column;
    position: relative;

    .close-icon {
      position: absolute;
      top: 12px;
      right: 12px;
    }

    .tips-header {
      color: var(---el-text-color-primary, #1c2026);
      font-family: "PingFang SC";
      font-size: 24px;
      font-style: normal;
      font-weight: 400;
      line-height: 32px;
      text-align: center;
    }

    .tips-desc {
      color: var(---el-text-color-regular, #505762);
      font-family: "PingFang SC";
      font-size: 24px;
      font-style: normal;
      font-weight: 400;
      line-height: 32px;
      margin: 16px 0;
      text-align: center;
    }

    .tips-item-wrp {
      display: flex;
      align-items: center;
      gap: 16px;

      .item {
        flex: 1;
        border-radius: 8px;
        background: var(---el-color-normal-bg, #f6f7f9);
        display: flex;
        padding: 16px;
        flex-direction: column;
        align-items: center;
        gap: 16px;

        .weight {
          display: flex;
          align-items: flex-end;

          &-num {
            color: var(---el-text-color-regular, #505762);
            text-align: center;
            font-family: DIN;
            font-size: 72px;
            font-style: normal;
            font-weight: 700;
            line-height: 70px;

            &.diff {
              color: var(---el-color-danger-light-6, #f76560);
            }
          }

          &-unit {
            color: var(---el-text-color-regular, #505762);
            text-align: center;
            font-family: "PingFang SC";
            font-size: 24px;
            font-style: normal;
            font-weight: 500;
            line-height: 32px;
          }
        }

        .sku {
          color: var(---el-text-color-regular, #505762);
          line-height: 56px;
          min-height: 146px;
          text-overflow: ellipsis;
          display: flex;
          align-items: center;

          div {
            max-height: 56px;
            overflow: hidden;
            text-align: center;
            font-family: "PingFang SC";
            font-size: 48px;
            font-style: normal;
            font-weight: 500;
          }

          &.diff {
            color: var(---el-color-danger-light-6, #f76560);
          }
        }

        .weight-label {
          color: var(---el-text-color-regular, #505762);
          text-align: center;
          font-family: "PingFang SC";
          font-size: 18px;
          font-style: normal;
          font-weight: 400;
          line-height: 26px;
        }
      }
    }

    .tips-footer {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 24px;
      gap: 24px;

      .el-button {
        min-width: 138px;
      }
    }

    .tips-content {
      margin: 24px 0;

      .form-item {
        display: flex;
        align-items: center;
        margin-bottom: 16px;

        .label {
          width: 84px;
          color: var(---el-text-color-regular, #505762);
          font-size: 16px;
          line-height: 24px;
        }

        .required {
          color: #f53f3f;
          margin-right: 8px;
        }

        .input {
          flex: 1;
        }

        :deep(.el-input__wrapper) {
          border-radius: 8px;
        }

        :deep(.el-input__inner) {
          height: 40px;
          font-size: 14px;
        }
      }
    }
  }

  &.lackModal {
    .tip-container {
      .tips-item-wrp {
        gap: 24px;
        align-items: flex-start;
        justify-content: center;

        .item {
          background: none;
          border-radius: 0;
          padding: 24px 0;
          gap: 8px;
          max-width: 240px;

          .box {
            padding: 24px 16px;
            gap: 16px;
            align-self: stretch;
            border-radius: 8px;
            border: 1px solid var(---el-text-color-secondary, #7e8694);
            color: var(---el-text-color-regular, #505762);
            text-align: center;
            font-family: "PingFang SC";
            font-size: 24px;
            font-style: normal;
            font-weight: 400;
            line-height: 32px;
            position: relative;
            cursor: pointer;

            &.active {
              color: var(---el-color-primary-light-4, #038a78);
              border: 1px solid var(---el-color-primary-light-6, #25b195);
            }

            .mark {
              width: 24px;
              height: 24px;
              background: #059e84;
              top: -1px;
              right: -1px;
              position: absolute;
              border-radius: 0 8px 0 8px;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }

          .lack-desc {
            color: var(---el-color-danger, #f53f3f);
            text-align: center;
            font-family: "PingFang SC";
            font-size: 18px;
            font-style: normal;
            font-weight: 300;
            line-height: 26px;
          }
        }
      }
    }
  }
}

:deep(.el-switch) {
  --el-switch-on-color: #059e84;
  --el-switch-off-color: #cfd9e4;

  .el-switch__label {
    color: #fff;
    margin-left: 10px;
    font-size: 14px;

    &.is-active {
      color: #fff;
    }
  }
}

.addProductModal {
  :deep(.el-dialog) {
    border-radius: 12px;
    background: #ffffff;
    box-shadow: 0px 12px 32px 4px rgba(0, 0, 0, 0.04),
      0px 8px 20px rgba(0, 0, 0, 0.08);
  }

  .tip-container {
    padding: 40px;

    .tips-header {
      color: #000000;
      font-size: 24px;
      font-weight: 600;
      text-align: center;
      margin-bottom: 16px;
    }

    .tips-content {
      .label-wrap {
        margin-bottom: 8px;

        .label {
          color: var(---el-text-color-regular, #505762);
          /* 标准/400/H2-18 */
          font-family: "PingFang SC";
          font-size: 18px;
          font-style: normal;
          font-weight: 400;
          line-height: 26px;
        }

        .required {
          color: #ff4d4f;
          margin-left: 4px;
        }
      }

      :deep(.el-input) {
        .el-input__wrapper {
          border: 1px solid #e5e6eb;
          border-radius: 4px;
          box-shadow: none;
          padding: 0 12px;
          height: 50px;
        }

        .el-input__inner {
          height: 50px;
          line-height: 50px;
          font-size: 24px;
          color: #1d2129;

          &::placeholder {
            color: var(---el-text-color-secondary, #7e8694);
            font-family: "PingFang SC";
            font-size: 24px;
            font-style: normal;
            font-weight: 400;
            line-height: 50px;
          }
        }
      }

      margin: 0;
    }

    .tips-footer {
      margin-top: 24px;
      display: flex;
      justify-content: center;
      gap: 16px;

      .cancel-btn {
        width: 184px;
        height: 48px;
        background: #f2f3f5;
        border-radius: 4px;
        border: none;
        font-size: 16px;
        color: #4e5969;

        &:hover {
          background: #e5e6eb;
        }
      }

      .confirm-btn {
        width: 184px;
        height: 48px;
        background: #00b42a;
        border-radius: 4px;
        border: none;
        font-size: 16px;
        color: #ffffff;

        &:hover {
          background: #009a29;
        }
      }
    }
  }
}
</style>
