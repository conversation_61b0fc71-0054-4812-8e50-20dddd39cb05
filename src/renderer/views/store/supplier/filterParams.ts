import { h, ref } from 'vue'
import { STATUS, defaultDeliveryTime } from '../const/constant'
import { userInfoT } from "@renderer/store/modules/template";
import dayjs from "dayjs";
const useUserStore = userInfoT()

export const filterForm = ref({
  warehouseInStatus: STATUS.WAIT_WAREHOUSE_IN,
  warehouseId: '',
  inDate: null,
  warehouseInTime: null,
  billDateStart: null,
  billDateEnd: null,
  createOrderDateRange: null,
  shopId: useUserStore?.getShopList?.[0]?.id || undefined
}); // 筛选表单
export const searchInput = ref('');
export const currentGoodsType = ref(null); // 当前选择商品类型
export const isPrintReceipt = ref(false); // 是否不打印小票

export const initFilterForm = () => {
  filterForm.value = {
    warehouseInStatus: STATUS.WAIT_WAREHOUSE_IN,
    warehouseId: '',
    inDate: null,
    warehouseInTime: null,
    billDateStart: null,
    billDateEnd: null,
    createOrderDateRange: null,
    shopId: useUserStore?.getShopList?.[0]?.id || undefined
  }
  searchInput.value = '';
  currentGoodsType.value = null;
  isPrintReceipt.value = false
}

/**
 * 初始化仓库条件
 */
export const handleInitWarehouseIdCondition = (warehouses) => {
  if (filterForm.value.warehouseId === "") {
    const defaultWarehouse = warehouses.find(({ defaultFlag }) => defaultFlag === true);
    filterForm.value.warehouseId = defaultWarehouse?.warehouseId || warehouses[0]?.warehouseId || undefined;
  }
}

/**
 * 处理搜索日期时间
 */
export const handleSelectedSearchDateTime = () => {
  if (!filterForm.value.createOrderDateRange) {
    filterForm.value.billDateStart = ''
    filterForm.value.billDateEnd = ''
  } else if (filterForm.value.createOrderDateRange.length === 2) {
    filterForm.value.billDateStart = `${filterForm.value.createOrderDateRange[0]} 00:00:00`
    filterForm.value.billDateEnd = `${filterForm.value.createOrderDateRange[1]} ${defaultDeliveryTime}`
  } else {
    filterForm.value.billDateStart = ''
    filterForm.value.billDateEnd = ''
  }

  if (filterForm.value.inDate) {
    filterForm.value.warehouseInTime = ''
  } else {
    filterForm.value.warehouseInTime = ''
  }
}

/**
 * 初始化搜索日期时间
 */
export const handleInitSearchDateTime = () => {
  const currentTime = new Date();
  if (!filterForm.value.inDate) {
    filterForm.value.inDate = dayjs(currentTime).format("YYYY-MM-DD");
  }
  if (filterForm.value.createOrderDateRange?.length === 0) {
    filterForm.value.createOrderDateRange = [`${dayjs(currentTime).format("YYYY-MM-DD")}`, `${dayjs(currentTime).format("YYYY-MM-DD")}`];
  }
}