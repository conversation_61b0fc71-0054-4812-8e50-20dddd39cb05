<template>
    <div class="card" @click="handleItemTap(data)">
        <div class="status" :class="[`status-${status}`]">
            {{ $t('components.supplierItem.001507-0') }}
            <div>
                <span :class="[data.hadInNum < data.inTotalNum ? '' : 'status-total']">{{ data.hadInNum || 0
                }}</span>
                <span class="status-total">/{{ data.inTotalNum || 0 }}</span>
            </div>
        </div>
        <div class="container">{{ data.businessUserName || '-' }}</div>
        <div class="footer">
            <div class="btns-wraper">
                <el-button @click.stop="handleItemTap(data)">{{ $t('components.supplierItem.001507-1') }}</el-button>
                <el-button type="primary" @click.stop="handleStore(data)" class="ml">{{ $t('components.supplierItem.001507-2') }}</el-button>
            </div>
        </div>
    </div>
</template>

<script setup>
import { computed, toRefs } from "vue";

const props = defineProps({
    data: {
        type: Object,
        default: () => { },
    },
});

const emits = defineEmits(["select", "store"]);

const { data } = toRefs(props);

const status = computed(() => {
    return (
        data.value.hadInNum == 0 ? "UNSORTED" : data.value.hadInNum == data.value.inTotalNum ? "SORTED" : "SORTING"
    );
});

function handleItemTap(data) {
    console.log(data);
    emits("select", data);
}

function handleStore(data) {
    console.log(data);
    emits("store", data);
}

</script>

<style lang="scss" scoped>
.card {
    cursor: pointer;
    width: calc((100% - 72px) / 4);
    display: flex;
    flex-direction: column;
    border: 1px solid var(---el-border-color-light, #E6EAF0);
    background: #fff;
    border-radius: 12px;

    .status {
        display: flex;
        height: 32px;
        padding: 0px 16px;
        justify-content: space-between;
        align-items: center;
        align-self: stretch;
        color: var(---el-color-white, #FFF);
        font-family: "PingFang SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px;
        border-top-left-radius: 12px;
        border-top-right-radius: 12px;
    }

    .status-total {
        color: var(---el-color-white, rgba(255, 255, 255, 0.6));
    }


    .status-UNSORTED {
        background: #DB4646;
    }

    .status-SORTED {
        background: var(---el-color-primary, #059E84);
    }

    .status-SORTING {
        background: #2C85D7;
    }

    .container {
        display: flex;
        padding: 40px 12px;
        flex-direction: column;
        align-items: flex-start;
        align-self: center;
        color: var(---el-text-color-primary, #1C2026);
        font-family: "PingFang SC";
        font-size: 20px;
        font-style: normal;
        font-weight: 600;
        line-height: 28px;
    }

    .footer {
        padding: 0 12px;
        margin-bottom: 12px;
        display: flex;
        flex-direction: column;
        gap: 12px;

        .btns-wraper {
            display: flex;
            align-items: center;
            flex: 1;

            .el-button:focus,
            .el-button:hover {
                color: #059e84;
                border-color: #059E84;
                background-color: #e8fff7;
            }

            .el-button {
                // min-width: 104px;
                min-height: 44px;
                text-align: center;
                padding: 10px 20px;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                gap: 4px;
                color: #059E84;
                border-radius: 8px;
                border: 1.5px solid var(---el-color-primary, #059E84);
                font-family: "PingFang SC";
                font-size: 16px;
                font-style: normal;
                font-weight: 400;
                line-height: 24px;
                margin: 0px;
                display: flex;
                flex: 1;
            }

            .ml {
                margin-left: 4px;
            }

            .el-button--primary {
                background: #059e84;
                color: #fff;
            }

            .el-button--primary:hover {
                background: rgb(55, 177, 157);
                color: #fff;
                border-color: #059E84;
            }
        }
    }
}
</style>
