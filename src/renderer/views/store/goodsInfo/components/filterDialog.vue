<template>
  <el-drawer
    v-model="visible"
    size="400px"
    direction="rtl"
    :with-header="false"
    style="position: absolute"
    modal-class="model-content"
    class="filter-form-drawer"
    >
    <div>
      <div class="header">
        <div>{{ $t('components.filterDialog.032414-0') }}</div>
        <el-icon size="24" @click="cancel" color="#1C2026">
          <Close />
        </el-icon>
      </div>
      <el-form label-position="top" class="filter-form-content">
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item :label="$t('components.filterDialog.970299-0')">
              <el-radio-group
                class="filter-radio-content"
                v-model="filterForm.warehouseInStatus"
                size="large"
              >
                <el-radio-button
                  v-for="item in status"
                  :key="item.value"
                  :label="item.value"
                >
                  {{ item.label }}
                </el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('components.filterDialog.186700-0')">
              <el-date-picker
                v-model="filterForm.inDate"
                type="date"
                value-format="YYYY-MM-DD"
                clearable
                :editable="false"
                :placeholder="$t('components.filterDialog.032414-3')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('components.filterDialog.186700-1')">
              <el-date-picker
                v-model="filterForm.createOrderDateRange"
                type="daterange"
                value-format="YYYY-MM-DD"
                clearable
                :editable="false"
                :placeholder="$t('components.filterDialog.186700-2')"
                :start-placeholder="$t('components.filterDialog.186700-3')"
                :end-placeholder="$t('components.filterDialog.186700-4')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('components.filterDialog.186700-5')">
              <el-select
                v-model="filterForm.warehouseId"
                :filterable="false"
                remote
                reserve-keyword
                :placeholder="$t('components.filterDialog.032414-5')"
                :remote-show-suffix="true"
              >
                <el-option
                  v-for="item in filterCondition.warehouses"
                  :key="item.warehouseId"
                  :label="item.warehouseName"
                  :value="item.warehouseId"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <template #footer>
      <div class="footer-content">
        <el-button type="primary" @click="confirm">{{ $t('components.filterDialog.032414-6') }}</el-button>
        <el-button @click="reset">{{ $t('components.filterDialog.032414-7') }}</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { log } from "console";
import { ref, toRefs, computed } from "vue";
import { defaultDeliveryTime, status } from "../../const/constant";
import dayjs from "dayjs";
import { userInfoT } from "@renderer/store/modules/template";
import { storeToRefs } from "pinia";
const props = defineProps({
  searchData: {
    type: Object,
    default: () => {},
  },
  filterCondition: {
    type: Object,
    default: () => {},
  },
});

const useUserStore = userInfoT();
const { shopList } = storeToRefs(useUserStore);

const { filterCondition, searchData } = toRefs(props);
const filterForm = ref(JSON.parse(JSON.stringify(searchData.value)));
const emits = defineEmits(["confirm", "reset"]);
const visible = ref(false);
const currentTime = ref(null); // 当前时间
const currentDeadlineTime = computed(
  () => filterCondition.value?.defaultCutOffTime || defaultDeliveryTime
); // 截止时间

function show() {
  filterForm.value = JSON.parse(JSON.stringify(searchData.value));
  currentTime.value = new Date();
  visible.value = true;
}

function reset() {
  emits("reset");
  visible.value = false;
}

function confirm() {
  emits("confirm", filterForm.value);
  visible.value = false;
}

defineExpose({
  show,
});
</script>

<style lang="scss" scoped>
.footer-content {
  width: 100%;
  display: flex;
  align-items: center;
  padding: 12px 20px;
  gap: 20px;

  .el-button:focus,
  .el-button:hover {
    color: #059e84;
    border-color: #79d8bf;
    background-color: #e8fff7;
  }

  .el-button {
    min-width: 104px;
    text-align: center;
    padding: 12px 20px;
    border-color: #059e84;
    color: #038a78;
    border-radius: 8px;
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    height: 48px;
  }

  .el-button--primary {
    background: #059e84;
    color: #fff;
    border-color: #038a78;
  }
  .el-button--primary:hover {
    background: rgb(55, 177, 157);
    color: #fff;
    border-color: rgb(55, 177, 157);
  }
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: var(---el-text-color-primary, #1c2026);
  text-align: center;
  font-family: "PingFang SC";
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: 26px;
  padding: 12px 20px;
  background: #c1cdda;
}

.filter-form-content {
  padding: 20px;

  ::v-deep .el-form-item__label {
    color: var(---el-text-color-regular, #505762);
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 300;
    line-height: 24px;
  }

  ::v-deep .el-form-item {
    margin-bottom: 24px;
    .el-form-item__content {
      // width: 70%;
    }
    .el-date-editor.el-input,
    .el-date-editor.el-input__wrapper,
    .el-select {
      width: 100% !important;
      border-radius: 8px !important;
    }
    .el-input__wrapper {
      border-radius: 8px !important;
      border: 1px solid var(---el-border-color-base, #CDD2DA);
      background: var(---el-color-white, #FFF);
    }
    .el-input__wrapper.is-focus,
    .el-input.is-focus .el-input__wrapper {
      box-shadow: 0 0 0 1px #009f64 !important;
    }

    .el-input__inner {
      height: 40px;
    }
  }
}
::v-deep .filter-radio-content {
  gap: 16px;

  .el-radio-button__original-radio:checked + .el-radio-button__inner {
    color: #009f64;
    border: 1px solid #009f64;
    background: rgba(0, 159, 100, 0.1);
  }
  .el-radio-button__inner:hover {
    color: #009f64;
    border: 1px solid #009f64;
    background: rgba(0, 159, 100, 0.1);
  }
  .el-radio-button__inner {
    border-radius: 8px;
    border: 1px solid #7e8694;
    background: #fff;
    box-shadow: none;
    font-size: 14px;
  }
}

:global(.el-overlay) {
  background-color: transparent !important;
}
</style>
<style lang="scss">
.el-select-dropdown__item.selected {
  color: #009f64 !important;
  &::after {
    background-color: #009f64 !important;
  }
}

.el-month-table td .cell:hover,
.el-month-table td.current:not(.disabled) .cell,
.el-month-table td.today .cell,
.el-month-table td.current:not(.disabled) .cell,
.el-month-table td.current:not(.disabled) .cell,
.el-year-table td.current:not(.disabled) .cell,
.el-year-table td .cell:hover,
.el-date-picker__header-label:hover,
.el-picker-panel__icon-btn:hover,
.el-date-table td.available:hover,
.el-date-table td.today .el-date-table-cell__text,
.el-date-table td.current:not(.disabled) .el-date-table-cell__text,
.el-year-table td.today .cell {
  color: #009f64 !important;
}

.el-date-table td.current:not(.disabled) .el-date-table-cell__text {
  background-color: #009f64;
  color: #fff !important;
}
</style>
