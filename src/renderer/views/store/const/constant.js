import { i18n } from "@renderer/i18n";

// 入库状态
export const STATUS = {
  ALL: "ALL",
  WAIT_WAREHOUSE_IN: "WAIT_WAREHOUSE_IN",
  FINISH: "FINISH",
  PARTIAL_WAREHOUSE_IN: "PARTIAL_WAREHOUSE_IN",
};

// 入库状态
export const status = [
  {
    value: STATUS.ALL,
    label: i18n.global.t("const.constant.538705-0"),
  },
  {
    value: STATUS.WAIT_WAREHOUSE_IN,
    label: i18n.global.t("const.constant.538705-1"),
  },
  {
    value: STATUS.FINISH,
    label: i18n.global.t("const.constant.538705-2"),
  },
  {
    value: STATUS.PARTIAL_WAREHOUSE_IN,
    label: "入库中",
  },
];

export const defaultDeliveryTime = "23:59:59"; // 默认截止时间
