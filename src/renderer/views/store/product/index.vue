<template>
  <div class="page">
    <div class="filter-dialog">
      <filter-dialog :searchData="filterForm" ref="filterDialogRef" :filterCondition="filterCondition"
        @confirm="handleConfirm" @reset="handleReset"></filter-dialog>
    </div>
    <div class="search-bar">
      <el-icon @click="handleBack" size="28" color="#1C2026">
        <Back />
      </el-icon>
      <div class="search-bar-right">
        <el-form @submit.native.prevent>
          <el-input v-model="searchInput" ref="inputRef" class="search-input"
            :placeholder="$t('productSorting.index.874905-0')" @focus="handleFocus" @blur="handleBlur"
            @keyup.enter="handleEnter">
            <template #prefix>
              <el-icon size="24" color="#7E8694">
                <Search />
              </el-icon>
            </template>
          </el-input>
        </el-form>
        <div class="divider"></div>
        <el-button @click="openFilterDialog">
          <SvgIcon name="filter"></SvgIcon>{{ $t('product.index.797724-0') }}
        </el-button>
      </div>
    </div>
    <div class="container">
      <div class="sidebar">
        <el-tree ref="categoryTreeRef" v-loading="treeLoading" :data="goodsCategoryList" class="tree" node-key="id"
          :empty-text="$t('productSorting.index.874905-4')" check-on-click-node highlight-current :props="defaultProps"
          @node-click="handleNodeChange">
          <template #default="{ node, data }">
            <div class="custom-tree-node">
              <span class="tree-goods-label">{{ data.name }}</span>
              <span v-if="data.id" class="tree-goods-total">
                ({{ data.hadInCount || 0 }}/{{ data.totalCount || 0 }})
              </span>
            </div>
          </template>
        </el-tree>
      </div>
      <div class="content" v-loading="loading">
        <div class="desc">
          <div class="desc-left">
            <div class="label">{{ $t('product.index.797724-1') }}</div>
            <div class="value">{{ filterForm.inDate }}</div>
          </div>
          <div class="desc-right">
            <el-checkbox v-model="isPrintReceipt" :label="true" @click.prevent="handleClickRadio(isPrintReceipt)">
              {{ $t("productSorting.index.874905-6") }}
            </el-checkbox>
          </div>
        </div>
        <div class="scroll-container">
          <div class="goods-list" v-if="goodsList?.length" v-infinite-scroll="handleScroll" :infinite-scroll-delay="500"
            :infinite-scroll-immediate="true">
            <Goods v-for="item in goodsList" :key="item.spuId" :data="item" @select="handleSelect"
              @handleSorting="handleToVideoSorting"></Goods>
          </div>
        </div>
      </div>
    </div>

    <PrinterDialogSelect ref="PrinterDialogSelectRef" :printerList="printerList" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from "vue";
import { userInfoT } from "@renderer/store/modules/template";
import { vueListen } from "../../../utils/ipcRenderer";
import { IpcChannel } from "../../../../ipc";
import { useRouter, useRoute } from "vue-router";
import { invoke } from "../../../utils/ipcRenderer";
import Goods from "./components/goodsItem.vue";
import filterDialog from "./components/filterDialog.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { defaultDeliveryTime, status } from "../const/constant";
import { i18n } from "@renderer/i18n";
import {
  queryGoodsList,
  posInSortQueryGoodsList,
  categoryList,
} from "@renderer/api/inStore";
import dayjs from "dayjs";
import {
  filterForm,
  searchInput,
  currentGoodsType,
  isPrintReceipt,
  handleInitSearchDateTime,
  handleSelectedSearchDateTime,
  handleInitWarehouseIdCondition,
} from "./filterParams";
import PrinterDialogSelect from "../../../components/PrinterDialogSelect.vue";
import { closeKeyBoard } from "../../../utils/util";
import { sortFinsh } from "@renderer/api/login";
const useUserStore = userInfoT();
const router = useRouter();
const route = useRoute();
const filterDialogRef = ref(null); // 筛选弹窗
const goodsList = ref([]); // 商品列表
const keyboardRef = ref(null);

const handleFocus = () => { };

const handleBlur = () => { };

const inputRef = ref(null);

const handleEnter = () => {
  inputRef.value.blur();
  closeKeyBoard();
  reLoad();
};

const searchVuale = ref("");
vueListen(IpcChannel.SendDataTest, (event, data) => { });

function handleClickRadio(value) {
  // 点击radio组件
  isPrintReceipt.value = !value;
}

const PrinterDialogSelectRef = ref(null); // 打印
function handleOneKeyPrint() {
  // 一键打印
  if (isPrintReceipt.value) {
    ElMessage.warning(i18n.global.t("productSorting.index.874905-7"));
    return;
  }
  //判断是否  配置过打印机
  if (!useUserStore.getPrinterName && !isPrintReceipt.value) {
    PrinterDialogSelectRef.value.show();
    return;
  }
  ElMessageBox.confirm(
    i18n.global.t("productSorting.index.874905-8"),
    i18n.global.t("productSorting.index.874905-2"),
    {
      confirmButtonText: i18n.global.t("productSorting.index.874905-9"),
      cancelButtonText: i18n.global.t("productSorting.index.874905-10"),
      type: "warning",
      customClass: "message-box-confirm",
    }
  ).then(async () => {
    const { shopId, ...restParams } = filterForm.value || {};
    try {
      const res = await posSortBatchPrintSort({
        shopId: shopId,
        searchInput: searchInput.value || undefined, // 助记码
        categoryCode: currentGoodsType.value || undefined, // 分类编码
        ...restParams,
        billDateStart: `${filterForm.value.billDateStart} 00:00:00`,
        sort: false,
        print: !isPrintReceipt.value,
      });
      if (res?.data?.data?.length && !isPrintReceipt.value) {
        PrinterDialogSelectRef.value.sendPrintStart(res?.data?.data);
      }
    } catch (error) {
      // ElMessage.error('打印失败,请重试');
    }
  });
}

// 一键入库
async function handleOneKeyStore() {
  if (!useUserStore.getPrinterName && !isPrintReceipt.value) {
    PrinterDialogSelectRef.value.show();
    return;
  }
  const { shopId, ...restParams } = filterForm.value || {};
  await sortFinsh({
    shopId: shopId,
    searchInput: searchInput.value || undefined, // 助记码
    categoryCode: currentGoodsType.value || undefined, // 分类编码
    condition: {
      ...restParams,
      deliveryDate: `${filterForm.value.billDateStart} 00:00:00`,
    },
  }).then((res) => {
    if (res.data.data == null || res.data.data == 0) {
      ElMessage.warning(i18n.global.t("productSorting.index.874905-11"));
      return;
    }
    let tipStr =
      i18n.global.t("productSorting.index.874905-12") +
      res.data.data +
      i18n.global.t("productSorting.index.874905-13");
    ElMessageBox.confirm(
      tipStr,
      i18n.global.t("productSorting.index.874905-1"),
      {
        confirmButtonText: i18n.global.t("productSorting.index.874905-9"),
        cancelButtonText: i18n.global.t("productSorting.index.874905-10"),
        type: "warning",
        customClass: "message-box-confirm",
      }
    ).then(async () => {
      try {
        const res = await posSortBatchPrintSort({
          shopId: shopId,
          searchInput: searchInput.value || undefined, // 助记码
          categoryCode: currentGoodsType.value || undefined, // 分类编码
          condition: {
            ...restParams,
            deliveryDate: `${filterForm.value.billDateStart} 00:00:00`,
          },
          sort: true,
          print: !isPrintReceipt.value,
        });
        ElMessage.success(i18n.global.t("productSorting.index.874905-14"));
        getCategoryList();
        reLoad();
        if (res?.data?.data?.length && !isPrintReceipt.value) {
          PrinterDialogSelectRef.value.sendPrintStart(res?.data?.data);
        }
      } catch (error) {
        //  ElMessage.error('分拣失败,请重试');
      }
    });
  });
}

function openFilterDialog() {
  // 打开筛选弹窗
  filterDialogRef.value.show();
}

function handleBack() {
  // 返回
  router.go(-1);
}

const filterCondition = ref({}); // 筛选条件
// 获取筛选条件
async function getQueryCondition() {
  const res = await queryGoodsList(filterForm.value.shopId);
  filterCondition.value = { ...res.data.data };
  handleInitWarehouseIdCondition(res.data.data?.warehouses || []);
  const currentDeadlineTime =
    res?.data?.data?.defaultCutOffTime || defaultDeliveryTime;
  handleInitSearchDateTime();
}

const pagingData = ref({
  total: 0,
  size: 16,
  current: 1,
});

function imgAnalysis(imgUrl) {
  let url = null;
  try {
    url = JSON.parse(imgUrl)?.[0];
  } catch (error) {
    url = imgUrl;
  }
  return url;
}

const loading = ref(false); // 列表loading
const isOpenPaging = ref(false); // 是否开启翻页
// 商品列表
async function getGoodsList(isScroll = false) {
  // 如果正在加载，不重复请求
  if (loading.value) return;

  loading.value = true;
  const { shopId, ...restParams } = filterForm.value || {};

  const params = {
    shopId: shopId,
    spuNameOrCode: searchInput.value || undefined, // 助记码
    categoryCode: currentGoodsType.value || undefined, // 分类编码
    supplierNameOrSpuCode: supplierNameOrSpuCode.value || undefined,
    supplierNameOrBusinessNo: supplierNameOrBusinessNo.value || undefined,
    businessUserId: businessUserId.value || undefined,
    businessNo: businessNo.value || undefined,
    size: pagingData.value.size,
    current: pagingData.value.current,
    ...restParams,
  };
  try {
    const res = await posInSortQueryGoodsList({ ...params });
    // 使用requestAnimationFrame优化DOM更新
    window.requestAnimationFrame(() => {
      const newList =
        res?.data?.data?.records?.map((t) => ({
          ...t,
          imgUrl: imgAnalysis(t.imgUrl),
        })) || [];
      goodsList.value = [...goodsList.value, ...newList];
      pagingData.value.total = Number(res?.data?.data?.total) || 0;
      loading.value = false;
      isOpenPaging.value = true;
    });
  } catch (error) {
    if (isScroll) {
      pagingData.value.current = pagingData.value.current - 1;
    }
    loading.value = false;
  }
}

const goodsCategoryList = ref([]); // 商品分类
const categoryTreeRef = ref(null);
const treeLoading = ref(false); // 商品类型树loading
// 获取商品分类
async function getCategoryList() {
  treeLoading.value = true;
  handleSelectedSearchDateTime();
  const { shopId, ...restParams } = filterForm.value || {};
  try {
    const res = await categoryList({
      shopId: shopId,
      spuNameOrCode: searchInput.value || undefined, // 助记码
      categoryCode: currentGoodsType.value || undefined, // 分类编码
      supplierNameOrSpuCode: supplierNameOrSpuCode.value || undefined,
      supplierNameOrBusinessNo: supplierNameOrBusinessNo.value || undefined,
      businessUserId: businessUserId.value || undefined,
      businessNo: businessNo.value || undefined,
      ...restParams,
    });

    const sortedTotalCount = (res?.data || []).reduce((prev, cur) => (cur?.hadInCount ?? 0) + prev, 0)
    const totalCount = (res?.data || []).reduce((prev, cur) => (cur?.totalCount ?? 0) + prev, 0)

    const rst = {
      ...(res?.data || [])[0],
      children: [],
      id: undefined,
      name: totalCount > 0 ? `全部商品(${sortedTotalCount}/${totalCount})` : `全部商品`,
    };

    goodsCategoryList.value = [rst, ...(res?.data || [])];
    treeLoading.value = false;
    nextTick(() => {
      categoryTreeRef.value.setCurrentKey(currentGoodsType.value);
    });
  } catch (error) {
    treeLoading.value = false;
  }
}

const reLoad = () => {
  // 重新加载
  goodsList.value = [];
  pagingData.value.current = 1;
  nextTick(() => {
    getGoodsList();
  });
};

// 添加节流函数
function throttle<T extends (...args: any[]) => any>(fn: T, delay: number) {
  let timer: number | null = null;
  return function (this: any, ...args: Parameters<T>) {
    if (timer) return;
    timer = window.setTimeout(() => {
      fn.apply(this, args);
      timer = null;
    }, delay);
  };
}

const handleScrollImpl = () => {
  console.log('handleScrollImpl')
  console.log(isOpenPaging.value, loading.value)
  console.log(goodsList.value.length, pagingData.value.total)
  if (!isOpenPaging.value || loading.value) return;
  if (goodsList.value.length >= pagingData.value.total) {
    // ElMessage.warning('没有更多了')
    return;
  }
  pagingData.value.current = pagingData.value.current + 1;
  getGoodsList(true);
};

// 使用节流函数包装handleScroll，200ms内只执行一次
const handleScroll = throttle(handleScrollImpl, 200);

function handleNodeChange(data) {
  if (data?.id === currentGoodsType.value) return;
  currentGoodsType.value = data?.id;
  reLoad();
}

// 筛选搜索
function handleConfirm(form) {
  filterForm.value = { ...form };
  currentGoodsType.value = undefined;
  handleSelectedSearchDateTime();
  getCategoryList();
  reLoad();
  router.push({
    query: {
      ...router.currentRoute.value.query,
      filterForm: JSON.stringify(filterForm.value),
    }
  })
}

// 重置
function handleReset() {
  initFilterForm();
  handleInitSearchDateTime();
  currentGoodsType.value = undefined;
  getCategoryList();
  reLoad();
}

// 选择商品进入称重页面
function handleSelect(data) {
  console.log(data.value, "data");
  router.push({
    path: "/home/<USER>",
    query: {
      businessUserId: businessUserId.value,
      businessNo: businessNo.value,
      data: JSON.stringify(data.value),
      filterForm: JSON.stringify(filterForm.value),
      isPrint: isPrintReceipt.value,
      shopId: filterForm.value.shopId,
    },
  });
}

const handleToVideoSorting = (data) => {
  console.log(data.value, "data");
  router.push({
    path: "/home/<USER>",
    query: {
      businessUserId: businessUserId.value,
      businessNo: businessNo.value,
      data: JSON.stringify(data.value),
      filterForm: JSON.stringify(filterForm.value),
      isPrint: isPrintReceipt.value,
      shopId: filterForm.value.shopId,
    },
  });
};

const printerList = ref([]); // 打印列表
const supplierNameOrBusinessNo = ref('');
const supplierNameOrSpuCode = ref('');
const businessUserId = ref('');
const businessNo = ref('');

onMounted(async () => {
  console.log('onMounted');

  //获取打印机列表
  printerList.value = await invoke(IpcChannel.GetPrinters);

  console.log(route.query, "route.query");
  const { data } = route.query;
  await getQueryCondition();

  if (data) {
    filterForm.value = JSON.parse(route.query.filterForm);
    isPrintReceipt.value = route.query.isPrint === 'true';
    supplierNameOrBusinessNo.value = route.query.supplierNameOrBusinessNo;
    supplierNameOrSpuCode.value = route.query.spuNameOrCode;
    const dataObj = JSON.parse(data ?? '{}');
    console.log(dataObj, "dataObj")
    businessUserId.value = dataObj.businessUserId;
    businessNo.value = dataObj.businessNo;
  }
  console.log(filterForm.value, "filterForm.value");

  await getCategoryList();
  reLoad();
});
</script>

<script lang="ts">
import { defineComponent } from "vue";
import { initFilterForm } from "./filterParams";
export default defineComponent({
  beforeRouteEnter(to, from, next) {
    //to当前路由，from从哪个路由来
    //这里的vm指的就是vue实例，可以用来当做this使用
    next(() => {
      if (from.path == "/home/<USER>") {
        initFilterForm();
      }
    });
  },
});
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: #a6b6c8;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  position: relative;
}

.search-bar {
  display: flex;
  align-items: center;
  padding: 20px 20px 16px 20px;
  z-index: 18;
  gap: 24px;
  background: #a6b6c8;

  ::v-deep .el-button {
    display: inline-flex;
    box-sizing: border-box;
    height: auto;
    line-height: 22px;
    padding: 12px 24px;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    border-radius: 12px;
    border: none;
    background: #fff;
    color: var(--el-text-color-primary, #1c2026);
    font-family: PingFang SC;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;

    &+.el-button {
      margin-left: 0;
    }

    &>span {
      gap: 4px;
    }
  }

  .search-bar-right {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 24px;

    .search-input {
      width: 554px;
      height: 48px;
      font-size: 18px;

      ::v-deep .el-input__wrapper {
        border-radius: 12px;
        border: 1px solid var(--el-border-color-base, #cdd2da);
        background: var(--el-color-white, #f6f7f9);
        padding: 1px 20px;

        &.is-focus {
          // border: 1px solid var(--el-border-color-base, #cdd2da);
          box-shadow: none !important;
        }
      }
    }

    .divider {
      width: 1px;
      height: 32px;
      background: #cdd2da;
    }
  }
}

.container {
  display: flex;
  flex: 1;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  border: 1px solid #fff;
  background: #c1cdda;
  overflow: hidden;

  .sidebar {
    width: 200px;
    padding: 20px 0;
    box-sizing: border-box;
    background: #fff;
    background: var(---el-border-color-light, #e6eaf0);
    overflow-y: auto;

    .label {
      color: var(--el-text-color-secondary, #7e8694);
      font-family: PingFang SC;
      font-size: 28px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      letter-spacing: -0.01px;
    }

    ::v-deep .el-tree {
      background: none !important;
    }

    .tree {
      margin-top: 8px;

      ::v-deep .custom-tree-node[data-v-7d14a2e4] {
        font-family: "PingFang SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px;
      }

      ::v-deep .is-current>.el-tree-node__content {
        background: #c1cdda;
        color: var(---el-text-color-primary, #038a78);
      }

      ::v-deep .el-tree-node__content {
        height: 40px;
        color: var(---el-text-color-primary, #1c2026);
        font-family: "PingFang SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px;

        .el-tree-node__label {
          // 文本超长显示省略号
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        &:hover {
          background: #f0f2f5;
        }
      }
    }
  }

  .content {
    flex: 1;
    padding: 20px;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .desc {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .desc-left {
        display: flex;
        align-items: center;
        gap: 8px;
        color: var(---el-text-color-regular, #1c2026);
        font-family: "PingFang SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px;
      }

      .desc-right {
        ::v-deep .el-radio {
          height: 24px;
          color: var(---el-text-color-primary, #1c2026);
          font-family: "PingFang SC";
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 24px;
        }

        ::v-deep .el-radio__inner:hover {
          border-color: #009f64;
        }

        ::v-deep .el-checkbox__label {
          color: #1c2026;
          font-family: "PingFang SC";
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 24px;
        }

        ::v-deep .el-checkbox__input.is-checked .el-checkbox__inner::after {
          top: 4px;
          left: 7px;
        }

        ::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
          color: #009f64 !important;
          background-color: #009f64 !important;
          border-color: #009f64 !important;
        }

        ::v-deep(.el-checkbox) {
          --el-checkbox-input-border-color-hover: #009f64 !important;
          --el-checkbox-border-radius: #009f64 !important;
          --el-checkbox-input-width: 20px !important;
          --el-checkbox-input-height: 20px !important;
        }
      }
    }

    .scroll-container {
      flex: 1;
      overflow-y: auto;
      height: calc(100vh - 250px);
      will-change: transform;
      transform: translateZ(0);
    }

    .goods-list {
      margin-top: 4px;
      flex-wrap: wrap;
      display: flex;
      gap: 16px;
    }
  }
}

::v-deep .filter-dialog {
  z-index: 20;
  position: relative;

  .model-content {
    height: calc(100vh - 57px);
    position: absolute !important;
  }

  .el-drawer {
    border-radius: 20px 0px 0px 20px;
    background: var(---el-border-color-lighter, #f0f2f5);
  }

  .el-drawer__body {
    padding: 0;
    box-sizing: border-box;
    overflow: hidden;
  }

  .el-drawer__footer {
    padding: 0;
  }
}

.select-all-goods {
  cursor: pointer;

  &:hover {
    background: #f0f2f5;
  }
}
</style>
<style lang="scss">
.message-box-confirm {

  .el-button:focus,
  .el-button:hover {
    color: #059e84;
    border-color: #79d8bf;
    background-color: #e8fff7;
  }

  .el-button--primary {
    background: #009f64 !important;
    color: #fff !important;
    border-color: #009f64 !important;
  }

  .el-button--primary:hover {
    background: rgb(55, 177, 157);
    color: #fff;
    border-color: rgb(55, 177, 157);
  }

  .el-message-box__headerbtn:hover .el-message-box__close {
    color: #009f64;
  }
}
</style>
