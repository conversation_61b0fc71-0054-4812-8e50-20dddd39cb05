<template>
    <div class="card" @click="handleSelectGoods">
        <div class="status" :class="[`status-${status}`]">
            {{ $t('components.goodsItem.556049-0') }}
            <div>
                <span :class="[data.hadInNum < data.totalNum ? '' : 'status-total']">{{ data.hadInNum || 0
                    }}</span>
                <span class="status-total">/{{ data.totalNum || 0 }}</span>
            </div>
        </div>
        <div class="container">
            <div class="adapt">
                <img :src="data.imgUrl" alt="" class="pic" loading="lazy" />
            </div>
            <div class="info">
                <div class="product-name">{{ data.spuName || "-" }}</div>
                <div class="stock-info">{{ $t('components.goodsItem.556049-1') }} {{ data.showAvailableStock }}</div>
            </div>
        </div>
        <div class="footer">
            <div class="btns-wraper">
                <el-button @click.stop="handleSelectGoods">{{ $t('components.goodsItem.556049-2') }}</el-button>
                <el-button type="primary" @click.stop="handleSorting" class="ml">{{ $t('components.goodsItem.556049-3') }}</el-button>
            </div>
        </div>
    </div>
</template>

<script setup>
import { computed, toRefs } from "vue";

const props = defineProps({
    data: {
        type: Object,
        default: () => { },
    },
});

const emits = defineEmits(["select", "handleSorting"]);
const { data } = toRefs(props);

const status = computed(() => {
    return (
        data.value.hadInNum == 0 ? "UNSORTED" : data.value.hadInNum == data.value.totalNum ? "SORTED" : "SORTING"
    );
});

// 开始入库
const handleSorting = (event) => {
  emits("handleSorting", data);
};

// 选择商品
function handleSelectGoods() {
  emits("select", data);
}
</script>

<style lang="scss" scoped>
.card {
    cursor: pointer;
    width: calc((100% - 48px) / 4);
    display: flex;
    flex-direction: column;
    border: 1px solid var(---el-border-color-light, #E6EAF0);
    background: #fff;
    border-radius: 12px;

    .status {
        display: flex;
        height: 32px;
        padding: 0px 16px;
        justify-content: space-between;
        align-items: center;
        align-self: stretch;
        color: var(---el-color-white, #FFF);
        font-family: "PingFang SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px;
        border-top-left-radius: 12px;
        border-top-right-radius: 12px;
    }

    .status-total {
        color: var(---el-color-white, rgba(255, 255, 255, 0.6));
    }

    .status-UNSORTED {
        background: #DB4646;
    }

    .status-SORTED {
        background: var(---el-color-primary, #059E84);
    }

    .status-SORTING {
        background: #2C85D7;
    }

    .container {
        display: flex;
        padding: 12px;
        flex-direction: row;
        align-items: flex-start;
        align-self: stretch;

        .item {
            display: flex;
            flex-direction: row;
            height: 32px;
            align-items: center;
            gap: 24px;
            align-self: stretch;

            .label {
                color: var(---el-text-color-regular, #505762);
                font-family: "PingFang SC";
                font-size: 18px;
                font-style: normal;
                font-weight: 300;
                width: 80px;
                line-height: 26px;
            }

            .value {
                color: var(---el-text-color-primary, #1C2026);
                font-family: "PingFang SC";
                font-size: 18px;
                font-style: normal;
                font-weight: 400;
                font-style: normal;
                line-height: 26px;
            }
        }

        .adapt {
            // 图像比例1:1
            width: 80px;
            height: 80px;
            position: relative;
            overflow: hidden; // 防止内容溢出

            .pic {
                width: 100%;
                height: 100%;
                object-fit: cover;
                will-change: transform; // 提示浏览器这个元素会变化
                transform: translateZ(0); // 强制GPU加速
                border-radius: 4px;
            }

            .stock {
                display: inline-flex;
                padding: 2px 8px;
                flex-direction: column;
                align-items: center;
                border-radius: 22px 0px 0px 22px;
                background: var(---el-color-warning-light-9, #ffe4ba);
                backdrop-filter: blur(2px);
                position: absolute;
                right: 0;
                bottom: 8px;
                z-index: 1; // 确保在图片上层
            }
        }

        .info {
            flex: 1;
            margin-left: 10px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            height: 80px;

            .product-name {
                color: var(---el-text-color-primary, #1C2026);
                font-family: "PingFang SC";
                font-size: 16px;
                font-weight: 500;
                line-height: 24px;
                margin-bottom: 0;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2; /* 最多显示两行 */
                -webkit-box-orient: vertical;
            }

            .stock-info {
                color: var(---el-text-color-regular, #505762);
                font-family: "PingFang SC";
                font-size: 14px;
                font-weight: 400;
                line-height: 20px;
                display: inline-block;
                padding: 2px 8px;
                background-color: #F5F7FA;
                border-radius: 20px;
            }
        }
    }

    .footer {
        padding: 12px 16px;
        display: flex;
        flex-direction: column;
        gap: 16px;
        border-top: 1px solid var(---el-border-color-light, #E6EAF0);

        .btns-wraper {
            display: flex;
            align-items: center;
            flex: 1;

            .el-button:focus,
            .el-button:hover {
                color: #059e84;
                border-color: #059E84;
                background-color: #e8fff7;
            }

            .el-button {
                min-height: 40px;
                text-align: center;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                gap: 4px;
                color: #059E84;
                border-radius: 8px;
                border: 1.5px solid var(---el-color-primary, #059E84);
                font-family: "PingFang SC";
                font-size: 16px;
                font-style: normal;
                font-weight: 400;
                line-height: 24px;
                margin: 0px;
                display: flex;
                flex: 1;
            }

            .ml {
                margin-left: 16px;
            }

            .el-button--primary {
                background: #059e84;
                color: #fff;
            }

            .el-button--primary:hover {
                background: rgb(55, 177, 157);
                color: #fff;
                border-color: #059E84;
            }
        }
    }
}
</style>
