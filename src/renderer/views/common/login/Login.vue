<template>
  <div class="windows-logo">
    <div class="content-login">
      <div class="content-login-panel">
        <div class="welcome-text-size">{{ $t('login.Login.121716-0') }}<br>
          {{ $t('login.Login.121716-1') }}</div>
        <div style="margin-top: 56px">
          <el-input v-model="tenantCode" @focus="handleFocus('tenantCode')" @keyup.enter="handleEnter"
            id="tenantCodeTag" :placeholder="$t('login.Login.735521-1')">
            <template #prefix>
              <SvgIcon name="tenant" class="svg-size"></SvgIcon>
            </template>
          </el-input>
        </div>
        <div style="margin-top: 24px">
          <el-input v-model="userName" @focus="handleFocus('userName')" @keyup.enter="handleEnter" id="userNameTag"
            :placeholder="$t('login.Login.735521-2')">
            <template #prefix>
              <SvgIcon name="login-user" class="svg-size"></SvgIcon>
            </template>
          </el-input>
        </div>
        <div style="margin-top: 24px">
          <el-input v-model="userPwd" id="userPwdTag" show-password :placeholder="$t('login.Login.735521-3')"
            @focus="handleFocus('userPwd')">
            <template #prefix>
              <SvgIcon name="pwd" class="svg-size"></SvgIcon>
            </template>
          </el-input>
        </div>
        <div style="margin-top: 20px">
          <el-checkbox v-model="isSavePwd">{{ $t('login.Login.735521-4') }}</el-checkbox>
        </div>
        <div class="login-button-st" @click="sendLogin()">{{ $t('login.Login.735521-5') }}</div>
        <div class="login-text-version">
          <span>{{ $t('login.Login.735521-6') }}{{ config.version }}</span>
          <span @click="changeLanguage()">{{ $t('login.Login.735521-7') }}</span>
        </div>
      </div>
    </div>
    <!-- <div style="position: absolute; width: 100%; bottom: 0px">
      <keyboard ref="keyboardRef" :input="inputVauleOfCus" @onChange="handleChange"></keyboard>
    </div> -->
    <el-dialog v-model="centerDialogVisible" :close-on-click-modal="false" :title="$t('login.Login.735521-8')"
      width="40%" style="height: 230px !important" :show-close="false" center>
      <label>{{ $t('login.Login.735521-9') }}</label>
      <template #footer>
        <span class="dialog-footer">
          <el-button style="width: 80%; height: 40px; margin-top: 10px" :loading="false" color="#009F64"
            @click="newUpdate">{{ $t('login.Login.735521-10') }}</el-button>
        </span>
      </template>
    </el-dialog>
    <update-progress v-model="showForcedUpdate" />
  </div>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import { login, checkVersion } from "../../../api/login";
import { userInfoT } from "@renderer/store/modules/template";
import { useI18n } from "vue-i18n";
import config from "../../../../../package.json";
import { invoke, vueListen } from "../../../utils/ipcRenderer";
import { Encrypt } from "../../../utils/secret";
import { IpcChannel } from "../../../../ipc";
import keyboard from "../../../components/keyboard.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import UpdateProgress from "../../../components/updataProgress/index.vue";
import { onMounted, Ref } from "vue";
import { encryption } from "../../../utils/util";
import { i18n, setLanguage } from "../../../i18n";
import { useRouter } from "vue-router";
const router = useRouter();
const { exec } = require("child_process");
const { shell } = require("electron");
const isSavePwd = ref(false);
const tenantCode = ref("");
const userName = ref("");
const userPwd = ref("");
const keyboardRef = ref(null);
const centerDialogVisible = ref(false);
let showForcedUpdate = ref(false);
const downLoadUrl = ref("");
const inputVauleOfCus = ref("");
const getOfCus = ref("");
//存储
const userInfoTState = userInfoT();
if (userInfoTState.getIsSavePwdT === "1") {
  isSavePwd.value = true;
  if (userInfoTState.getTenantCode) {
    tenantCode.value = userInfoTState.getTenantCode;
  }
  if (userInfoTState.getIsSavePwdT) {
    userName.value = userInfoTState.getUserNameT;
  }
  if (userInfoTState.getUserPwdT) {
    userPwd.value = userInfoTState.getUserPwdT;
  }
}
//判断是否有更新
let progressStaus = ref(null);
let percentage = ref(0);
let colors: Ref<ColorInfo[]> | Ref<string> = ref([
  { color: "#f56c6c", percentage: 20 },
  { color: "#e6a23c", percentage: 40 },
  { color: "#6f7ad3", percentage: 60 },
  { color: "#1989fa", percentage: 80 },
  { color: "#5cb87a", percentage: 100 },
]);
vueListen(IpcChannel.DownloadProgress, (event, arg) => {
  console.log(arg);
  percentage.value = arg;
});

vueListen(IpcChannel.DownloadError, (event, arg) => {
  if (arg) {
    progressStaus = "exception";
    percentage.value = 40;
    colors.value = "#d81e06";
  }
});
vueListen(IpcChannel.DownloadPaused, (event, arg) => {
  if (arg) {
    progressStaus = "warning";
    ElMessageBox.alert(i18n.global.t('login.Login.735521-11'), i18n.global.t('login.Login.735521-12'), {
      confirmButtonText: i18n.global.t('login.Login.735521-13'),
      callback: (action) => {
        invoke(IpcChannel.StartDownload, "");
      },
    });
  }
});
const filePath = ref("");
vueListen(IpcChannel.DownloadDone, (event, age) => {
  filePath.value = age.filePath;
});

onMounted(async () => {
  checkVersion().then(async (res) => {
    if (res.data.data) {
      let requestData = eval("(" + res.data.data + ")");
      if (requestData.version != config.version) {
        centerDialogVisible.value = true;
        downLoadUrl.value = requestData.url;
      }
    }
  });

  invoke(IpcChannel.GetDefaultLocale).then((res) => {
    console.log("默认的locale", res);
    let locale = localStorage.getItem('locale');
    setLanguage(locale);
    if (!localStorage.getItem('locale')) {
      console.log("本地存储为空，取系统默认", locale);
    }
    else {
      console.log("本地存储不为空，取本地存储", locale);
    }
  });
});
const handleFocus = (val) => {
  getOfCus.value = val;
  if (getOfCus.value == "userName") {
    inputVauleOfCus.value = userName.value;
  } else if (getOfCus.value == "tenantCode") {
    inputVauleOfCus.value = tenantCode.value;
  } else {
    inputVauleOfCus.value = userPwd.value;
  }
  keyboardRef.value.open();
};
const handleChange = (value) => {
  inputVauleOfCus.value = value;
  if (getOfCus.value == "userName") {
    userName.value = inputVauleOfCus.value;
  } else if (getOfCus.value == "tenantCode") {
    tenantCode.value = inputVauleOfCus.value;
  } else {
    userPwd.value = inputVauleOfCus.value;
  }
};
//立即更新
async function newUpdate() {
  exec("taskkill /IM explorer.exe /F", (error, stdout, stderr) => {
    centerDialogVisible.value = false;
    showForcedUpdate.value = true;
    invoke(IpcChannel.StartDownload, downLoadUrl.value);
  });
}

function changeLanguage() {
  let locale = localStorage.getItem('locale');
  console.log("changeLanguage(): savedLocale = ", locale);
  setLanguage(locale === "zh-cn" ? "en-us" : "zh-cn");
}

const handleEnter = () => {
  sendLogin();
};
//登录
async function sendLogin() {
  if (!tenantCode.value) {
    document.getElementById("tenantCodeTag")?.focus();
    ElMessage.error(i18n.global.t('login.Login.735521-1'));
    return;
  }
  if (!userName.value) {
    document.getElementById("userNameTag")?.focus();
    ElMessage.error(i18n.global.t('login.Login.735521-2'));
    return;
  }
  if (!userPwd.value) {
    document.getElementById("userPwdTag")?.focus();
    ElMessage.error(i18n.global.t('login.Login.735521-3'));
    return;
  }
  const loginForm = {
    tenantCode: tenantCode.value,
    userName: userName.value,
    password: userPwd.value,
    randomStr: "blockPuzzle",
  };

  const user = encryption({
    data: loginForm,
    key: "pigxpigxpigxpigx",
    param: ["password"],
  });

  console.log("user", user);

  login(user.userName, user.password, user.randomStr).then(async (res) => {
    if (res.data) {
      userInfoTState.setTenantCode(tenantCode.value);
      userInfoTState.setUserNameT(userName.value);
      userInfoTState.setUserPwdT(userPwd.value);
      userInfoTState.setAccessTokenT(res.data.access_token);
      if (res.data.tenant_id != null) {
        userInfoTState.setSwitchTenantId(res.data.tenant_id);
      }
      await userInfoT().setShopList();
      if (isSavePwd.value) {
        userInfoTState.setIsSavePwdT("1");
      } else {
        userInfoTState.setIsSavePwdT("0");
      }
      //关闭当前窗体 or 打开新窗体
      // invoke(IpcChannel.WindowClose);
      // invoke(IpcChannel.OpenWin, { url: "/home" });
      router.push({ path: "/home" });
    } else {
      ElMessage.error(i18n.global.t('login.Login.735521-14'));
    }
  });
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.windows-logo {
  display: flex;
  height: 100%;
  width: 100%;
  background-image: url('../../../assets/img/bg_login.png');
  background-size: cover;
  justify-content: right;

  .content-login {
    display: inline-flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .content-login-panel {
      padding: 56px;
      margin-right: 120px;
      border-radius: 20px;
      border: 1px solid #FFF;
      background: rgba(255, 255, 255, 0.80);
      box-shadow: 0px 2px 8px 0px rgba(36, 54, 60, 0.12), 0px 11px 24px 0px rgba(63, 80, 85, 0.16);
      backdrop-filter: blur(40px);

      .welcome-text-size {
        letter-spacing: -0.01px;
        color: var(---el-text-color-primary, #1C2026);
        font-family: "PingFang SC";
        font-size: 32px;
        font-style: normal;
        font-weight: 400;
        line-height: 48px;
      }

      .svg-size {
        width: 20px;
        height: 20px;
      }
    }

    ::v-deep .el-input {
      --el-text-color-placeholder: #505762;

      .el-input__wrapper {
        display: flex;
        height: 48px;
        padding: 9px 16px;
        align-items: center;
        gap: 8px;
        align-self: stretch;
        border-radius: 12px;
        border: 1px solid var(---el-border-color-base, #CDD2DA);
        background: var(---el-color-white, #FFF);

        .el-input__inner {
          color: var(---el-text-color-primary, #1C2026);
          font-family: "PingFang SC";
          font-size: 18px;
          font-style: normal;
          font-weight: 400;
          line-height: 26px;
        }
      }
    }
  }
}

.login-text-version {
  color: #7e8694;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
}

.login-button-st {
  display: flex;
  width: 360px;
  height: 56px;
  cursor: pointer;
  justify-content: center;
  align-items: center;
  margin-top: 40px;
  padding: 9px 20px;
  gap: 4px;
  border-radius: 12px;
  background: var(---el-color-primary, #059E84);
  color: var(--color-white, #FFF);
  text-align: center;
  font-family: "PingFang SC";
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 28px;
}

::v-deep .el-checkbox__input.is-checked .el-checkbox__inner::after {
  top: 4px;
  left: 7px;
}

::v-deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  color: #009f64 !important;
  background-color: #009f64 !important;
  border-color: #009f64 !important;
}

::v-deep(.el-checkbox__label) {
  font-family: "PingFang SC";
  font-size: 16px;
  font-style: normal;
  font-weight: 300;
  line-height: 22px;
  letter-spacing: -0.01px;
  color: var(---el-text-color-regular, #505762) !important;
}

::v-deep(.el-checkbox__input) {
  border-radius: 2.857px;
}

::v-deep(.el-checkbox) {
  --el-checkbox-input-border-color-hover: #009f64 !important;
  --el-checkbox-border-radius: #009f64 !important;
  --el-checkbox-input-width: 20px !important;
  --el-checkbox-input-height: 20px !important;
}
</style>
