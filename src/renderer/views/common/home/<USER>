<template>
  <div style="height: 100%;">
    <div class="title-right-user">
      <div class="div-w-style">
        <img src="@renderer/assets/img/icon-user.png" class="img-size"/>
        <span class="tx-font-size">11</span>
      </div>
      <div class="div-w-style" @click="resetLogin()">
        <img src="@renderer/assets/img/icon-reset.png" class="img-size"/>
        <span class="tx-font-size">1111</span>
      </div>
      <div class="div-w-style" @click="exitWindows()">
        <img src="@renderer/assets/img/icon-colse.png" class="img-size"/>
        <span class="tx-font-size">返回</span>
      </div>
    </div>
    <div class="title-proj-text">Windows端分拣系统</div>
  </div>
</template>

<script setup lang="ts">
import { vueListen } from "../../../utils/ipcRenderer";
import { IpcChannel } from "../../../../ipc";
import { useRouter } from 'vue-router'
import { invoke } from "../../../utils/ipcRenderer";
const router = useRouter()
vueListen(IpcChannel.SendDataTest, (event, data) => {
  console.log(data)
})

//退出系统
function exitWindows() {
  router.go(-1)
}
//注销登录
function resetLogin() {
  //关闭当前窗体
  invoke(IpcChannel.WindowClose);
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped >
  .title-right-user {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    background-color: #1BD49B;
    height: 80px;
    .div-w-style{
      cursor: pointer;
      padding-right: 32px;
      display: flex;
      align-items: center;
      .img-size {
        width: 28px;
        height: 26px;
        margin-right: 5px;
      }
      .tx-font-size {
        font-size: 18px;
        font-weight: 600;
        color: white;
      }
    }
  }
  .title-proj-text {
    display: flex;
    justify-content: center;
    color: #fff;
    background-color: #1BD49B;
    height: 40px;
    font-size: 26px;
    font-weight: 800;
  }
  .txt-version-style {
    display: flex;
    justify-content: flex-end;
    font-size: 14px;
    color: #fff;
    padding-right: 50px;
    height: 25px;
    background-color: #1BD49B;
    font-weight: 600;
  }
  .main_container{
    position: relative;
    display: flex;
    justify-content: center;
    padding: 0;
    background-color: #1BD49B;
    height: calc(100vh - 145px);
  }
  .main_container_mask {
    position: absolute;
    display: flex;
    justify-content: space-between;
    padding-top: 30px;
    padding-left: 30px;
    padding-right: 30px;
    top: 0px;
    width: 96%;
    color: #fff;
    height: calc(100vh - 175px);
    background: rgba(0,0,0,0.3);
    border-radius: 4px;
    .div-cursor {
      text-align: center;
      cursor: pointer;
    }
  }
  .home-tx-font-size {
        font-size: 20px;
        font-weight: 600;
        color: white;
      }
</style>
