<template>
  <div class="page">
    <div class="header">
      <div class="l-section">
        <div class="logo"></div>
        <div>{{ $t("home.Home.293447-0") }}</div>
      </div>
      <div class="center">{{ displayName }}</div>
      <div class="r-section">
        <div class="user-icon"></div>
        <div class="user-name">{{ accessTokenTState.getUserNameT }}</div>
        <div class="role">{{ $t("home.Home.739021-0") }}</div>
        <div class="split"></div>
        <SvgIcon
          name="cancel-circle"
          class="svg-size"
          @click="resetLogin('close')"
        ></SvgIcon>
      </div>
    </div>
    <div class="content">
      <div class="menu-bar">
        <div @click="handleMenu('sorting')" class="menu-item">
          <div class="menu-icon">
            <img
              :src="
                activeMenu === 'sorting'
                  ? 'https://oss-public.yunlizhi.cn/frontend/delivery-center/sort-weight/sorting_active.svg'
                  : 'https://oss-public.yunlizhi.cn/frontend/delivery-center/sort-weight/sorting.svg'
              "
              style="width: 100%; height: 100%"
            />
          </div>
          <div class="menu-text" :class="{ active: activeMenu === 'sorting' }">
            {{ $t("home.Home.293447-1") }}
          </div>
        </div>
        <div @click="handleMenu('store')" class="menu-item">
          <div class="menu-icon">
            <img
              :src="
                activeMenu === 'store'
                  ? 'https://oss-public.yunlizhi.cn/frontend/delivery-center/sort-weight/store_active.svg'
                  : 'https://oss-public.yunlizhi.cn/frontend/delivery-center/sort-weight/store.svg'
              "
              style="width: 100%; height: 100%"
            />
          </div>
          <div class="menu-text" :class="{ active: activeMenu === 'store' }">
            {{ $t("home.Home.739021-1") }}
          </div>
        </div>
        <!-- <div @click="handleMenu('learn')" class="menu-item">
          <div class="menu-icon">
            <img
              :src="
                activeMenu === 'learn'
                  ? 'https://oss-public.yunlizhi.cn/frontend/delivery-center/sort-weight/learn_active.svg'
                  : 'https://oss-public.yunlizhi.cn/frontend/delivery-center/sort-weight/learn.svg'
              "
              style="width: 100%; height: 100%"
            />
          </div>
          <div class="menu-text" :class="{ active: activeMenu === 'learn' }">
            {{ $t("home.Home.293447-2") }}
          </div>
        </div> -->
        <div class="place-holder"></div>
        <div @click="handleMenu('setting')" class="menu-item">
          <div class="menu-icon">
            <img
              :src="
                activeMenu === 'setting'
                  ? 'https://oss-public.yunlizhi.cn/frontend/delivery-center/sort-weight/setting_active.svg'
                  : 'https://oss-public.yunlizhi.cn/frontend/delivery-center/sort-weight/setting.svg'
              "
              style="width: 100%; height: 100%"
            />
          </div>
          <div class="menu-text" :class="{ active: activeMenu === 'setting' }">
            {{ $t("home.Home.293447-3") }}
          </div>
        </div>
      </div>
      <div style="width: calc(100% - 84px); height: 100%">
        <router-view></router-view>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { logout } from "@renderer/api/login";
import { ElMessage } from "element-plus";
import { useRouter } from "vue-router";
import { useRoute } from "vue-router";
import { userInfoT } from "@renderer/store/modules/template";
import { vueListen } from "../../../utils/ipcRenderer";
import { IpcChannel } from "../../../../ipc";
import { invoke } from "../../../utils/ipcRenderer";
import { startInitAi } from "../../../utils/ipcRenderer";
import { i18n } from "@renderer/i18n";
const router = useRouter();
const route = useRoute();
const activeMenu = ref("sorting");

//存储
const accessTokenTState = userInfoT();
vueListen(IpcChannel.SendDataTest, (event, data) => {});

const handleMenu = (key) => {
  activeMenu.value = key;
  router.push({ path: "/home/<USER>", query: { menuKey: key } });
};

onMounted(() => {
  const locale = i18n.global.locale;
  console.log("默认的locale", locale);
});

//注销登录
function resetLogin(val) {
  logout(accessTokenTState.getAccessTokenT).then((res) => {
    if (res.data.data == true) {
      if (val == "close") {
        invoke(IpcChannel.AppClose);
      } else {
        invoke(IpcChannel.WindowClose);
        invoke(IpcChannel.OpenWin, { url: "/" });
      }
    } else {
      ElMessage.error(res.data.msg);
    }
  });
}

const displayName = computed(() => route.meta?.zh ?? "");

// 启动识别服务
startInitAi();
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.svgimg-size {
  width: 220px;
  height: 220px;
}

.page {
  width: 100%;
  height: 100%;
  background: #21262d;
  display: flex;
  flex-direction: column;

  .header {
    width: 100%;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 20px;

    .l-section {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 8px;
      color: #f0f6fc;
      font-family: "PingFang SC";
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 24px;

      .logo {
        width: 36px;
        height: 36px;
        background: url("../../assets/icons/svg/canpan_logo.svg") no-repeat;
        background-size: cover;
      }
    }

    .center {
      text-align: center;
      flex: 1;
      color: var(---el-text-color-primary, #f0f6fc);
      text-align: center;
      font-family: "PingFang SC";
      font-size: 20px;
      font-style: normal;
      font-weight: 400;
      line-height: 28px;
    }

    .r-section {
      display: flex;
      align-items: center;

      .user-name {
        margin: 0 12px 0 8px;
        color: #f0f6fc;
        font-family: "PingFang SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px;
      }

      .role {
        padding: 0px 6px;
        border-radius: 22px;
        background: var(---el-color-normal-bg, #f6f7f9);
        color: var(---el-border-color-extra-light, #161b22);
        text-align: center;
        font-family: "PingFang SC";
        font-size: 12px;
        font-style: normal;
        font-weight: 500;
        line-height: 20px;
      }

      .split {
        width: 1px;
        height: 24px;
        background: #6e7681;
        margin: 0 20px;
      }

      .user-icon {
        width: 28px;
        height: 28px;
        background: url("../../assets/icons/svg/user_icon.svg") no-repeat;
        background-size: cover;
      }

      .svg-size {
        width: 24px;
        height: 24px;
      }
    }
  }

  .content {
    flex: 1;
    height: calc(100% - 56px);
    display: flex;

    .menu-bar {
      width: 84px;
      height: 100%;
      padding: 32px 8px;
      display: flex;
      flex-direction: column;
      gap: 32px;

      .menu-item {
        width: 100%;
        height: 70px;
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;

        .menu-icon {
          width: 44px;
          height: 44px;
          background-size: cover;
        }

        .menu-text {
          text-align: center;
          color: var(---el-text-color-secondary, #b1bac6);
          text-align: center;
          font-family: "PingFang SC";
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 24px;

          &.active {
            color: #4fc5a9;
          }
        }
      }

      .place-holder {
        flex: 1;
      }
    }
  }
}
</style>
