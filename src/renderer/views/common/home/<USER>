<template>
  <div class="print-page" v-for="(printerObj, idx) in printerArr" :key="idx">
    <div class="font-16 bold">{{ printerObj.tempCode }}</div>
    <div class="span-item-2 font-12 mt-2">{{ printerObj.customerName }}</div>
    <div class="span-item-2 font-12 mt-2">{{ printerObj.spuName }}</div>
    <div class="content">
      <div>
        <div class="span-item-2 font-12 mt-2">
          下单: <span>{{ printerObj.showOrderCount }}</span>
        </div>
        <div class="span-item-2 font-16 mt-2 bold">
          送货: <span>{{ printerObj.showDeliveryNumber }}</span>
        </div>
      </div>
      <div class="img-container">
        <img src="../../../assets/img/QRcode.jpeg" />
      </div>
    </div>

    <div class="bouttom-div-st mt-2 font-12">
      <div class="span-item-1">{{ printerObj.lineName }}</div>
      <div class="span-item-1">{{ printerObj.deliveryDate }}</div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted, toRaw, Ref } from "vue";
import { invoke } from "../../../utils/ipcRenderer";
import { IpcChannel } from "../../../../ipc";
import { userInfoT } from "@renderer/store/modules/template";
import type { WebContentsPrintOptions } from "electron";
const margins: Ref<WebContentsPrintOptions["margins"]> = ref({
  marginType: "none",
  top: 0,
  bottom: 0,
  left: 0,
  right: 0,
});
const userInfoTState = userInfoT();
const printerArr = JSON.parse(userInfoTState.getPrinterObj);
const pageSizeObject = ref({ width: 60000, height: 40000 });
onMounted(async () => {
  //开始打印
  printPage()
});

async function printPage() {
  const printRes = await invoke(IpcChannel.PrintHandlePrint, {
    silent: true,
    deviceName: userInfoTState.getPrinterName,
    printBackground: false,
    margins: { marginType: "none" },
    pageSize: toRaw(pageSizeObject.value),
  });
  if (printRes.success || printRes.failureReason) {
    invoke(IpcChannel.WindowClose);
  }
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.bouttom-div-st {
  display: flex;
  justify-content: space-between;
}
.span-item-2 {
  color: #1c2026;
  font-weight: 500;
}

.bold {
  font-weight: bold;
}

.content {
  display: flex;
  align-items: flex-start;
  gap: 5mm;

  .img-container {
    width: 15mm;
    height: 15mm;

    img {
      width: 100%;
      height: 100%;
    }
  }
}

.print-page {
  page-break-after: always;
  border: none;
  box-sizing: border-box;
  color: #1c2026;
  width: 60mm;
  height: 40mm;
  margin: 0mm 2mm;
}
</style>
