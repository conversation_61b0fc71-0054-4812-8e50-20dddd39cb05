<template>
  <div class="menu-content">
    <div
      class="item"
      v-for="item in renderMenus"
      :key="item.name"
      @click="handleMenuDetail(item)"
    >
      <div>
        <img
          :src="`https://oss-public.yunlizhi.cn/frontend/delivery-center/sort-weight/${item.icon}.svg`"
        />
      </div>
      <div class="name">{{ item.name }}</div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from "vue";
import { useRoute } from "vue-router";
import { useRouter } from "vue-router";
import { i18n } from "@renderer/i18n";
import { logout } from "@renderer/api/login";
import { IpcChannel } from "../../../../ipc";
import { invoke } from "../../../utils/ipcRenderer";
import { userInfoT } from "@renderer/store/modules/template";

const router = useRouter();
const route = useRoute();
const menuKey = ref(route.query.menuKey ?? "sorting");
const accessTokenTState = userInfoT();

const menuList = ref([
  {
    menu: "sorting",
    children: [
      {
        name: i18n.global.t("home.menu.716229-0"),
        icon: "goods_sorting",
        fun: () => {
          router.push({ path: "/home/<USER>" });
        },
      },
      {
        name: i18n.global.t("home.menu.716229-1"),
        icon: "line_sorting",
        fun: () => {
          router.push({ path: "/home/<USER>" });
        },
      },
      {
        name: i18n.global.t("home.menu.716229-2"),
        icon: "order_sorting",
        fun: () => {
          router.push({ path: "/home/<USER>" });
        },
      },
      {
        name: i18n.global.t("home.menu.716229-3"),
        icon: "out_stock",
        fun: () => {
          router.push({ path: "/home/<USER>" });
        },
      },
      // { name: i18n.global.t('home.menu.716229-4'), icon: "identify_sorting" },
    ],
  },
  {
    menu: "store",
    children: [
      {
        name: i18n.global.t('home.menu.851864-0'),
        icon: "goods_sorting",
        fun: () => {
          router.push({ path: "/home/<USER>" });
        },
      },
      {
        name: i18n.global.t('home.menu.851864-1'),
        icon: "line_sorting",
        fun: () => {
          router.push({ path: "/home/<USER>" });
        },
      },
      {
        name: i18n.global.t('home.menu.851864-2'),
        icon: "order_sorting",
        fun: () => {
          router.push({ path: "/home/<USER>" });
        },
      },
      {
        name: i18n.global.t('home.menu.851864-3'),
        icon: "order_sorting",
        fun: () => {
          router.push({ path: "/home/<USER>" });
        },
      },
    ],
  },
  {
    menu: "learn",
    children: [
      { name: i18n.global.t("home.menu.716229-6"), icon: "active_learning" },
      { name: i18n.global.t("home.menu.716229-7"), icon: "import" },
      { name: i18n.global.t("home.menu.716229-8"), icon: "export" },
      { name: i18n.global.t("home.menu.716229-9"), icon: "clearn" },
      { name: i18n.global.t("home.menu.716229-10"), icon: "LAN" },
    ],
  },
  {
    menu: "setting",
    children: [
      { 
        name: i18n.global.t("home.menu.716229-11"), 
        icon: "idenfity_setting",
        fun: () => {
          router.push({ path: "/home/<USER>" });
        },
       },
      { name: i18n.global.t("home.menu.716229-12"), icon: "watermark" },
      { name: i18n.global.t("home.menu.716229-13"), icon: "print_setting" },
      { 
        name: i18n.global.t("home.menu.716229-14"), 
        icon: "login_out",
        fun: () => {
          resetLogin()
        },
       },
    ],
  },
]);

const handleMenuDetail = (row) => {
  row.fun();
};

const renderMenus = computed(() => {
  return (
    menuList.value.find((item) => item.menu === menuKey.value)?.children ?? []
  );
});

//注销登录
function resetLogin(val) {
  logout(accessTokenTState.getAccessTokenT).then((res) => {
    if (res.data.data == true) {
      if (val == "close") {
        invoke(IpcChannel.AppClose);
      } else {
        invoke(IpcChannel.WindowClose);
        invoke(IpcChannel.OpenWin, { url: "/" });
      }
    } else {
      ElMessage.error(res.data.msg);
    }
  });
}

watch(
  () => route.query.menuKey,
  (newVal) => {
    menuKey.value = newVal || "sorting";
  },
  { immediate: true }
);
</script>

<style scoped lang="scss">
.menu-content {
  width: 100%;
  height: auto;
  max-height: 100%;
  padding: 20px;
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  gap: 20px;

  .item {
    flex: 1 1 400px;
    max-width: 400px;
    height: 234px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.8);
    background: linear-gradient(
      180deg,
      rgba(185, 211, 225, 0.95) 0%,
      rgba(225, 235, 243, 0.95) 100%
    );

    img {
      width: 88px;
      height: 88px;
    }

    .name {
      color: var(---el-text-color-primary, #1c2026);
      text-align: center;
      font-family: "PingFang SC";
      font-size: 18px;
      font-style: normal;
      font-weight: 300;
      line-height: 26px;
      margin-top: 8px;
    }
  }
}
</style>
