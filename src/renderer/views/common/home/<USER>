<template>
  <div class="page">
    <div class="title-right-user">
      <div class="div-w-style ml-30" style="width: 70%;background-color: chartreuse;">
        <el-button type="success" round @click="exitWindows()" size="large">返回</el-button>
        <el-input v-model="searchVuale" clearable placeholder="请输入搜索内容"
          style="width: 100%;height: 45px;margin-left: 20px;" />
      </div>
      <div class="div-w-style" @click="exitWindows()" style="width: 30%;background-color: red;">
        <el-button type="success" round size="large">一键打印</el-button>
        <el-button type="warning" round size="large">筛选</el-button>
        <el-button type="danger" round size="large">一键分拣</el-button>
      </div>
    </div>
    <div class="container">
      <div class="sidebar">
        树
      </div>
      <div class="content">
        内容
      </div>
    </div>
    <div class="div-child-st">
      <div>
        <span class="txt-left-size">青豇豆优质</span>
        <span class="txt-left-num">(库存： 0.0斤)</span>
      </div>
      <div>
        <span class="txt-left-size">不打印分拣小票：</span>
        <el-radio-group v-model="searchVuale" @click="radioOnClick()">
          <el-radio size="large"/>
        </el-radio-group>
      </div>
    </div>
    <div class="div-line-st"></div>
    <div class="bt-select-st">
      <el-button type="primary">全部</el-button>
      <el-button class="ml5">斤</el-button>
      <el-button class="ml5">中袋</el-button>
      <el-button class="ml5">大袋</el-button>
    </div>
    <div class="div-mian-grid">
      <div class="fl">
        <div class="c-child-st">1</div>
        <div class="c-child-cz">操作</div>
      </div>
      <div class="fl">
        <div class="c-child-st">1</div>
        <div class="c-child-cz">操作</div>
      </div>
      <div class="fl">
        <div class="c-child-st">1</div>
        <div class="c-child-cz">操作</div>
      </div>
      <div class="fl">
        <div class="c-child-st">1</div>
        <div class="c-child-cz">操作</div>
      </div>
      <div class="fl">
        <div class="c-child-st">1</div>
        <div class="c-child-cz">操作</div>
      </div>
      
      <div class="fl">
        <div class="c-child-st">1</div>
        <div class="c-child-cz">操作</div>
      </div>
      
      <div class="fl">
        <div class="c-child-st">1</div>
        <div class="c-child-cz">操作</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { vueListen } from "../../../utils/ipcRenderer";
import { IpcChannel } from "../../../../ipc";
import { useRouter } from 'vue-router'
import { invoke } from "../../../utils/ipcRenderer";
const router = useRouter()
const searchVuale = ref('')
vueListen(IpcChannel.SendDataTest, (event, data) => {
  console.log(data)
})

//退出系统
function exitWindows() {
  router.go(-1)
}
//注销登录
function resetLogin() {
  //关闭当前窗体
  invoke(IpcChannel.WindowClose);
}
//单选按钮点击事件
function radioOnClick() {
  alert(111)
}
</script>

<style rel="stylesheet/scss" lang="scss"  scoped>
.page {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.title-right-user {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #1BD49B;
  -webkit-app-region: drag;
  height: 80px;

  .div-w-style {
    cursor: pointer;
    padding-right: 32px;
    display: flex;
    align-items: center;

    .img-size {
      width: 28px;
      height: 26px;
      margin-right: 5px;
    }

    .tx-font-size {
      font-size: 18px;
      font-weight: 600;
      color: white;
    }
  }
}

.container {
  display: flex;
  flex: 1;
  .sidebar {
    width: 200px;
    background: #eee;
  }
  .content {
    flex: 1;
    padding: 16px;
  }
}
</style>
