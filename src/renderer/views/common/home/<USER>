<template>
  <div class="page">
    <div class="title-right-user">
      <div class="div-w-style">
        <SvgIcon name="home-user"></SvgIcon>
        <span class="tx-font-size">{{ accessTokenTState.getUserNameT }}</span>
      </div>
      <div class="div-w-style" @click="resetLogin('reset')">
        <SvgIcon name="send-out"></SvgIcon>
        <span class="tx-font-size">注销</span>
      </div>
      <div class="div-w-style" @click="resetLogin('close')">
        <SvgIcon name="title-close"></SvgIcon>
        <span class="tx-font-size">关闭</span>
      </div>
    </div>
    <div class="title-proj-text">Windows端分拣系统</div>
    <div class="main_container">
      <el-row :gutter="20" style="width: 100%">
        <el-col :span="6">
          <div class="div-cursor" @click="goProductPage()">
            <SvgIcon name="home-item1" class="svgimg-size"></SvgIcon>
            <div class="home-tx-font-size">按商品分拣</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="div-cursor" @click="goCustomerPage()">
            <SvgIcon name="home-item2" class="svgimg-size"></SvgIcon>
            <div class="home-tx-font-size">按客户分拣</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="div-cursor" @click="goLinesPage()">
            <SvgIcon name="home-item6" class="svgimg-size"></SvgIcon>
            <div class="home-tx-font-size">按线路分拣</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="div-cursor" @click="goScarceGoodsPage()">
            <SvgIcon name="home-item3" class="svgimg-size"></SvgIcon>
            <div class="home-tx-font-size">缺货商品</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="div-cursor" @click="goOrderPage()">
            <SvgIcon name="home-item4" class="svgimg-size"></SvgIcon>
            <div class="home-tx-font-size">订单分拣差异</div>
          </div>
        </el-col>

        <el-col :span="6">
          <div class="div-cursor">
            <SvgIcon name="home-item5" class="svgimg-size"></SvgIcon>
            <div class="home-tx-font-size">系统设置</div>
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="main_container">
      <el-row :gutter="20" style="width: 100%; margin-top: 30px">
        <!-- <el-col :span="6">
          <div class="div-cursor">
            <SvgIcon name="home-item5" class="svgimg-size"></SvgIcon>
            <div class="home-tx-font-size">系统设置</div>
          </div>
        </el-col> -->
        <el-col :span="6"></el-col>
        <el-col :span="6"></el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { logout } from "@renderer/api/login";
import { ElMessage } from "element-plus";
import { vueListen } from "../../../utils/ipcRenderer";
import { userInfoT } from "@renderer/store/modules/template";
import { IpcChannel } from "../../../../ipc";
import { useRouter } from "vue-router";
import { invoke } from "../../../utils/ipcRenderer";

const router = useRouter();
//存储
const accessTokenTState = userInfoT();
vueListen(IpcChannel.SendDataTest, (event, data) => {
  console.log(data);
});
//注销登录
function resetLogin(val) {
  logout(accessTokenTState.getAccessTokenT).then((res) => {
    if (res.data.data == true) {
      if (val == "close") {
        invoke(IpcChannel.AppClose);
      } else {
        invoke(IpcChannel.WindowClose);
        invoke(IpcChannel.OpenWin, { url: "/" });
      }
    } else {
      ElMessage.error(res.data.msg);
    }
  });
}

// 按商品分拣
function goProductPage() {
  router.push("productSorting");
}
// 按客户分拣
function goCustomerPage() {
  // ElMessage({
  //   message: '功能维护中...',
  //   type: 'success',
  // })
  router.push("customerSorting");
}

function goLinesPage() {
  router.push("linesSorting");
}

// 缺货商品
function goScarceGoodsPage() {
  // router.push('testDemo')
  ElMessage({
    message: "功能维护中...",
    type: "success",
  });
}
// 订单差异分拣
function goOrderPage() {
  ElMessage({
    message: "功能维护中...",
    type: "success",
  });
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.svgimg-size {
  width: 220px;
  height: 220px;
}
.page {
  height: 100%;
}
.title-right-user {
  display: flex;
  padding: 12px 20px;
  height: 50px;
  align-items: center;
  justify-content: flex-end;
  gap: 8px;
  align-self: stretch;
  background: linear-gradient(90deg, #185a9d 0%, #43cea2 100%);
  .div-w-style {
    cursor: pointer;
    display: flex;
    padding-left: 32px;
    align-items: center;
    .img-size {
      width: 28px;
      height: 26px;
      margin-right: 5px;
    }
    .tx-font-size {
      margin-left: 5px;
      color: #fff;
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      letter-spacing: 2.24px;
    }
  }
}
.title-proj-text {
  display: flex;
  padding-bottom: 64px;
  padding-top: 50px;
  justify-content: center;
  font-weight: 800;
  color: #1c2026;
  font-family: Avenir;
  font-size: 36px;
  font-style: normal;
  font-weight: 800;
  line-height: 32px; /* 88.889% */
}
.main_container {
  .div-cursor {
    text-align: center;
    cursor: pointer;
    height: 250px;
  }
}
.home-tx-font-size {
  margin-top: -44px;
  color: #1c2026;
  font-family: PingFang SC;
  font-size: 32px;
  font-style: normal;
  font-weight: 600;
}
</style>
