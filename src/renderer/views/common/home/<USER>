<template>
  <div class="page">
    <div class="title-right-user">
      <div class="div-w-style ml-30" style="width: 70%;background-color: chartreuse;">
        <el-button type="success" round @click="exitWindows()" size="large">返回</el-button>
        <el-input v-model="searchVuale" clearable placeholder="请输入搜索内容"
          style="width: 100%;height: 45px;margin-left: 20px;" />
      </div>
      <div class="div-w-style" @click="exitWindows()" style="width: 30%;background-color: red;">
        <el-button type="success" round size="large">一键打印</el-button>
        <el-button type="warning" round size="large">筛选</el-button>
        <el-button type="danger" round size="large">一键分拣</el-button>
      </div>
    </div>
    <div class="container">
      <div class="sidebar">
        树
      </div>
      <div class="content">
        <div class="row">
          <select class="grip-right" v-model="selName">
            <option v-for="(item, index) in printers" :key="index" :value="item.name">{{ item.displayName }}</option>
          </select>
          <button type="button" @click="printPage">{{ t('print.print') }}</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref,onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { IpcChannel } from "../../../../ipc";
import { useRouter } from 'vue-router'
import { invoke } from "../../../utils/ipcRenderer";
const router = useRouter()
const { t } = useI18n()
const searchVuale = ref('')
const selName = ref('')
const printers = ref<Electron.PrinterInfo[]>([])
  onMounted(async () => {
  // 获取打印机列表
  printers.value = await invoke(IpcChannel.GetPrinters)
  if (printers.value.length) {
    const defaultItem = printers.value.find(v => v.isDefault)
    if (defaultItem) {
      selName.value = defaultItem.name
    } else {
      selName.value = printers.value[0].name
    }
  }
})

async function printPage() {
  if (selName.value) {
    invoke(IpcChannel.OpenWin, {url: "/PrintPage"}); 
  }
}
//退出系统
function exitWindows() {
  router.go(-1)
}
//注销登录
function resetLogin() {
  //关闭当前窗体
  // invoke(IpcChannel.WindowClose);
}
//单选按钮点击事件
function radioOnClick() {
  alert(111)
}
</script>

<style rel="stylesheet/scss" lang="scss"  scoped>
.page {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.title-right-user {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #1BD49B;
  -webkit-app-region: drag;
  height: 80px;

  .div-w-style {
    cursor: pointer;
    padding-right: 32px;
    display: flex;
    align-items: center;

    .img-size {
      width: 28px;
      height: 26px;
      margin-right: 5px;
    }

    .tx-font-size {
      font-size: 18px;
      font-weight: 600;
      color: white;
    }
  }
}

.container {
  display: flex;
  flex: 1;
  .sidebar {
    width: 200px;
    background: #eee;
  }
  .content {
    flex: 1;
    padding: 16px;
  }
}
</style>
