@import './transition.scss';

html,
body,
#app {
  width: 100%;
  height: 100%;
}

body {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
  margin: 0;
  padding: 0;
}

html {
  box-sizing: border-box;

  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    background-color: none;
  }

  ::-webkit-scrollbar-track {
    background-color: none;
  }

  ::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background-color: #555;
  }
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

div:focus {
  outline: none;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

//main-container全局样式
.app-main {
  min-height: 100%
}

.app-container {
  padding: 20px;
}

// 这里是一些常用的公用样式
@for $i from 1 to 100 {
  .mt-#{$i} {
    margin-top: #{$i}px;
  }

  .mr-#{$i} {
    margin-right: #{$i}px;
  }

  .mb-#{$i} {
    margin-bottom: #{$i}px;
  }

  .ml-#{$i} {
    margin-left: #{$i}px;
  }

  .pt-#{$i} {
    padding-top: #{$i}px;
  }

  .pr-#{$i} {
    padding-right: #{$i}px;
  }

  .pb-#{$i} {
    padding-bottom: #{$i}px;
  }

  .pl-#{$i} {
    padding-left: #{$i}px;
  }

  .font-#{$i} {
    font-size: #{$i}px
  }
}


.printer {
  border-radius: 16px !important;
  padding: 40px;

  .el-dialog__header {
    padding: 0;
    margin-right: 0;
    margin-bottom: 16px;

    .el-dialog__title {
      color: var(---el-text-color-primary, #1C2026);
      font-family: "PingFang SC";
      font-size: 24px;
      font-style: normal;
      font-weight: 300;
      line-height: 32px;
    }


  }

  .el-dialog__body {
    padding: 0;
  }

  .el-dialog__footer {
    margin-top: 24px;
    padding: 0;
  }
}