
import request from '@renderer/utils/request'

/**
 * 筛选条件
 * https://app.apifox.com/project/3068489
 */
export function queryGoodsList(shopId) {
  return request({
    url: `/fresh-wms/posSort/queryGoodsList/${shopId}`,
    method: 'get',
    params: {}
  })
}


/**
 * 展示列表分页查询
 * https://app.apifox.com/project/3068489
 */
export function posSortQueryGoodsList(data) {
  return request({
    url: `/fresh-wms/posSort/queryGoodsList`,
    method: 'post',
    data
  })
}


/**
 * 商品分类列表查询
 * https://app.apifox.com/project/3068489
 */
export function categoryList(data) {
  return request({
    url: `/fresh-wms/posSort/categoryList`,
    method: 'post',
    data
  })
}

/**
 * 账号店铺
 * https://app.apifox.com/project/3068489
 */
export function shopinfoPage(data) {
  return request({
    url: `/fresh-mall-admin/shopinfo/page`,
    method: 'get',
    params: data
  })
}

/**
 * 一键打印/分拣
 * https://app.apifox.com/project/3068489
 */
export function posSortBatchPrintSort(data) {
  return request({
    url: `/fresh-wms/posSort/batchPrintSort`,
    method: 'post',
    data: data
  })
}

/**
 *  判断当天是否存在未排线
 *  https://app.apifox.com/project/3068489
 */
export function sortScheduleByDate(data) {
  return request({
    url: `/fresh-wms/sort/scheduleByDate`,
    method: 'post',
    data: data
  })
}

export const fetchCustomerList = (data) => request({
  url: `/fresh-wms/sort/cusSortList`,
  method: 'post',
  data: data
})

export const fetchLineList = (data) => request({
  url: `/fresh-wms/sort/lineSortList`,
  method: 'post',
  data: data
})

export const fetchNewLineList = (data) => request({
  url: `/fresh-wms/posSort/lineSortList`,
  method: 'post',
  data: data
})

export const fetchNewCustomerList = (data) => request({
  url: `/fresh-wms/posSort/cusSortList`,
  method: 'post',
  data: data
})

export function upload(data) {
  return request({
    url: `/admin/file/upload-v2?dir=file&fileType=*`,
    method: 'post',
    data,
    headers: { 'Content-Type': 'multipart/form-data' },
  });
}

export const fetchLackList = (data) => request({
  url: `/fresh-wms/posSort/lackSortList`,
  method: 'post',
  data: data
})
