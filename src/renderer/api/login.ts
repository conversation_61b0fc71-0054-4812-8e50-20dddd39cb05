// 仅示例
import request from '@renderer/utils/request'
import qs from 'qs'
const CLIENT_ID = 'pig:pig'
const basicAuth = `Basic ${window.btoa(CLIENT_ID)}`

export function login(username, password, randomStr) {
  const grant_type = 'password'
  const data = qs.stringify({ username, password })
  return request({
    url: '/auth/oauth/token',
    method: 'post',
    headers: {
      isToken: false,
      Authorization: basicAuth
    },
    params: { randomStr, grant_type },
    data
  })
}

export function logout(token) {
  return request({
    url: '/auth/token/logout',
    method: 'delete',
    params: { token }
  })
}

//称重商品详情
export function goodsDetailPage(data) {
  return request({
    url: '/fresh-wms/posSort/goodsDetailPage',
    method: 'post',
    data
  })
}

export function reSetSort(data) {
  return request({
    url: `/fresh-wms/sort/reset/` + data,
    method: 'get'
  })
}
// check版本
export function checkVersion() {
  return request({
    url: `/fresh-wms/posSort/softwareUpdate`,
    method: 'get'
  })
}
//一键分拣  Or  一键打印
export function batchPrintSort(data) {
  return request({
    url: '/fresh-wms/posSort/batchPrintSort',
    method: 'post',
    data
  })
}

// -------------------- item 编辑界面 -----------------------------------------

//分拣 or 打印
export function printSort(data) {
  return request({
    url: '/fresh-wms/posSort/printSort',
    method: 'post',
    data
  })
}

//标记缺货
export function sortLack(sorId) {
  return request({
    url: '/fresh-wms/posSort/lack/' + sorId,
    method: 'post'
  })
}

//标记缺货
export function sortLackv2(data) {
  return request({
    url: '/fresh-wms/posSort/handlerLack',
    method: 'post',
    data
  })
}

// 分拣确认
export function sortFinsh(data) {
  return request({
    url: '/fresh-wms/posSort/preSort',
    method: 'post',
    data
  })
}


// 查看spu Map 
export function fetchSpuMap(data) {
  return request({
    url: '/fresh-mall-admin/goodsspu/page',
    method: 'get',
    data,
    params: data
  })

  // return new Promise((resolve, reject) => {
  //   resolve({
  //     data: [{ spuId: '1', spuName: '苹果', unit: 'kg', url: '' }, { spuId: '1', spuName: '醋', unit: '瓶', url: '' }]
  //   })
  // })
}

