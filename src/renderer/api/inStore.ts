import request from '@renderer/utils/request'

/**
 * 筛选条件
 * https://app.apifox.com/project/3068489
 */
export function queryGoodsList(shopId) {
  return request({
    url: `/fresh-wms/posSort/queryGoodsList/${shopId}`,
    method: 'get',
    params: {}
  })
}

/**
 * 商品分类列表查询
 * https://yapi.ops.yunlizhi.cn/project/971/interface/api/123021
 */
export function categoryList(data) {
  return request({
    url: `/fresh-wms/posInSort/categoryList`,
    method: 'post',
    data
  })
}

/**
 * 入库列表分页查询
 * https://yapi.ops.yunlizhi.cn/project/971/interface/api/123028
 */
export function posInSortQueryGoodsList(data) {
  return request({
    url: `/fresh-wms/posInSort/queryGoodsList`,
    method: 'post',
    data
  })
}

/**
 * 查看商品详情
 * https://yapi.ops.yunlizhi.cn/project/971/interface/api/123029
 */
export function goodsDetailPage(data) {
  return request({
    url: `/fresh-wms/posInSort/goodsDetailPage`,
    method: 'post',
    data
  })
}

/**
 * 供应商列表查询
 * https://yapi.ops.yunlizhi.cn/project/971/interface/api/123036
 */
export function querySupplierList(data) {
  return request({
    url: `/fresh-wms/posInSort/supplierWarehouseInInfo`,
    method: 'post',
    data
  })
}

/**
 * 
 * 订单列表查询
 * https://yapi.ops.yunlizhi.cn/project/971/interface/api/123037
 */
export function queryOrderList(data) {
  return request({
    url: `/fresh-wms/posInSort/inOrderWarehouseInInfo`,
    method: 'post',
    data
  })
}

/**
 * 入库单详情查询
 * https://yapi.ops.yunlizhi.cn/project/971/interface/api/123029
 */
export function queryInOrderDetail(data) {
  return request({
    url: `/fresh-wms/posInSort/goodsDetailPage`,
    method: 'post',
    data
  })
}

/**
 * 确认入库
 * https://yapi.ops.yunlizhi.cn/project/971/interface/api/123030
 */
export function confirmWarehouseIn(data) {
  return request({
    url: `/fresh-wms/warehousein/confirm`,
    method: 'post',
    data
  })
}

/**
 * 取消入库
 * https://yapi.ops.yunlizhi.cn/project/971/interface/api/123031
 */
export function cancelWarehouseIn(data) {
  return request({
    url: `/fresh-wms/posInSort/backPoint`,
    method: 'post',
    data: data
  })
}

/**
 * 新增商品信息
 */
export function addGoodsSpuInfo(data) {
  return request({
    url: '/fresh-wms/posInSort/addGoodsSpuInfo',
    method: 'post',
    data
  })
}

/**
 * 识别入库
 */
export function identifyIn(data) {
  return request({
    url: '/fresh-wms/posInSort/identifyIn',
    method: 'post',
    data
  })
}




