import { defineStore } from "pinia";
import { shopinfoPage } from '../../api/sorting'
interface StateType {
  accessTokenT: string;
  tenantCode: string;
  userNameT: string;
  userPwdT: string;
  isSavePwdT: string;
  switchTenantId: string;
  shopList: Array<string>;
  printerName: string;
  printerObj: string;
  printerSize: string;
}


export const userInfoT = defineStore({
  id: "userInfoT",
  state: (): StateType => ({
    accessTokenT: localStorage.getItem("accessTokenT") || "",
    tenantCode: localStorage.getItem("tenantCode") || "",
    userNameT: localStorage.getItem("userNameT") || "",
    userPwdT: localStorage.getItem("userPwdT") || "",
    isSavePwdT: localStorage.getItem("isSavePwdT") || "",
    switchTenantId: localStorage.getItem("switchTenantId") || "",
    shopList: JSON.parse(localStorage.getItem("shopList")) || [],
    printerName: localStorage.getItem("printerName") || "",
    printerObj: localStorage.getItem("printerObj") || "",
    printerSize: localStorage.getItem("printerSize") || "",

  }),
  getters: {
    getAccessTokenT: (state): string => state.accessTokenT,
    getTenantCode: (state): string => state.tenantCode,
    getUserNameT: (state): string => state.userNameT,
    getUserPwdT: (state): string => state.userPwdT,
    getIsSavePwdT: (state): string => state.isSavePwdT,
    getSwitchTenantId: (state): string => state.switchTenantId,
    getShopList: (state): Array<string> => state.shopList,
    getPrinterName: (state): string => state.printerName,
    getPrinterObj: (state): string => state.printerObj,
    printerSize: (state): string => state.printerSize,
  },
  actions: {
    setAccessTokenT(data: string) {
      this.accessTokenT = data;
      localStorage.setItem("accessTokenT", data);
    },
    setTenantCode(data: string) {
      this.tenantCode = data;
      localStorage.setItem("tenantCode", data);
    },
    setUserNameT(data: string) {
      this.userNameT = data;
      localStorage.setItem("userNameT", data);
    },
    setUserPwdT(data: string) {
      this.userPwdT = data;
      localStorage.setItem("userPwdT", data);
    },
    setIsSavePwdT(data: string) {
      this.isSavePwdT = data;
      localStorage.setItem("isSavePwdT", data);
    },
    setSwitchTenantId(data: string) {
      this.switchTenantId = data;
      localStorage.setItem("switchTenantId", data);
    },
    setPrinterName(data: string) {
      this.printerName = data;
      localStorage.setItem("printerName", data);
    },
    setPerinterObj(data: string) {
      this.printerObj = data;
      localStorage.setItem("printerObj", data);
    },
    setPrinterSize(data: string) {
      this.printerSize = data;
      localStorage.setItem("printerSize", data);
    },
    async setShopList() {
      try {
        const res = await shopinfoPage({ size: 100 })
        this.shopList = res?.data?.data?.records || []
        console.log("setShopList", res);

        localStorage.setItem("shopList", JSON.stringify(res?.data?.data?.records || []));
      } catch (error) {
        localStorage.setItem("shopList", JSON.stringify([]));
      }
    }
  },
});
