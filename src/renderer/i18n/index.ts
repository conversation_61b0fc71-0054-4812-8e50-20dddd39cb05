import { createI18n } from "vue-i18n"

export function loadLanguages() {
    const context = import.meta.globEager("./languages/*.json");

    const languages: AnyObject = { zh: {}, en: {} };

    let langs = Object.keys(context);
    for (let key of langs) {
        let lang = context[key];
        Object.assign(languages.zh, lang.zh);
        Object.assign(languages.en, lang.en);
    }

    return languages
}

export function i18nt(key: string) {
    return i18n.global.d(key);
}

export const i18n = createI18n({
    legacy: false,
    locale: localStorage.getItem('locale') || 'zh-cn',
    fallbackLocale: 'zh-cn',
    messages: loadLanguages()
})

export function setLanguage(locale: string) {
    i18n.global.locale.value = locale === "zh-cn" ? "zh" : "en"
    localStorage.setItem('locale', locale);
    console.log("setLanguage(): locale = ", locale);
    console.log("setLanguage(): localStorage locale = ", localStorage.getItem('locale'));
    console.log("setLanguage(): i18n.global.locale.value = ", i18n.global.locale.value);
}