import axios, { AxiosRequestConfig } from "axios";
import { ElMessage } from "element-plus";
const serves = axios.create({
  baseURL:  __CONFIG__.API_HOST,
  timeout: 5000
});
// 设置请求发送之前的拦截器
serves.interceptors.request.use(
  (config) => {
    if(config.url == '/fresh-wms/posSort/softwareUpdate'){
      return config;
    }
    const token = localStorage.getItem("accessTokenT");
    const switchTenantId = localStorage.getItem("switchTenantId");
    const macAddress = localStorage.getItem("macAddress");
    
    if(config.url == '/auth/oauth/token'){
      // config.headers['Authorization'] =  `Basic YWRtaW46YWRtaW4=`;
      config.headers['isToken'] =  false;
    } else {
      if (token) {
         config.headers['Authorization'] = `Bearer ${token}`;
      }
      config.headers['switch-tenant-id'] = switchTenantId
      config.headers['TENANT-ID'] = switchTenantId
    }
    
    if (macAddress) {
      config.headers['LOCAL_MAC'] = macAddress;
    }
    
    return config;
  },
  (err) => Promise.reject(err)
);

// 设置请求接受拦截器
serves.interceptors.response.use(
  (res) => {
    // 设置接受数据之后，做什么处理
    if (res.data.code === 50000) {
      ElMessage.error(res.data.data);
    }
    return res;
  },
  (err) => {
    console.log(err, 'error');
    // 判断请求异常信息中是否含有超时timeout字符串
    if (err.message.includes("timeout")) {
      console.log("错误回调", err);
      ElMessage.error("网络超时");
    }
    if (err.message.includes("Network Error")) {
      console.log("错误回调", err);
      ElMessage.error("服务端未启动，或网络连接错误");
    }
    if(err.response.data.msg){
      ElMessage.error(err.response.data.msg);
      if (err.response.data.msg.includes("token 过期")) {
        window.location.href = "/login";
      }
    } else if (err.message) {
      ElMessage.error('请求接口异常：'+err.message);
    }
    return Promise.reject(err);
  }
);

export interface BaseDataStruct<T> {
  data: T;
}

const requestPlus = async <D = any, T = any>(
  params: AxiosRequestConfig<T>
): Promise<BaseDataStruct<D>> => {
  return await serves(params);
};

// 将serves抛出去
export default requestPlus;
