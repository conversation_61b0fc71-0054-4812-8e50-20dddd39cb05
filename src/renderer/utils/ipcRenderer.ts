import { Ipc<PERSON><PERSON>erInvoke, IpcRendererOn } from "../../ipc"
import { onUnmounted } from "vue"
const { ipc<PERSON><PERSON><PERSON> } = require("electron");

type VoidParametersIpcRendererInvokeKey = {
  [K in keyof IpcRendererInvoke]: Parameters<IpcRendererInvoke[K]>[0] extends void ? K : never
}[keyof IpcRendererInvoke]
type NotVoidParametersIpcRendererInvokeKey = Exclude<keyof IpcRendererInvoke, VoidParametersIpcRendererInvokeKey>


export function invoke<T extends VoidParametersIpcRendererInvokeKey>(channel: T): ReturnType<IpcRendererInvoke[T]>;
export function invoke<T extends NotVoidParametersIpcRendererInvokeKey>(channel: T, args: Parameters<IpcRendererInvoke[T]>[0]): ReturnType<IpcRendererInvoke[T]>;

export function invoke<T extends keyof IpcRendererInvoke>(channel: T, args?: Parameters<IpcRendererInvoke[T]>[0]) {
  return ipcRenderer.invoke(channel, args) as ReturnType<IpcRendererInvoke[T]>
}



/**
 * ipcRenderon Vue setup中使用
 *
 * @export
 * @template T
 * @param {T} channel
 * @param {IpcRendererOn[T]} callback
 */
export function vueListen<T extends keyof IpcRendererOn>(channel: T, callback: IpcRendererOn[T]) {
  ipcRenderer.on(channel, callback)
  onUnmounted(() => {
    ipcRenderer.removeListener(channel, callback);
  })
}

/**
 * ipcRenderon
 * 
 * @export
 * @template T
 * @param {T} channel
 * @param {IpcRendererOn[T]} callback
 * @return {() => void} 副作用清理函数
 */
export function listen<T extends keyof IpcRendererOn>(channel: T, callback: IpcRendererOn[T]): () => void {
  ipcRenderer.on(channel, callback)
  return () => {
    ipcRenderer.removeListener(channel, callback);
  }
}



/**
 * 连接串口
 * @param path 
 */
export async function connect(path) {
  await ipcRenderer.invoke('serial-connect', { path });
}

/**
 * 关闭串口
 */
export async function serialClose() {
  await ipcRenderer.invoke('serial-close');
}

/**
 * 初始化启动识别AI服务
 */
export async function startInitAi() {
  await ipcRenderer.invoke('start-ai-service');
}

/**
 * AI识别
 */
export async function recongnize(params) {
  await ipcRenderer.invoke('recongnize', params);
}

/**
 * AI识别反馈
 */
export async function recognizeConfirm(code, name) {
  await ipcRenderer.invoke('confirm', code, name);
}

/**
 * 显示AI设置
 */
export async function showSetting() {
  await ipcRenderer.invoke('show-setting');
}


/**
 * 读取识别编码JSON
 */
export async function readSkuFIle() {
  return await ipcRenderer.invoke('read-file');
}




/**
 * 清除学习数据
 */
export async function clearnLearnData() {
  await ipcRenderer.invoke('clean-learn-data');
}
