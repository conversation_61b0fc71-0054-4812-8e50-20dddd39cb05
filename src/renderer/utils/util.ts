const { exec } = require('child_process')
const os = require('os')
import * as CryptoJS from 'crypto-js'


export const OpenVirtualKeyBoard = ()=>{  // 唤起软键盘
 if (os.platform() === 'win32') {
    exec('osk.exe') // window
  } if(os.platform()=== 'darwin'){
    exec('open -a KeyboardViewer') // mac
  } else {
    exec('onboard') // Linux 
  }
}


export const closeKeyBoard = ()=>{ // 关闭软键盘
  if (os.platform() === 'win32') {
    exec('taskkill /IM osk.exe /F', (error, stdout, stderr) => {
      if (error) {
        console.error(`错误: ${error.message}`);
        return;
      }
    });
  } if(os.platform()=== 'darwin'){
      exec('killall KeyboardViewer', (error, stdout, stderr) => {
      if (error) {
        console.error(`错误: ${error.message}`);
        return;
      }
    });
  } else {
     exec('pkill onboard', (error, stdout, stderr) => {
      if (error) {
        console.error(`错误: ${error.message}`);
        return;
      }
    });
  }
}

/**
 *加密处理
 */
 export const encryption = (params) => {
  let { data, type, param, key } = params
  const result = JSON.parse(JSON.stringify(data))
  if (type === 'Base64') {
    param.forEach((ele) => {
      result[ele] = btoa(result[ele])
    })
  } else {
    param.forEach((ele) => {
      var data = result[ele]
      key = CryptoJS.enc.Latin1.parse(key)
      var iv = key
      // 加密
      var encrypted = CryptoJS.AES.encrypt(data, key, {
        iv: iv,
        mode: CryptoJS.mode.CFB,
        padding: CryptoJS.pad.NoPadding
      })
      result[ele] = encrypted.toString()
    })
  }
  return result
}