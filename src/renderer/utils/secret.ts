  /**
   * AES加密 ：字符串 key iv  返回base64
   */
  const param = ["password"]
  const key = '1234567891234567'
  import CryptoJS from 'crypto-js/crypto-js';
  export function Encrypt(word) {
    let result = word
    param.forEach((ele) => {
      let sconedKey = CryptoJS.enc.Latin1.parse(key)
      var iv = sconedKey
      var encrypted = CryptoJS.AES.encrypt(result, sconedKey, {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.ZeroPadding,
      });
      result = encrypted.toString()
    })
    return result;
  }
   
  /**
   * AES 解密 ：字符串 key iv  返回base64
   *  */
  export function Decrypt(word) {
    const base64 = CryptoJS.enc.Base64.parse(word);
    const src = CryptoJS.enc.Base64.stringify(base64);
  
    const decrypt = CryptoJS.AES.decrypt(src, key, {
      iv: iv,
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7,
    });
    return CryptoJS.enc.Utf8.stringify(decrypt);
  }