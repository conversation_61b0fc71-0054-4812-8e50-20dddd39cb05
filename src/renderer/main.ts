import { createApp } from 'vue'
import { createPinia } from 'pinia'

import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css'
import 'dayjs/locale/zh-cn'
import locale from 'element-plus/dist/locale/zh-cn'
import './styles/index.scss'
import './permission'
import App from './App.vue'
import router from './router'
import { errorHandler } from './error'
import { i18n } from "./i18n"
import TitleBar from "./components/common/TitleBar.vue"
import SvgIcon from './components/common/SvgIcon.vue'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import AppVue from './App.vue';
const app = createApp(App)
const store = createPinia()
app.use(ElementPlus, { i18n: i18n.global.d, locale })
// app._context.components.ElDrawer.props.closeOnClickModal.default = false

for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
}
app.use(router)
app.use(store)
app.use(i18n)
errorHandler(app)
// 全局引入 TitleBar 组件
app.component("TitleBar", TitleBar);
app.component("SvgIcon", SvgIcon)
app.mount("#app")
process.env['ELECTRON_DISABLE_SECURITY_WARNINGS'] = 'true'

