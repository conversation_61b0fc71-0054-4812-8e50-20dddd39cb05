<template>
  <div class="goods" @click="handleSelectGoods">
    <div
      class="process-header"
      :class="{
      unsort: (data.sortedNum || 0) === 0,
      sorting: (data.sortedNum || 0) > 0,
      sorted: (data.sortedNum || 0) === (data.totalNum || 0),
      }"
    >
      <div>分拣进度</div>
      <div>{{ data.sortedNum || 0 }}/{{ data.totalNum || 0 }}</div>
    </div>
    <div class="adapt">
      <img :src="data.imgUrl" alt="" class="pic" loading="lazy" />
      <div class="stock">库存 {{data.showAvailableStock}}</div>
    </div>
    <div class="footer">
      <div class="title">{{ data.spuName || "--" }}</div>
      <div class="btns-wraper">
        <el-button @click.stop="handleSelectGoods">查看订单</el-button>
        <el-button type="primary" @click.stop="handleSorting" class="ml">开始分拣</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { toRefs } from "vue";

const props = defineProps({
  data: {
    type: Object,
    default: () => {},
  },
  userAddressId: {
    type: String,
    default: "",
  },
  lineId: {
    type: String,
    default: "",
  },
});

const emits = defineEmits(["select", "handleSorting"]);
const { data } = toRefs(props);

// 使用事件修饰符.stop阻止事件冒泡，避免触发父组件的点击事件
const handleSorting = (event) => {
  emits("handleSorting", {
    spuId: data.value.spuId,
    showAvailableStock: data.value.showAvailableStock,
    userAddressId: props.userAddressId,
    lineId: props.lineId,
    spuName: data.value.spuName,
    specInfos: data.value.specInfos,
    categoryCode: data.value.firstCategoryCode,
    imgUrl: data.value.imgUrl,
  });
};

function handleSelectGoods() {
  // 选择商品
  emits("select", {
    spuId: data.value.spuId,
    showAvailableStock: data.value.showAvailableStock,
    userAddressId: props.userAddressId,
    lineId: props.lineId,
    spuName: data.value.spuName,
    specInfos: data.value.specInfos,
    categoryCode: data.value.firstCategoryCode,
    imgUrl: data.value.imgUrl,
  });
}
</script>

<style lang="scss" scoped>
.goods {
  cursor: pointer;
  width: 248px;
  display: flex;
  flex-direction: column;
  border-top: 1px solid var(---el-border-color-light, #e6eaf0);
  background: #fff;
  border-radius: 16px;
  will-change: transform; // 提示浏览器这个元素会变化
  transform: translateZ(0); // 强制GPU加速
  contain: content; // 告诉浏览器这个元素和它的内容是独立的

  .process-header {
    width: 100%;
    height: 32px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: var(---el-color-white, #fff);
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;

    &.unsort {
      background: #db4646;
    }

    &.sorting {
      background: #2c85d7;
    }

    &.sorted {
      background: #059e84;
    }
  }
  &:focus {
    background: rgba(51, 51, 51, 0.1);
  }
  .adapt {
    // 图像比例4:3
    width: 100%;
    height: 186px; // 使用固定高度替代百分比padding
    background: pink;
    position: relative;
    overflow: hidden; // 防止内容溢出

    .pic {
      width: 100%;
      height: 100%;
      object-fit: cover;
      will-change: transform; // 提示浏览器这个元素会变化
      transform: translateZ(0); // 强制GPU加速
    }

    .stock {
      display: inline-flex;
      padding: 2px 8px;
      flex-direction: column;
      align-items: center;
      border-radius: 22px 0px 0px 22px;
      background: var(---el-color-warning-light-9, #ffe4ba);
      backdrop-filter: blur(2px);
      position: absolute;
      right: 0;
      bottom: 8px;
      z-index: 1; // 确保在图片上层
    }
  }

  .footer {
    padding: 12px 16px;
    display: flex;
    flex-direction: column;
    gap: 12px;

    .title {
      overflow: hidden;
      color: var(---el-text-color-primary, #1c2026);
      text-overflow: ellipsis;
      font-family: "PingFang SC";
      height: 52px;
      font-size: 18px;
      font-style: normal;
      font-weight: 400;
      line-height: 26px;
    }

    .btns-wraper {
      display: flex;
      align-items: center;
      flex: 1;

      .el-button:focus,
      .el-button:hover {
        color: #059e84;
        border-color: #059E84;
        background-color: #e8fff7;
      }

      .el-button {
        min-width: 104px;
        min-height: 48px;
        text-align: center;
        padding: 12px 20px;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 4px;
        color: #059E84;
        border-radius: 8px;
        border: 1.5px solid var(---el-color-primary, #059E84);
        font-family: "PingFang SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px;
        margin: 0px;
        display: flex;
        flex: 1;
      }

      .ml {
        margin-left: 4px;
      }

      .el-button--primary {
        background: #059e84;
        color: #fff;
      }
      .el-button--primary:hover {
        background: rgb(55, 177, 157);
        color: #fff;
        border-color: #059E84;
      }
    }

    .btn-wrapper {
      padding: 0 12px 12px;
    }

    .btn {
      display: flex;
      padding: 4px 16px;
      justify-content: center;
      align-items: center;
      gap: 4px;
      align-self: stretch;
      border-radius: 24px;
      color: #fff;
      font-family: PingFang SC;
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px;
      letter-spacing: -0.01px;

      .svg-icon {
        font-size: 18px;
      }

      &-success {
        background: #00b42a;
      }

      &-warning {
        background: #ff7d00;
      }

      &-error {
        background: #f53f3f;
      }
    }
  }
}
</style>
