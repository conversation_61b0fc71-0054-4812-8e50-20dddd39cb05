<template>
  <el-dialog
    v-model="isDialogVisible"
    :title="$t('components.PrinterDialogSelect.289542-0')"
    width="40%"
    center
    class="printer"
    :close-on-click-modal="false"
    :show-close="false"
  >
    <div style="text-align: center">
      <span class="div-print-title">{{ $t('components.PrinterDialogSelect.289542-1') }}</span>
      <span class="div-print-title2">{{ printerName }}</span>
      <span class="div-print-title2" v-if="!printerName"
        >{{ $t('components.PrinterDialogSelect.289542-2') }}</span
      >
      <div style="padding: 24px 0">
        <el-select
          v-model="printerName"
          class="m-2"
          :placeholder="$t('components.PrinterDialogSelect.289542-3')"
          size="large"
          style="width: 340px"
        >
          <el-option
            v-for="(item, index) in printerList"
            :key="index"
            :value="item.name"
            >{{ item.displayName }}</el-option
          >
        </el-select>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button
          class="normal"
          @click="cancelSelect"
          style="width: 136px; height: 56px"
          >{{ $t('components.PrinterDialogSelect.289542-4') }}</el-button
        >
        <el-button
          class="high-ligth-green"
          @click="confirmSelect"
          style="width: 136px; height: 56px"
        >
          {{ $t('components.PrinterDialogSelect.289542-5') }}
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, toRefs, onMounted, nextTick } from "vue";
import { ElMessage } from "element-plus";
import { userInfoT } from "@renderer/store/modules/template";
import { storeToRefs } from "pinia";
import { invoke } from "../utils/ipcRenderer";
import { IpcChannel } from "../../ipc";
import { i18n } from "@renderer/i18n";

const props = defineProps({
  printerList: {
    type: Array,
    default: () => [],
  },
});
const isDialogVisible = ref(false);
const useUserStore = userInfoT();
const { printerName } = storeToRefs(useUserStore);
const emits = defineEmits(["confirm", "shopChange"]);

function cancelSelect() {
  isDialogVisible.value = false;
  printerName.value = "";
}
function confirmSelect() {
  isDialogVisible.value = false;
  if (!printerName.value) {
    ElMessage.error(i18n.global.t('components.PrinterDialogSelect.289542-3'));
    return;
  }
  isDialogVisible.value = false;
  useUserStore.setPrinterName(printerName.value);
}

// 开启打印
async function sendPrintStart(val) {
  nextTick(() => {
    useUserStore.setPerinterObj(JSON.stringify(val));
    invoke(IpcChannel.OpenWin, { url: "/PrintPage" });
  });
}

function show() {
  isDialogVisible.value = true;
}

defineExpose({
  show,
  sendPrintStart,
});
</script>

<style lang="scss" scoped>
::v-deep(.el-button) {
  display: inline-flex;
  box-sizing: border-box;
  height: auto;
  line-height: 22px;
  padding: 14px 20px;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  border-radius: 12px;
  border: none;
  color: var(---el-text-color-primary, #1c2026);
  font-family: "PingFang SC";
  font-size: 24px;
  font-style: normal;
  font-weight: 400;
  line-height: 32px;

  & + .el-button {
    margin-left: 0;
  }

  & > span {
    gap: 4px;
  }
}

::v-deep(.el-button.normal) {
  border: 1px solid #b0b7c2;
  background: #fff;
  padding: 12px 20px;
  color: #505762;
  font-size: 24px;
  font-style: normal;
  font-weight: 400;
  line-height: 32px;
  border: 1px solid var(---el-text-color-placeholder, #b0b7c2);
  background: linear-gradient(180deg, #fff 0%, #bbc1c3 100%);
}

::v-deep(.el-button.high-ligth-green) {
  border: 1px solid #6dd8b1;
  background: linear-gradient(180deg, #b5ffdb 28.57%, #00af70 125%);
}
.dialog-footer {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 24px;
  .el-button:focus,
  .el-button:hover {
    color: #059e84;
    border-color: #79d8bf;
    background-color: #e8fff7;
  }
  .el-button--primary {
    background: #009f64;
    color: #fff;
    border-color: #009f64;
  }
  .el-button--primary:hover {
    background: rgb(55, 177, 157);
    color: #fff;
    border-color: rgb(55, 177, 157);
  }
}

.div-print-title {
  color: #1c2026;
  font-family: PingFang SC;
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
  line-height: 32px; /* 133.333% */
}
.div-print-title2 {
  color: red;
  font-family: PingFang SC;
  font-size: 24px;
  font-style: normal;
  font-weight: 600;
}
::v-deep .m-2 {
  .el-date-editor.el-input,
  .el-date-editor.el-input__wrapper,
  .el-select {
    width: 100% !important;
  }
  .el-input__wrapper.is-focus,
  .el-input.is-focus .el-input__wrapper {
    box-shadow: 0 0 0 1px #009f64 !important;
  }

  .el-input__inner {
    height: 50px;
    &::placeholder {
      color: var(---el-text-color-secondary, #7e8694);
      font-size: 18px;
      line-height: 50px !important;
    }
  }
}
</style>
