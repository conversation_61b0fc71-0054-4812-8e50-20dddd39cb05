<template>
  <title-bar />
  <div>
    <div class="icon-logo" >
      <img src="@renderer/assets/img/logo.png" style="margin-top: 20px;"/>
    </div>
    <div class="left-side">
      <el-input v-model="userName" clearable  style="width: 240px" placeholder="请输入用户名" />
    </div>
    <div class="left-side">
      <el-input v-model="userPwd"  clearable show-password  style="width: 240px" placeholder="请输入密码" />
    </div>
    <div class="tex-pwd-stlye">忘记密码</div>
    <div class="right-side">
      <el-button color="#1BD49B" round @click="sendLogin()" style="width: 140px;margin-right: 10px;">
        <span style="color: white;"> {{ t("loginTxt.loginCancel") }}</span>
      </el-button>
      <el-button color="#1BD49B" round @click="CheckUpdate('one')" style="width: 140px;margin-left: 10px;">
        <span style="color: white;"> {{ t("loginTxt.loginCommit") }}</span>
      </el-button>
    </div>
    <label style="font-size: 11px;color: #B4B6BD;display: flex;justify-content: center;padding-top: 30px;">由四川唯鲜生活供应链管理有限公司提供技术支持</label>
  </div>
</template>

<script setup lang="ts">
import { message } from "@renderer/api/login";
import TitleBar from "../components/common/TitleBar.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { onUnmounted, Ref, ref } from "vue";
import { i18n, setLanguage } from "@renderer/i18n";
import { useI18n } from "vue-i18n";
import { invoke, vueListen } from "../utils/ipcRenderer";
import { IpcChannel } from "../../ipc";
import { useTemplateStore } from "@renderer/store/modules/template";
import { ProgressInfo } from "electron-updater";
const storeTemplate = useTemplateStore();
const { t } = useI18n();
setTimeout(() => {
  storeTemplate.TEST_ACTION("654321");
  console.log(`storeTemplate`, storeTemplate.getTest1);
}, 1000);

const { ipcRenderer, shell } = require("electron");

let percentage = ref(0);
let colors: Ref<ColorInfo[]> | Ref<string> = ref([
  { color: "#f56c6c", percentage: 20 },
  { color: "#e6a23c", percentage: 40 },
  { color: "#6f7ad3", percentage: 60 },
  { color: "#1989fa", percentage: 80 },
  { color: "#5cb87a", percentage: 100 },
]);

let dialogVisible = ref(false);
let progressStaus = ref(null);
let showForcedUpdate = ref(false);
let filePath = ref("");
let updateStatus = ref("");

let userName = ref("");
let userPwd = ref("");

let elPageSize = ref(100);
let elCPage = ref(1);

invoke(IpcChannel.GetStaticPath).then((res) => {
  console.log("staticPath", res);
})


function changeLanguage() {
  setLanguage(i18n.global.locale.value === "zh-cn" ? "en" : "zh");
}

function printDemo() {
  invoke(IpcChannel.OpenPrintDemoWindow);
}

function browserDemo() {
  invoke(IpcChannel.OpenBrowserDemoWindow);
}

function handleSizeChange(val: number) {
  elPageSize.value = val;
}

function handleCurrentChange(val: number) {
  elCPage.value = val;
}

function crash() {
  process.crash();
}
//登录
function sendLogin() {
  // message().then((res) => {
  //   ElMessageBox.alert(res.data, "提示", {
  //     confirmButtonText: "确定",
  //   });
  // });

  //关闭当前窗体
  if(!userName.value){
    alert(111);
    ElMessage.error('用户名不能为空')
    return;
  }
  if(!userPwd.value){
    ElMessage.error('密码不能为空')
    return;
  }
  invoke(IpcChannel.WindowClose);
  let data = {
    url: "/form/index",
  };
  //打开新窗体
  invoke(IpcChannel.OpenWin, data);
}
function StopServer() {
  invoke(IpcChannel.StopServer).then((res) => {
    if (res) {
      ElMessage({
        type: "success",
        message: "已关闭",
      });
    }
  });
}
function StartServer() {
  invoke(IpcChannel.StartServer).then((res) => {
    if (res) {
      ElMessage({
        type: "success",
        message: res,
      });
    }
  });
}
// 获取electron方法
function open() { }
function CheckUpdate(data) {
  switch (data) {
    case "one":
      invoke(IpcChannel.CheckUpdate);
      break;
    case "two":
      // TODO 测试链接
      console.log('test Url')
      break;
    case "three":
      invoke(IpcChannel.HotUpdate);
      break;
    case "threetest":
      alert("更新后再次点击没有提示");
      invoke(IpcChannel.HotUpdateTest);
      break;
    case "four":
      showForcedUpdate.value = true;
      break;
    default:
      break;
  }
}
function openPreloadWindow() {
  ElMessageBox.alert(t("home.openPreloadWindowError.content"), t("home.openPreloadWindowError.title"), {
    confirmButtonText: t("home.openPreloadWindowError.confirm"),
    callback: (action) => { },
  });
}

function handleClose() {
  dialogVisible.value = false;
}

const showInMyComputer = ref(0) // 0-不显示 1-开启 -1-关闭
if (process.platform === 'win32') {
  invoke(IpcChannel.CheckShowOnMyComputer).then(bool => {
    showInMyComputer.value = bool ? 1 : -1
  })
}
function setShowOnMyComputer() {
  invoke(IpcChannel.SetShowOnMyComputer, showInMyComputer.value === -1).then(success => {
    if (success) {
      showInMyComputer.value = showInMyComputer.value === -1 ? 1 : -1
    }
  })
}

vueListen(IpcChannel.DownloadProgress,(event, arg) => {
  console.log(arg);
  percentage.value = arg;
});


vueListen(IpcChannel.DownloadError, (event, arg) => {
  if (arg) {
    progressStaus = "exception";
    percentage.value = 40;
    colors.value = "#d81e06";
  }
});
vueListen(IpcChannel.DownloadPaused, (event, arg) => {
  if (arg) {
    progressStaus = "warning";
    ElMessageBox.alert("下载由于未知原因被中断！", "提示", {
      confirmButtonText: "重试",
      callback: (action) => {
        invoke(IpcChannel.StartDownload, "");
      },
    });
  }
});
vueListen(IpcChannel.DownloadDone, (event, age) => {
  filePath.value = age.filePath;
  progressStaus = "success";
  ElMessageBox.alert("更新下载完成！", "提示", {
    confirmButtonText: "确定",
    callback: (action) => {
      shell.openPath(filePath.value);
    },
  });
});
// electron-updater的更新监听
vueListen(IpcChannel.UpdateMsg, (event, args) => {
  switch (args.state) {
    case -1:
      const msgdata = {
        title: "发生错误",
        message: args.msg as string,
      };
      dialogVisible.value = false;
      invoke(IpcChannel.OpenErrorbox, msgdata);
      break;
    case 0:
      ElMessage("正在检查更新");
      break;
    case 1:
      ElMessage({
        type: "success",
        message: "已检查到新版本，开始下载",
      });
      dialogVisible.value = true;
      break;
    case 2:
      ElMessage({ type: "success", message: "无新版本" });
      break;
    case 3:
      percentage.value = Number((args.msg as ProgressInfo).percent.toFixed(1));
      break;
    case 4:
      progressStaus = "success";
      ElMessageBox.alert("更新下载完成！", "提示", {
        confirmButtonText: "确定",
        callback: (action) => {
          invoke(IpcChannel.ConfirmUpdate);
        },
      });
      break;
    default:
      break;
  }
});
vueListen(IpcChannel.HotUpdateStatus, (event, msg) => {
  switch (msg.status) {
    case "downloading":
      ElMessage("正在下载");
      break;
    case "moving":
      ElMessage("正在移动文件");
      break;
    case "finished":
      ElMessage.success("成功,请重启");
      break;
    case "failed":
      ElMessage.error(msg.message);
      break;
    default:
      break;
  }
  console.log(msg);
  updateStatus.value = msg.status;
});

</script>

<style>
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: "Source Sans Pro", sans-serif;
}

#logo {
  height: auto;
  margin-bottom: 20px;
  width: 120px;
}

.icon-logo {
  display: flex;
  justify-content: center;
}


.left-side {
  display: flex;
  justify-content: center;
  color: #2c3e50;
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 20px;
}

.right-side {
  display: flex;
  justify-content: center;
  padding-top: 15px;
}

.welcome {
  color: #555;
  font-size: 23px;
  margin-bottom: 10px;
}

.title {
  color: #2c3e50;
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 6px;
}

.title.alt {
  font-size: 18px;
  margin-bottom: 10px;
}

.doc {
  margin-bottom: 10px;
}

.doc p {
  color: black;
  margin-bottom: 10px;
}

.doc .el-button {
  margin-top: 10px;
  margin-right: 10px;
}

.doc .el-button+.el-button {
  margin-left: 0;
}

.conten {
  text-align: center;
}
.tex-pwd-stlye{
  display: flex;
  justify-content: flex-end;
  font-size: 12px;color:#B4B6BD;
  margin-right: 90px;
  &:hover {
    cursor: pointer;
    color: #3C3D43;
  }
}
</style>
