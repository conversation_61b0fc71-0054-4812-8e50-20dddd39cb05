<template>
  <div>
     <div v-show="showKeyboard" :class="keyboardClass"></div>
  </div>
</template>

<script setup>
import {ref,onMounted,toRefs,nextTick,reactive} from 'vue'
import Keyboard from "simple-keyboard";
import "simple-keyboard/build/css/index.css";
import layout from 'simple-keyboard-layouts/build/layouts/chinese' // 中文输入法
const displayDefault = reactive({
  '{bksp}': '删除',
  '{lock}': 'caps',
  '{enter}': 'enter',
  '{tab}': 'tab',
  '{shift}': 'shift',
  '{change}': '中文',
  '{space}': ' ',
  '{clear}': '清空',
  '{close}': '关闭'
})
const props = defineProps({
  keyboardClass: {
    default: "simple-keyboard",
    type: String
  },
  input: {
    type: String
  },
  maxLength:{
    default: '',
  }
})


const keyboard = ref(null)
const showKeyboard = ref(false)
const {input} = toRefs(props)
const emits = defineEmits(['onChange','onKeyPress','onClose'])

function onKeyPress(button, $event) {
  // 点击关闭
  if (button === '{close}') {
    showKeyboard.value = false
    // let keyboard = $event.path[3]
    // keyboard.style.visibility = 'hidden'
    emits('onClose', '')
    return false
  } else if (button === '{change}') {
    // 切换中英文输入法
    if (keyboard.value.options.layoutCandidates !== null) {
      displayDefault['{change}'] = '中文'
      // 切换至英文
      keyboard.value.setOptions({
        layoutCandidates: null,
        display: displayDefault
      })
    } else {
      // 切换至中文
      displayDefault['{change}'] = '英文'
      keyboard.value.setOptions({
        layoutCandidates: layout.layoutCandidates,
        display: displayDefault
      })
    }
  } else if (button === '{clear}') {
    keyboard.value.clearInput()
  } else if (button === '{enter}') {
    showKeyboard.value = false
  } else {
    let value = $event.target.offsetParent.parentElement.children[0].children[0].value
    // 输入框有默认值时，覆写
    if (value) {
      keyboard.value.setInput(value)
    }
    emits('onKeyPress', button)
  }
  if (button === '{shift}' || button === '{lock}') handleShift()
}

const handleShift = () => {
  let currentLayout = keyboard.value.options.layoutName;
  console.log(currentLayout,'currentLayout');
  let shiftToggle = currentLayout === "default" ? "shift" : "default";
  keyboard.value.setOptions({
    layoutName: shiftToggle
  });
}

const onChange = (input) => {
  emits("onChange", input);
}
const onClose = (input) => {
  emits("onClose", input);
}
const open = () => {
  showKeyboard.value = true
  console.log(keyboard.value,'keyboard.valuekeyboard.value');
  nextTick(()=>{
    keyboard.value.setInput(input.value);
  })
}

const close = () => {
  showKeyboard.value = false
}

onMounted(()=>{
  keyboard.value = new Keyboard(props.keyboardClass, {
    onChange: onChange,
    onClose: onClose,
    onKeyPress: onKeyPress,
    layoutCandidates: null,
    layout: {
      // 默认布局
      default: [
        '` 1 2 3 4 5 6 7 8 9 0 - = {bksp}',
        '{tab} q w e r t y u i o p [ ] \\',
        "{lock} a s d f g h j k l ; ' {enter}",
        '{shift} z x c v b n m , . / {clear}',
        '{change} {space} {close}'
      ],
      // shift布局
      shift: [
        '~ ! @ # $ % ^ & * ( ) _ + {bksp}',
        '{tab} Q W E R T Y U I O P { } |',
        '{lock} A S D F G H J K L : " {enter}',
        '{shift} Z X C V B N M < > ? {clear}',
        '{change} {space} {close}'
      ]
    },
    // 按钮展示文字
    display: displayDefault,
    // 按钮样式
    buttonTheme: [
      {
        class: 'hg-red close',
        buttons: '{close}'
      },
      {
        class: 'change',
        buttons: '{change}'
      }
    ],
    // 输入限制长度
    maxLength: props.maxLength
  })
})

defineExpose({
  open,
  close
})
</script>

<style scoped>
</style>
