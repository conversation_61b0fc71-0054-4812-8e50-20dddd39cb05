{"name": "electron-vite-template", "version": "1.0.3", "main": "./dist/electron/main/main.js", "author": "sky <https://github.com/umbrella22>", "description": "electron-vite-template project", "license": "MIT", "encryptionLevel": 0, "scripts": {"dev": "esno .electron-vite/dev-runner.ts", "build": "cross-env BUILD_TARGET=clean esno .electron-vite/build.ts -m prod && electron-builder -c build.json", "build:qa": "cross-env BUILD_TARGET=clean esno .electron-vite/build.ts -m sit && electron-builder -c build.json", "build:win32": "cross-env BUILD_TARGET=clean esno .electron-vite/build.ts  && electron-builder -c build.json --win  --ia32", "build:win64": "cross-env BUILD_TARGET=clean esno .electron-vite/build.ts -m sit && electron-builder -c build.json --win  --x64", "build:mac": "cross-env BUILD_TARGET=clean esno .electron-vite/build.ts  && electron-builder -c build.json --mac", "build:dir": "cross-env BUILD_TARGET=clean esno .electron-vite/build.ts  && electron-builder -c build.json --dir", "build:clean": "cross-env BUILD_TARGET=onlyClean esno .electron-vite/build.ts", "build:web": "cross-env BUILD_TARGET=web esno .electron-vite/build.ts", "pack:resources": "esno .electron-vite/hot-updater.ts", "pack:rustUpdater": "electron_updater_node_cli -p -c updateConfig.json", "dep:upgrade": "yarn upgrade-interactive --latest", "preinstall": "node .electron-vite/checkMirrorConfig.js", "postinstall": "electron-builder install-app-deps"}, "dependencies": {"@serialport/parser-readline": "^13.0.0", "@types/crypto-js": "^4.1.3", "axios": "^1.4.0", "crypto-js": "^4.2.0", "easy-auto-launch": "^6.0.2", "electron-log": "^4.4.8", "electron-updater": "^6.1.1", "express": "^4.18.2", "glob": "^10.3.3", "qs": "^6.11.2", "regedit": "^5.1.2", "openai": "^4.85.4", "semver": "^7.5.4", "serialport": "^13.0.0", "simple-keyboard": "^3.7.28", "simple-keyboard-layouts": "^3.3.37", "uuid": "^9.0.0", "yarn": "^1.22.19"}, "devDependencies": {"@rollup/plugin-alias": "^5.0.0", "@rollup/plugin-commonjs": "^25.0.3", "@rollup/plugin-json": "^6.0.0", "@rollup/plugin-node-resolve": "^15.1.0", "@rollup/plugin-replace": "^5.0.2", "@types/fs-extra": "^11.0.1", "@types/node": "^18.16.2", "@types/uuid": "^9.0.2", "@vitejs/plugin-vue": "^4.2.3", "@vitejs/plugin-vue-jsx": "^3.0.1", "@vue/compiler-sfc": "^3.3.4", "adm-zip": "^0.5.10", "cfonts": "^3.2.0", "chalk": "5.3.0", "cross-env": "^7.0.3", "del": "^7.0.0", "dotenv": "^16.3.1", "electron": "^21.3.1", "electron-builder": "^24.6.3", "electron-devtools-vendor": "^1.2.0", "electron_updater_node_cli": "^0.3.3", "electron_updater_node_core": "^0.3.3", "element-plus": "^2.3.8", "esno": "^0.17.0", "extract-zip": "^2.0.1", "fs-extra": "^11.1.1", "javascript-obfuscator": "^4.0.2", "listr2": "^6.6.0", "minimist": "^1.2.8", "pinia": "^2.1.4", "portfinder": "^1.0.32", "rollup-plugin-esbuild": "^5.0.0", "rollup-plugin-obfuscator": "^1.0.3", "sass": "^1.69.4", "tslib": "^2.6.1", "typescript": "^5.1.6", "vite": "^4.4.7", "vue": "^3.3.4", "vue-i18n": "^9.2.2", "vue-router": "^4.2.4"}, "keywords": ["vite", "electron", "vue3", "rollup"]}