{"asar": false, "extraFiles": [], "extraResources": [{"from": "bin", "to": "app/bin", "filter": ["**/*"]}], "publish": [{"provider": "generic", "url": "http://127.0.0.1"}], "afterPack": ".electron-vite/afterPack.js", "beforePack": ".electron-vite/beforePack.js", "productName": "SortingWeigh", "appId": "org.sky.electron-vite-template", "directories": {"output": "build"}, "files": ["dist/electron/**/*"], "dmg": {"contents": [{"x": 410, "y": 150, "type": "link", "path": "/Applications"}, {"x": 130, "y": 150, "type": "file"}]}, "mac": {"icon": "build/icons/icon.icns"}, "win": {"icon": "build/icons/icon.ico"}, "linux": {"target": "deb", "icon": "build/icons"}}