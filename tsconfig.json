{"compileOnSave": false, "compilerOptions": {"resolveJsonModule": true, "baseUrl": ".", "outDir": "./dist/electron/main", "sourceMap": true, "declaration": false, "module": "esnext", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2017", "types": ["vite/client"], "paths": {"@config/*": ["config/*"], "@renderer/*": ["src/renderer/*"], "@main/*": ["src/main/*"], "@root/*": ["./*"]}, "typeRoots": ["node_modules/@types"], "lib": ["es2018", "dom"]}, "include": ["src/**/*", "customTypes/*"], "exclude": ["node_modules"]}