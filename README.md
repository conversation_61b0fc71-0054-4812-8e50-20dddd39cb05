# Sorting-weigh
    Sorting-weigh 是一个基于Vue3、Vite TypeScript、Electron 的屏端运用, 目标是为了解决各类小型TC食材打包的方案。
# Build Setup

```bash
# Clone this repository
$ git clone http://gitlab.wefreshscm.com/freshfood-scm/sorting-weigh.git
# Go into the repository
$ cd Sorting-weigh
# install dependencies
$ npm install
# 安装依赖
npm config edit
# 该命令会打开npm的配置文件，请在空白处添加
# registry=https://registry.npmmirror.com
# ELECTRON_MIRROR=https://npmmirror.com/mirrors/electron/
# ELECTRON_CUSTOM_DIR="{{ version }}"
# ELECTRON_BUILDER_BINARIES_MIRROR=https://npmmirror.com/mirrors/electron-builder-binaries/
# 然后关闭该窗口，重启命令行.
# 使用yarn安装
$ yarn or yarn install
# 启动之后，会在9080端口监听
$ yarn run dev
# build命令在不同系统环境中，需要的的不一样，需要自己根据自身环境进行配置
$ yarn  run build


# 注：版本升级处理
    1. 修改package.json 文件中version 版本号
    2. 执行打包命令 yarn run build  
    3. 打包成功后  修改updateConfig.json 文件中version 版本号、url 地址 以及 exe 文件名称
    4. 把改名后的 exe文件包和 updateConfig.json  给后端同学，发布服务器。
```



```
sorting-weigh
├─ .git
├─ .gitignore
├─ build.json
├─ config
│  └─ index.ts  // 运行 proxy 设置
├─ customTypes  //  枚举  常量 类型
│  ├─ global.d.ts
│  ├─ image.d.ts
│  ├─ Item.d.ts
│  └─ shims-vue.d.ts
├─ env    //  打包环境配置
│  ├─ .env
│  ├─ prod.env
│  └─ sit.env
├─ package.json   
├─ README.md
├─ src
│  ├─ ipc.ts  // electron  Ipc 调用方法
│  ├─ main
│  │  ├─ auto-launch.ts
│  │  ├─ config
│  │  │  ├─ const.ts
│  │  │  ├─ DisableButton.ts
│  │  │  ├─ hotPublish.ts
│  │  │  ├─ menu.ts
│  │  │  ├─ StaticPath.ts
│  │  │  └─ windowsConfig.ts
│  │  ├─ index.ts
│  │  ├─ server  // 本地server 
│  │  │  ├─ index.ts
│  │  │  └─ server.ts
│  │  └─ services  // 服务，文件下载、文件上传、窗体的设置等
│  │     ├─ browserHandle.ts
│  │     ├─ checkupdate.ts
│  │     ├─ downloadFile.ts
│  │     ├─ HotUpdater.ts
│  │     ├─ HotUpdaterTest.ts
│  │     ├─ ipcMain.ts
│  │     ├─ printHandle.ts
│  │     ├─ regeditUtils.ts
│  │     ├─ trayManager.ts
│  │     └─ windowManager.ts
│  └─ renderer
│     ├─ api      // Api  request 地址
│     │  ├─ login.ts
│     │  └─ sorting.ts
│     ├─ App.vue
│     ├─ assets  
│     ├─ components  // 组件
│     │  ├─ common
│     │  │  ├─ SvgIcon.vue
│     │  │  └─ TitleBar.vue
│     │  ├─ keyboard.vue
│     ├─ error.ts
│     ├─ i18n      // 国际化
│     │  ├─ index.ts
│     │  └─ languages
│     │     ├─ en.ts
│     │     └─ zh-cn.ts
│     ├─ index.html
│     ├─ main.ts
│     ├─ permission.ts
│     ├─ public
│     │  ├─ icons
│     │  │  ├─ icon.ico
│     │  │  └─ run_icon.png
│     │  ├─ loader.html
│     │  ├─ tray.html
│     │  └─ trayIcon
│     ├─ router   // 路由
│     │  ├─ constantRouterMap.ts
│     │  └─ index.ts
│     ├─ store
│     │  └─ modules
│     │     └─ template.ts
│     ├─ styles
│     │  ├─ custom-title.scss
│     │  ├─ index.scss
│     │  └─ transition.scss
│     ├─ utils   //工具类
│     │  ├─ ipcRenderer.ts
│     │  ├─ notification.ts
│     │  ├─ performance.ts
│     │  ├─ printTemplate
│     │  ├─ request.ts
│     │  ├─ secret.ts
│     │  ├─ timer.ts
│     │  └─ util.ts
│     └─ views   // 页面代码块
│        ├─ 404.vue
│        ├─ home
│        │  ├─ CustomerSorting.vue
│        │  ├─ Home.vue   // 主界面窗体
│        │  ├─ OrderSorting.vue
│        │  ├─ PrintPage.vue
│        │  └─ ProductSorting.vue
│        ├─ login
│        │  └─ Login.vue  // 登录窗体
│        └─ productSorting
│           ├─ components
│           │  ├─ filterDialog.vue
│           │  ├─ Goods.vue
│           │  ├─ GoodsInfoItem.vue
│           │  ├─ PrinterDialogSelect.vue
│           │  └─ Title.vue
│           ├─ constant.js
│           ├─ filterParams.ts
│           ├─ GoodsInfo.vue
│           └─ index.vue
├─ static
├─ tsconfig.json
├─ updateConfig.json
└─ yarn.lock

```